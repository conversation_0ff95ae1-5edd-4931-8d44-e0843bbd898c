function extractGoMapData(mapData) {
    const result = [];
    for (const [coord, info] of Object.entries(mapData)) {
        // id
        const id = coord;
        // 名称
        const name = info.N || '';
        // 描述
        const desc = info.L || '';
        // 左地图（西）
        const leftName = (info.Y && info.Y.N) ? info.Y.N : '';
        const leftId = (info.Y && info.Y.I) ? info.Y.I : '';
        // 右地图（东）
        const rightName = (info.X && info.X.N) ? info.X.N : '';
        const rightId = (info.X && info.X.I) ? info.X.I : '';
        // 上地图（北）
        const upName = (info.Z && info.Z.N) ? info.Z.N : '';
        const upId = (info.Z && info.Z.I) ? info.Z.I : '';
        // 下地图（南）
        const downName = (info.S && info.S.N) ? info.S.N : '';
        const downId = (info.S && info.S.I) ? info.S.I : '';

        result.push([
            id, name, desc,
            leftName, leftId,
            rightName, rightId,
            upName, upId,
            downName, downId
        ].join('|'));
    }
    return result.join('\n');
} 