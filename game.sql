/*
Navicat MySQL Data Transfer

Source Server         : game
Source Server Version : 50726
Source Host           : localhost:3306
Source Database       : game

Target Server Type    : MYSQL
Target Server Version : 50726
File Encoding         : 65001

Date: 2024-06-14 18:01:49
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for area_entity
-- ----------------------------
DROP TABLE IF EXISTS `area_entity`;
CREATE TABLE `area_entity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of area_entity
-- ----------------------------
INSERT INTO `area_entity` VALUES ('1', '永远的回忆');

-- ----------------------------
-- Table structure for general_entity
-- ----------------------------
DROP TABLE IF EXISTS `general_entity`;
CREATE TABLE `general_entity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  `level` int(11) NOT NULL DEFAULT '1',
  `potential` int(11) NOT NULL DEFAULT '0',
  `attack` int(11) NOT NULL DEFAULT '20',
  `defense` int(11) NOT NULL DEFAULT '40',
  `hp` int(11) NOT NULL DEFAULT '2701',
  `hpNow` int(11) NOT NULL DEFAULT '2701',
  `near` int(11) NOT NULL DEFAULT '0',
  `teamOrder` int(11) NOT NULL DEFAULT '2',
  `sort` int(11) NOT NULL DEFAULT '0',
  `count` int(11) NOT NULL DEFAULT '0',
  `countAll` int(11) NOT NULL DEFAULT '11',
  `cmd1` varchar(255) NOT NULL DEFAULT '快捷',
  `cmd2` varchar(255) NOT NULL DEFAULT '快捷',
  `cmd3` varchar(255) NOT NULL DEFAULT '快捷',
  `cmd4` varchar(255) NOT NULL DEFAULT '快捷',
  `cmd5` varchar(255) NOT NULL DEFAULT '快捷',
  `cmd6` varchar(255) NOT NULL DEFAULT '逃跑',
  `fightStatus` int(11) NOT NULL DEFAULT '0',
  `controlStatus` int(11) NOT NULL DEFAULT '0',
  `attackTargetId` int(11) DEFAULT NULL,
  `userId` int(11) NOT NULL,
  `create_date` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of general_entity
-- ----------------------------

-- ----------------------------
-- Table structure for good_entity
-- ----------------------------
DROP TABLE IF EXISTS `good_entity`;
CREATE TABLE `good_entity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  `desc` varchar(100) NOT NULL DEFAULT '',
  `price` int(11) NOT NULL,
  `type` int(11) NOT NULL,
  `subType` int(11) NOT NULL,
  `addhp` int(11) NOT NULL,
  `addatk` int(11) NOT NULL,
  `adddef` int(11) NOT NULL,
  `weight` int(11) NOT NULL DEFAULT '1',
  `useLevel` int(11) NOT NULL DEFAULT '1',
  `roleUse` int(11) NOT NULL DEFAULT '0',
  `generalUse` int(11) NOT NULL DEFAULT '0',
  `soldierUse` int(11) NOT NULL DEFAULT '0',
  `bind` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `IDX_9ff9fb69e044e3379aa8c2d11e` (`type`),
  KEY `IDX_3ec5de067fb3b99cf1e85e98ea` (`subType`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of good_entity
-- ----------------------------
INSERT INTO `good_entity` VALUES ('1', '单刀', '', '60', '2', '21', '0', '6', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('2', '长剑', '', '60', '2', '0', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('3', '铁锤', '', '60', '2', '0', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('4', '木棍', '', '60', '2', '0', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('5', '木枪', '', '60', '2', '0', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('6', '铁斧', '', '60', '2', '0', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('7', '单刀（士兵）', '', '60', '4', '21', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('8', '长剑（士兵）', '', '60', '4', '22', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('9', '铁锤（士兵）', '', '60', '4', '25', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('10', '木棍（士兵）', '', '60', '4', '23', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('11', '木枪（士兵）', '', '60', '4', '26', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('12', '布衣', '', '60', '3', '32', '0', '0', '6', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('13', '布帽', '', '60', '3', '0', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('14', '石子项链', '', '60', '3', '0', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('15', '布手套', '', '60', '3', '0', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('16', '草织戒指', '', '60', '3', '0', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('17', '石子项链（士兵）', '', '60', '5', '0', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('18', '布手套（士兵）', '', '60', '5', '0', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('19', '草织戒指（士兵）', '', '60', '5', '0', '0', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('20', '金创药', '', '30', '1', '0', '2300', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('21', '行军散', '', '60', '1', '0', '3300', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('22', '造化丹', '', '120', '1', '0', '500', '0', '0', '1', '1', '0', '0', '0', '1');
INSERT INTO `good_entity` VALUES ('23', '活络丸', '', '120', '1', '0', '7500', '0', '0', '1', '1', '0', '0', '0', '1');

-- ----------------------------
-- Table structure for logs_entity
-- ----------------------------
DROP TABLE IF EXISTS `logs_entity`;
CREATE TABLE `logs_entity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` varchar(255) NOT NULL,
  `name` varchar(20) NOT NULL,
  `food` int(11) NOT NULL DEFAULT '0',
  `wood` int(11) NOT NULL DEFAULT '0',
  `stone` int(11) NOT NULL DEFAULT '0',
  `iron` int(11) NOT NULL DEFAULT '0',
  `gold` int(11) NOT NULL DEFAULT '0',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `potential` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=142 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of logs_entity
-- ----------------------------
INSERT INTO `logs_entity` VALUES ('4', '3', '建造庄院', '50', '50', '25', '25', '50', '2024-05-11 17:49:34', '0');
INSERT INTO `logs_entity` VALUES ('5', '3', '任务:1奖励', '50', '50', '0', '0', '0', '2024-05-20 16:26:26', '0');
INSERT INTO `logs_entity` VALUES ('6', '3', '任务:1奖励', '0', '0', '0', '0', '0', '2024-05-20 17:19:27', '0');
INSERT INTO `logs_entity` VALUES ('7', '3', '任务:1奖励', '0', '0', '0', '0', '0', '2024-05-20 17:26:27', '0');
INSERT INTO `logs_entity` VALUES ('8', '3', '任务:2奖励', '0', '0', '0', '0', '0', '2024-05-20 17:31:45', '0');
INSERT INTO `logs_entity` VALUES ('9', '3', '任务:3奖励', '0', '0', '0', '0', '50', '2024-05-20 17:31:51', '0');
INSERT INTO `logs_entity` VALUES ('10', '3', '任务:1奖励', '0', '0', '0', '0', '0', '2024-05-20 17:39:54', '0');
INSERT INTO `logs_entity` VALUES ('11', '3', '任务:2奖励', '0', '0', '0', '0', '0', '2024-05-20 17:39:57', '0');
INSERT INTO `logs_entity` VALUES ('12', '3', '任务:3奖励', '0', '0', '0', '0', '50', '2024-05-20 17:40:01', '8');
INSERT INTO `logs_entity` VALUES ('13', '3', '任务:4奖励', '0', '0', '0', '0', '0', '2024-05-23 17:41:44', '0');
INSERT INTO `logs_entity` VALUES ('14', '3', '任务:1奖励', '0', '0', '0', '0', '0', '2024-05-27 16:55:05', '0');
INSERT INTO `logs_entity` VALUES ('15', '3', '任务:2奖励', '0', '0', '0', '0', '0', '2024-05-27 16:55:07', '0');
INSERT INTO `logs_entity` VALUES ('16', '3', '任务:3奖励', '0', '0', '0', '0', '50', '2024-05-27 16:55:08', '8');
INSERT INTO `logs_entity` VALUES ('17', '3', '任务:4奖励', '0', '0', '0', '0', '0', '2024-05-27 16:56:21', '0');
INSERT INTO `logs_entity` VALUES ('18', '3', '任务:1奖励', '0', '0', '0', '0', '0', '2024-05-27 16:56:56', '0');
INSERT INTO `logs_entity` VALUES ('19', '3', '任务:2奖励', '0', '0', '0', '0', '0', '2024-05-27 16:56:57', '0');
INSERT INTO `logs_entity` VALUES ('20', '3', '任务:3奖励', '0', '0', '0', '0', '50', '2024-05-27 16:56:58', '8');
INSERT INTO `logs_entity` VALUES ('21', '3', '任务:4奖励', '0', '0', '0', '0', '0', '2024-05-27 16:57:03', '0');
INSERT INTO `logs_entity` VALUES ('22', '3', '任务:1奖励', '0', '0', '0', '0', '0', '2024-05-27 16:58:39', '0');
INSERT INTO `logs_entity` VALUES ('23', '3', '任务:2奖励', '0', '0', '0', '0', '0', '2024-05-27 16:58:40', '0');
INSERT INTO `logs_entity` VALUES ('24', '3', '任务:3奖励', '0', '0', '0', '0', '50', '2024-05-27 16:58:41', '8');
INSERT INTO `logs_entity` VALUES ('25', '3', '任务:4奖励', '0', '0', '0', '0', '0', '2024-05-27 16:58:43', '0');
INSERT INTO `logs_entity` VALUES ('26', '3', '任务:7奖励', '400', '400', '400', '400', '50', '2024-05-29 12:01:56', '10');
INSERT INTO `logs_entity` VALUES ('27', '3', '任务:10奖励', '0', '0', '0', '0', '50', '2024-05-29 12:02:10', '40');
INSERT INTO `logs_entity` VALUES ('28', '3', '任务:1奖励', '0', '0', '0', '0', '0', '2024-05-29 15:55:36', '0');
INSERT INTO `logs_entity` VALUES ('29', '3', '任务:2奖励', '0', '0', '0', '0', '0', '2024-05-29 15:55:47', '0');
INSERT INTO `logs_entity` VALUES ('30', '3', '任务:3奖励', '0', '0', '0', '0', '50', '2024-05-29 15:55:49', '8');
INSERT INTO `logs_entity` VALUES ('31', '3', '任务:4奖励', '0', '0', '0', '0', '0', '2024-05-29 15:56:16', '0');
INSERT INTO `logs_entity` VALUES ('32', '3', '任务:7奖励', '400', '400', '400', '400', '50', '2024-05-29 15:56:20', '10');
INSERT INTO `logs_entity` VALUES ('33', '3', '建造庄院', '50', '50', '25', '25', '50', '2024-05-29 16:14:46', '0');
INSERT INTO `logs_entity` VALUES ('34', '3', '任务:10奖励', '0', '0', '0', '0', '50', '2024-05-29 16:16:07', '40');
INSERT INTO `logs_entity` VALUES ('35', '3', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-10 17:41:31', '0');
INSERT INTO `logs_entity` VALUES ('36', '3', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-10 17:46:10', '0');
INSERT INTO `logs_entity` VALUES ('37', '3', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-10 17:47:23', '0');
INSERT INTO `logs_entity` VALUES ('38', '3', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-10 17:53:00', '0');
INSERT INTO `logs_entity` VALUES ('39', '3', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-10 17:53:04', '0');
INSERT INTO `logs_entity` VALUES ('40', '3', '购买物品:长剑', '0', '0', '0', '0', '60', '2024-06-10 17:53:15', '0');
INSERT INTO `logs_entity` VALUES ('41', '3', '购买物品:长剑', '0', '0', '0', '0', '60', '2024-06-10 17:59:15', '0');
INSERT INTO `logs_entity` VALUES ('42', '3', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-10 17:59:31', '0');
INSERT INTO `logs_entity` VALUES ('43', '3', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-10 17:59:45', '0');
INSERT INTO `logs_entity` VALUES ('44', '3', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-10 18:00:46', '0');
INSERT INTO `logs_entity` VALUES ('45', '3', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-10 18:00:53', '0');
INSERT INTO `logs_entity` VALUES ('46', '3', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-10 18:01:01', '0');
INSERT INTO `logs_entity` VALUES ('47', '3', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-10 18:01:33', '0');
INSERT INTO `logs_entity` VALUES ('48', '3', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-11 09:46:16', '0');
INSERT INTO `logs_entity` VALUES ('49', '3', '购买物品:长剑', '0', '0', '0', '0', '60', '2024-06-11 09:46:23', '0');
INSERT INTO `logs_entity` VALUES ('50', '3', '购买物品:长剑', '0', '0', '0', '0', '60', '2024-06-11 09:46:31', '0');
INSERT INTO `logs_entity` VALUES ('51', '3', '购买物品:铁锤', '0', '0', '0', '0', '60', '2024-06-12 10:23:03', '0');
INSERT INTO `logs_entity` VALUES ('52', '3', '购买物品:木棍', '0', '0', '0', '0', '60', '2024-06-12 10:23:06', '0');
INSERT INTO `logs_entity` VALUES ('53', '3', '购买物品:单刀（士兵）', '0', '0', '0', '0', '60', '2024-06-12 10:32:22', '0');
INSERT INTO `logs_entity` VALUES ('54', '3', '购买物品:长剑（士兵）', '0', '0', '0', '0', '120', '2024-06-12 10:32:27', '0');
INSERT INTO `logs_entity` VALUES ('55', '3', '购买物品:木枪（士兵）', '0', '0', '0', '0', '60', '2024-06-12 10:58:19', '0');
INSERT INTO `logs_entity` VALUES ('56', '3', '购买物品:木棍（士兵）', '0', '0', '0', '0', '60', '2024-06-12 10:58:22', '0');
INSERT INTO `logs_entity` VALUES ('57', '3', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-12 11:40:56', '0');
INSERT INTO `logs_entity` VALUES ('58', '3', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-13 17:06:33', '0');
INSERT INTO `logs_entity` VALUES ('59', '3', '购买物品:单刀', '0', '0', '0', '0', '300', '2024-06-13 17:13:02', '0');
INSERT INTO `logs_entity` VALUES ('60', '3', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-13 17:13:25', '0');
INSERT INTO `logs_entity` VALUES ('61', '3', '出售物品:长剑', '0', '0', '0', '0', '48', '2024-06-13 17:15:10', '0');
INSERT INTO `logs_entity` VALUES ('62', '3', '出售物品:长剑', '0', '0', '0', '0', '48', '2024-06-13 17:17:43', '0');
INSERT INTO `logs_entity` VALUES ('63', '3', '出售物品:木棍（士兵）', '0', '0', '0', '0', '48', '2024-06-14 10:37:10', '0');
INSERT INTO `logs_entity` VALUES ('64', '3', '出售物品:木枪（士兵）', '0', '0', '0', '0', '48', '2024-06-14 10:37:22', '0');
INSERT INTO `logs_entity` VALUES ('65', '3', '出售物品:长剑（士兵）', '0', '0', '0', '0', '48', '2024-06-14 10:41:37', '0');
INSERT INTO `logs_entity` VALUES ('66', '3', '出售物品:长剑（士兵）', '0', '0', '0', '0', '48', '2024-06-14 10:57:57', '0');
INSERT INTO `logs_entity` VALUES ('67', '4', '任务:1奖励', '0', '0', '0', '0', '0', '2024-06-14 11:32:23', '0');
INSERT INTO `logs_entity` VALUES ('68', '4', '任务:2奖励', '0', '0', '0', '0', '0', '2024-06-14 11:32:25', '0');
INSERT INTO `logs_entity` VALUES ('69', '4', '任务:3奖励', '0', '0', '0', '0', '50', '2024-06-14 11:32:26', '8');
INSERT INTO `logs_entity` VALUES ('70', '4', '任务:4奖励', '0', '0', '0', '0', '0', '2024-06-14 11:32:34', '0');
INSERT INTO `logs_entity` VALUES ('71', '4', '任务:7奖励', '400', '400', '400', '400', '50', '2024-06-14 11:32:37', '10');
INSERT INTO `logs_entity` VALUES ('72', '4', '建造庄院', '50', '50', '25', '25', '50', '2024-06-14 11:32:50', '0');
INSERT INTO `logs_entity` VALUES ('73', '4', '任务:10奖励', '0', '0', '0', '0', '50', '2024-06-14 11:34:03', '40');
INSERT INTO `logs_entity` VALUES ('74', '4', '购买物品:长剑', '0', '0', '0', '0', '60', '2024-06-14 11:35:36', '0');
INSERT INTO `logs_entity` VALUES ('75', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 11:35:44', '0');
INSERT INTO `logs_entity` VALUES ('76', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 11:35:57', '0');
INSERT INTO `logs_entity` VALUES ('77', '4', '出售物品:长剑', '0', '0', '0', '0', '48', '2024-06-14 15:16:02', '0');
INSERT INTO `logs_entity` VALUES ('78', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 15:18:43', '0');
INSERT INTO `logs_entity` VALUES ('79', '4', '购买物品:长剑', '0', '0', '0', '0', '60', '2024-06-14 15:18:46', '0');
INSERT INTO `logs_entity` VALUES ('80', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 15:18:58', '0');
INSERT INTO `logs_entity` VALUES ('81', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 15:19:06', '0');
INSERT INTO `logs_entity` VALUES ('82', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 15:19:50', '0');
INSERT INTO `logs_entity` VALUES ('83', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 15:19:59', '0');
INSERT INTO `logs_entity` VALUES ('84', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 15:20:12', '0');
INSERT INTO `logs_entity` VALUES ('85', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 15:20:18', '0');
INSERT INTO `logs_entity` VALUES ('86', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 15:21:56', '0');
INSERT INTO `logs_entity` VALUES ('87', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 15:22:06', '0');
INSERT INTO `logs_entity` VALUES ('88', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 15:25:58', '0');
INSERT INTO `logs_entity` VALUES ('89', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 15:26:03', '0');
INSERT INTO `logs_entity` VALUES ('90', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 15:29:26', '0');
INSERT INTO `logs_entity` VALUES ('91', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 15:29:27', '0');
INSERT INTO `logs_entity` VALUES ('92', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 15:29:28', '0');
INSERT INTO `logs_entity` VALUES ('93', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 15:29:37', '0');
INSERT INTO `logs_entity` VALUES ('94', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 15:29:40', '0');
INSERT INTO `logs_entity` VALUES ('95', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 15:29:43', '0');
INSERT INTO `logs_entity` VALUES ('96', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 15:29:50', '0');
INSERT INTO `logs_entity` VALUES ('97', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 15:29:56', '0');
INSERT INTO `logs_entity` VALUES ('98', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 15:30:17', '0');
INSERT INTO `logs_entity` VALUES ('99', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 15:30:37', '0');
INSERT INTO `logs_entity` VALUES ('100', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 15:32:00', '0');
INSERT INTO `logs_entity` VALUES ('101', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 15:32:11', '0');
INSERT INTO `logs_entity` VALUES ('102', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 15:36:49', '0');
INSERT INTO `logs_entity` VALUES ('103', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 15:36:50', '0');
INSERT INTO `logs_entity` VALUES ('104', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 15:36:51', '0');
INSERT INTO `logs_entity` VALUES ('105', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 15:36:52', '0');
INSERT INTO `logs_entity` VALUES ('106', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 16:18:17', '0');
INSERT INTO `logs_entity` VALUES ('107', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 16:19:36', '0');
INSERT INTO `logs_entity` VALUES ('108', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 16:20:05', '0');
INSERT INTO `logs_entity` VALUES ('109', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 16:20:40', '0');
INSERT INTO `logs_entity` VALUES ('110', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:21:49', '0');
INSERT INTO `logs_entity` VALUES ('111', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:22:43', '0');
INSERT INTO `logs_entity` VALUES ('112', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:23:34', '0');
INSERT INTO `logs_entity` VALUES ('113', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 16:24:05', '0');
INSERT INTO `logs_entity` VALUES ('114', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 16:25:26', '0');
INSERT INTO `logs_entity` VALUES ('115', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 16:27:19', '0');
INSERT INTO `logs_entity` VALUES ('116', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:29:26', '0');
INSERT INTO `logs_entity` VALUES ('117', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 16:29:32', '0');
INSERT INTO `logs_entity` VALUES ('118', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:31:10', '0');
INSERT INTO `logs_entity` VALUES ('119', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 16:31:16', '0');
INSERT INTO `logs_entity` VALUES ('120', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:34:38', '0');
INSERT INTO `logs_entity` VALUES ('121', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:35:21', '0');
INSERT INTO `logs_entity` VALUES ('122', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:35:43', '0');
INSERT INTO `logs_entity` VALUES ('123', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:35:51', '0');
INSERT INTO `logs_entity` VALUES ('124', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 16:35:57', '0');
INSERT INTO `logs_entity` VALUES ('125', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 16:35:59', '0');
INSERT INTO `logs_entity` VALUES ('126', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 16:36:07', '0');
INSERT INTO `logs_entity` VALUES ('127', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 16:36:09', '0');
INSERT INTO `logs_entity` VALUES ('128', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:36:29', '0');
INSERT INTO `logs_entity` VALUES ('129', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:36:31', '0');
INSERT INTO `logs_entity` VALUES ('130', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:37:03', '0');
INSERT INTO `logs_entity` VALUES ('131', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:37:05', '0');
INSERT INTO `logs_entity` VALUES ('132', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:37:39', '0');
INSERT INTO `logs_entity` VALUES ('133', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:37:59', '0');
INSERT INTO `logs_entity` VALUES ('134', '4', '出售物品:单刀', '0', '0', '0', '0', '48', '2024-06-14 16:38:09', '0');
INSERT INTO `logs_entity` VALUES ('135', '4', '购买物品:单刀', '0', '0', '0', '0', '60', '2024-06-14 16:42:25', '0');
INSERT INTO `logs_entity` VALUES ('136', '4', '购买物品:长剑', '0', '0', '0', '0', '60', '2024-06-14 16:45:11', '0');
INSERT INTO `logs_entity` VALUES ('137', '4', '任务:15奖励', '900', '900', '900', '900', '50', '2024-06-14 16:45:15', '40');
INSERT INTO `logs_entity` VALUES ('138', '4', '购买物品:布帽', '0', '0', '0', '0', '60', '2024-06-14 16:48:25', '0');
INSERT INTO `logs_entity` VALUES ('139', '4', '任务:18奖励', '0', '0', '0', '0', '65', '2024-06-14 16:49:21', '60');
INSERT INTO `logs_entity` VALUES ('140', '4', '购买物品:金创药', '0', '0', '0', '0', '30', '2024-06-14 16:56:08', '0');
INSERT INTO `logs_entity` VALUES ('141', '4', '购买物品:金创药', '0', '0', '0', '0', '270', '2024-06-14 16:56:20', '0');

-- ----------------------------
-- Table structure for manor_entity
-- ----------------------------
DROP TABLE IF EXISTS `manor_entity`;
CREATE TABLE `manor_entity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(11) NOT NULL,
  `level` int(11) NOT NULL DEFAULT '0',
  `userId` int(11) NOT NULL,
  `create_date` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `status` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of manor_entity
-- ----------------------------
INSERT INTO `manor_entity` VALUES ('6', '1', '1', '4', '2024-06-14 11:32:50.919070', '2');

-- ----------------------------
-- Table structure for manor_event_entity
-- ----------------------------
DROP TABLE IF EXISTS `manor_event_entity`;
CREATE TABLE `manor_event_entity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(11) NOT NULL,
  `buildId` int(11) NOT NULL,
  `completionTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `userId` int(11) NOT NULL,
  `num1` int(11) NOT NULL DEFAULT '0',
  `num2` int(11) NOT NULL DEFAULT '0',
  `num3` int(11) NOT NULL DEFAULT '0',
  `create_date` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `status` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `IDX_0df90000d75c08c5e4afb20fe2` (`buildId`),
  KEY `IDX_2575644be532d4ee2995c629ab` (`userId`),
  KEY `IDX_720a0db17febbd41d3ae5dc570` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of manor_event_entity
-- ----------------------------
INSERT INTO `manor_event_entity` VALUES ('6', '1', '6', '2024-06-14 11:33:40', '4', '1', '0', '0', '2024-06-14 11:32:50.923707', '2');

-- ----------------------------
-- Table structure for maps_entity
-- ----------------------------
DROP TABLE IF EXISTS `maps_entity`;
CREATE TABLE `maps_entity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `desc` varchar(100) NOT NULL,
  `topId` int(11) NOT NULL,
  `bottomId` int(11) NOT NULL,
  `leftId` int(11) NOT NULL,
  `rightId` int(11) NOT NULL,
  `top` varchar(255) DEFAULT NULL,
  `bottom` varchar(255) DEFAULT NULL,
  `left` varchar(255) DEFAULT NULL,
  `right` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of maps_entity
-- ----------------------------
INSERT INTO `maps_entity` VALUES ('1', '庄院广场', '欢迎来到我的世界', '5', '11', '3', '2', null, null, null, null);
INSERT INTO `maps_entity` VALUES ('2', '练武场', '', '0', '0', '1', '0', null, null, null, null);
INSERT INTO `maps_entity` VALUES ('3', '管家房', '', '0', '0', '0', '1', null, null, null, null);
INSERT INTO `maps_entity` VALUES ('4', '卧室', '', '0', '0', '0', '5', null, null, null, null);
INSERT INTO `maps_entity` VALUES ('5', '后院', '', '8', '1', '4', '6', null, null, null, null);
INSERT INTO `maps_entity` VALUES ('6', '家丁房', '', '0', '0', '5', '0', null, null, null, null);
INSERT INTO `maps_entity` VALUES ('7', '石头山', '', '0', '0', '8', '0', null, null, null, null);
INSERT INTO `maps_entity` VALUES ('8', '良田', '', '10', '5', '9', '7', null, null, null, null);
INSERT INTO `maps_entity` VALUES ('9', '铁石山', '', '0', '0', '0', '8', null, null, null, null);
INSERT INTO `maps_entity` VALUES ('10', '林场', '', '0', '8', '0', '0', null, null, null, null);
INSERT INTO `maps_entity` VALUES ('11', '庄院大门', '这里是你家的庄院，也颇有些气势', '1', '0', '0', '0', null, null, null, null);

-- ----------------------------
-- Table structure for npc_entity
-- ----------------------------
DROP TABLE IF EXISTS `npc_entity`;
CREATE TABLE `npc_entity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  `desc` varchar(100) NOT NULL,
  `gpsId` int(11) NOT NULL,
  `canAttack` int(11) NOT NULL DEFAULT '0',
  `canBuy` int(11) NOT NULL DEFAULT '0',
  `directAttack` int(11) NOT NULL DEFAULT '0',
  `attack` int(11) NOT NULL DEFAULT '0',
  `defense` int(11) NOT NULL DEFAULT '0',
  `hp` int(11) NOT NULL DEFAULT '0',
  `intervalTime` int(11) NOT NULL DEFAULT '0',
  `hpNow` int(11) NOT NULL DEFAULT '0',
  `teamOrder` int(11) NOT NULL DEFAULT '2',
  PRIMARY KEY (`id`),
  KEY `IDX_126dbdc6febacdd88a075cc066` (`gpsId`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of npc_entity
-- ----------------------------
INSERT INTO `npc_entity` VALUES ('1', '新人大使', 'Hi，菜鸟你好！', '1', '0', '0', '0', '0', '0', '0', '0', '0', '2');
INSERT INTO `npc_entity` VALUES ('2', '武器', '他是你的庄园收留的一个卖武器的', '1', '0', '1', '0', '0', '0', '0', '0', '0', '2');
INSERT INTO `npc_entity` VALUES ('3', '防具', '他是你的庄园收留的一个卖防具的', '1', '0', '1', '0', '0', '0', '0', '0', '0', '2');
INSERT INTO `npc_entity` VALUES ('4', '药品', '他是你的庄园收留的一个卖药品的', '1', '0', '1', '0', '0', '0', '0', '0', '0', '2');
INSERT INTO `npc_entity` VALUES ('5', '田武师', '我是田武师', '2', '1', '0', '0', '0', '0', '0', '0', '0', '2');
INSERT INTO `npc_entity` VALUES ('6', '庄园家丁', '庄院里的家丁，很有些功夫。', '2', '1', '0', '0', '0', '0', '0', '0', '0', '2');
INSERT INTO `npc_entity` VALUES ('7', '庄园家丁', '庄院里的家丁，很有些功夫。', '2', '1', '0', '0', '0', '0', '0', '0', '0', '2');
INSERT INTO `npc_entity` VALUES ('8', '田鼠', '', '8', '0', '0', '1', '20', '40', '2701', '0', '2701', '2');
INSERT INTO `npc_entity` VALUES ('9', '田鼠', '', '8', '0', '0', '1', '20', '40', '2701', '0', '2701', '2');
INSERT INTO `npc_entity` VALUES ('10', '田鼠', '', '8', '0', '0', '1', '20', '40', '2701', '0', '2701', '2');

-- ----------------------------
-- Table structure for npc_entity_goods_good_entity
-- ----------------------------
DROP TABLE IF EXISTS `npc_entity_goods_good_entity`;
CREATE TABLE `npc_entity_goods_good_entity` (
  `npcEntityId` int(11) NOT NULL,
  `goodEntityId` int(11) NOT NULL,
  PRIMARY KEY (`npcEntityId`,`goodEntityId`),
  KEY `IDX_d6da84c5cb08aa30e3e285beb8` (`npcEntityId`),
  KEY `IDX_d04aee018861f215e0a6be2115` (`goodEntityId`),
  CONSTRAINT `FK_d04aee018861f215e0a6be21158` FOREIGN KEY (`goodEntityId`) REFERENCES `good_entity` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_d6da84c5cb08aa30e3e285beb8a` FOREIGN KEY (`npcEntityId`) REFERENCES `npc_entity` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of npc_entity_goods_good_entity
-- ----------------------------
INSERT INTO `npc_entity_goods_good_entity` VALUES ('2', '1');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('2', '2');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('2', '3');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('2', '4');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('2', '5');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('2', '6');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('2', '7');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('2', '8');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('2', '9');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('2', '10');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('2', '11');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('3', '12');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('3', '13');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('3', '14');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('3', '15');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('3', '16');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('3', '17');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('3', '18');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('3', '19');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('4', '20');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('4', '21');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('4', '22');
INSERT INTO `npc_entity_goods_good_entity` VALUES ('4', '23');

-- ----------------------------
-- Table structure for person_goods_entity
-- ----------------------------
DROP TABLE IF EXISTS `person_goods_entity`;
CREATE TABLE `person_goods_entity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `count` int(11) NOT NULL,
  `userId` int(11) NOT NULL,
  `goodId` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_f9d71f69a8e624cc51c0d3d9a5` (`userId`),
  KEY `FK_292beda8230b683578beab4a72f` (`goodId`),
  CONSTRAINT `FK_292beda8230b683578beab4a72f` FOREIGN KEY (`goodId`) REFERENCES `good_entity` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of person_goods_entity
-- ----------------------------
INSERT INTO `person_goods_entity` VALUES ('39', '6', '4', '1');
INSERT INTO `person_goods_entity` VALUES ('40', '1', '4', '2');
INSERT INTO `person_goods_entity` VALUES ('41', '1', '4', '13');
INSERT INTO `person_goods_entity` VALUES ('42', '10', '4', '20');

-- ----------------------------
-- Table structure for role_entity
-- ----------------------------
DROP TABLE IF EXISTS `role_entity`;
CREATE TABLE `role_entity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  `gender` int(11) NOT NULL DEFAULT '1',
  `lvl` int(11) NOT NULL DEFAULT '1',
  `gpsId` int(11) NOT NULL DEFAULT '1',
  `taskId` int(11) NOT NULL DEFAULT '1',
  `showTaskId` int(11) NOT NULL DEFAULT '0',
  `userId` int(11) NOT NULL,
  `areaId` int(11) NOT NULL,
  `sid` varchar(32) DEFAULT NULL,
  `nowWeight` int(11) DEFAULT '0',
  `count` int(11) NOT NULL DEFAULT '5',
  `office` int(11) NOT NULL DEFAULT '0',
  `potential` int(11) NOT NULL DEFAULT '0',
  `soul` int(11) NOT NULL DEFAULT '0',
  `prestige` int(11) NOT NULL DEFAULT '0',
  `food` int(11) NOT NULL DEFAULT '1500',
  `wood` int(11) NOT NULL DEFAULT '1500',
  `stone` int(11) NOT NULL DEFAULT '1500',
  `iron` int(11) NOT NULL DEFAULT '1500',
  `maxWeight` int(11) NOT NULL DEFAULT '85',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `gold` int(11) NOT NULL DEFAULT '1550',
  PRIMARY KEY (`id`),
  KEY `IDX_6b7dbf5da4f72695f2585774f4` (`userId`),
  KEY `IDX_95923760614f90fc689a58626d` (`areaId`),
  KEY `IDX_61db0b4faa9a193b713c7f952e` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of role_entity
-- ----------------------------
INSERT INTO `role_entity` VALUES ('4', '我是GM', '0', '1', '2', '21', '0', '4', '1', '53c42c1c351d71b18e7bf0975060b2f7', '18', '5', '0', '158', '0', '0', '2750', '2750', '2775', '2775', '85', '2024-06-14 11:29:02', '2024-06-14 17:41:27', '649');

-- ----------------------------
-- Table structure for skill_entity
-- ----------------------------
DROP TABLE IF EXISTS `skill_entity`;
CREATE TABLE `skill_entity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  `damage` int(11) NOT NULL DEFAULT '2',
  `distance` int(11) NOT NULL DEFAULT '1',
  `damageNum` int(11) NOT NULL DEFAULT '1',
  `weaponType` int(11) NOT NULL DEFAULT '0',
  `type` int(11) NOT NULL DEFAULT '1',
  `interval` int(11) NOT NULL DEFAULT '0',
  `isDefault` int(11) NOT NULL DEFAULT '0',
  `create_date` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `generalId` int(11) DEFAULT NULL,
  `npcId` int(11) DEFAULT NULL,
  `soldierId` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK_856a8fff4f48267b0fad8ab4e4f` (`generalId`),
  KEY `FK_7d86a0e5fdd4e685633e691dff9` (`npcId`),
  KEY `FK_ad49f80cb091512f43e7a9989d8` (`soldierId`),
  CONSTRAINT `FK_7d86a0e5fdd4e685633e691dff9` FOREIGN KEY (`npcId`) REFERENCES `npc_entity` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_856a8fff4f48267b0fad8ab4e4f` FOREIGN KEY (`generalId`) REFERENCES `general_entity` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_ad49f80cb091512f43e7a9989d8` FOREIGN KEY (`soldierId`) REFERENCES `soldier_entity` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of skill_entity
-- ----------------------------

-- ----------------------------
-- Table structure for soldier_entity
-- ----------------------------
DROP TABLE IF EXISTS `soldier_entity`;
CREATE TABLE `soldier_entity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL,
  `level` int(11) NOT NULL DEFAULT '1',
  `attack` int(11) NOT NULL DEFAULT '20',
  `defense` int(11) NOT NULL DEFAULT '20',
  `hp` int(11) NOT NULL DEFAULT '520',
  `teamOrder` int(11) NOT NULL DEFAULT '1',
  `population` int(11) NOT NULL DEFAULT '1',
  `count` int(11) NOT NULL DEFAULT '0',
  `lastDamage` int(11) NOT NULL DEFAULT '0',
  `type` varchar(20) NOT NULL DEFAULT 'bubing',
  `userId` int(11) NOT NULL,
  `pipId` int(11) NOT NULL,
  `create_date` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of soldier_entity
-- ----------------------------

-- ----------------------------
-- Table structure for task_entity
-- ----------------------------
DROP TABLE IF EXISTS `task_entity`;
CREATE TABLE `task_entity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content` text NOT NULL,
  `type` int(11) NOT NULL DEFAULT '0',
  `npcId` int(11) NOT NULL,
  `gold` int(11) NOT NULL,
  `potential` int(11) NOT NULL,
  `food` int(11) NOT NULL,
  `wood` int(11) NOT NULL,
  `stone` int(11) NOT NULL,
  `iron` int(11) NOT NULL,
  `condition` int(11) NOT NULL DEFAULT '0',
  `gpsId` int(11) NOT NULL,
  `taskIdPrev` int(11) NOT NULL,
  `taskIdNext` int(11) NOT NULL,
  `goodsId` int(11) NOT NULL,
  `goodsNum` int(11) NOT NULL,
  `rewardId` int(11) NOT NULL,
  `rewardNum` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of task_entity
-- ----------------------------
INSERT INTO `task_entity` VALUES ('1', '“name……该起床了…”<br/>\r\n你朦朦胧胧揉着困倦的双眼。原来是你的贴身丫环小雨轻轻的叫醒了你。<br/>\r\n小雨：“name……刚才田武师在找你，说有要事请name去商量呢……”<br/>\r\n你：“哦……这田武师可真麻烦……”<br/>\r\n小雨温柔的替你穿好衣裳……一张俏脸左右打量了你好久，含羞道：“好了，name，穿戴整齐了，您可以去了……”<br/>\r\n你望着小雨可爱的小脸，笑道：“还是小雨最关心我了……我都不舍得离开你半步了……”<br/>\r\n小雨顿时羞得双脸通红，道：“name最坏了，人家不理你了！赶紧去吧……田武师该着急了……”<br/>', '0', '0', '0', '0', '0', '0', '0', '0', '0', '1', '0', '2', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('2', '你迷迷糊糊来到庄院广场见到了田武师，嘟噜道：“本name想睡个懒觉都不成，你这师傅也太抠门了吧！”<br/>\r\n田武师：“name！小人不敢…不是我抠门…老主人在世时，吩咐小的一定要将name培养成一个万人之上的雄才，小人时刻不敢忘记老主人的托付，发誓一定要让name出人头地，可不只是掌管我们这个小小的庄院而已！”<br/>', '0', '0', '0', '0', '0', '0', '0', '0', '0', '1', '0', '3', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('3', '你：“你可真是奇怪，做个万人之上的人就是那么容易的吗！现在世道那么乱，一不小心就给人砍了！”<br/>\r\n田武师：“name不必担忧，小人正想跟你说说这方面的事情…这里谈话不是很方便，name到练武场来找小人吧，小人自和你详说！”<br/>\r\n(到练武场找田武师对话，从庄院广场往右走一步即可达到练武场)<br/>\r\n银两+50<br/>\r\n潜能+8<br/>\r\n<br/>', '0', '0', '50', '8', '0', '0', '0', '0', '0', '1', '0', '4', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('4', '<font color=\"red\">小雨妹妹</font>:请您注意，人物前面有！号的表示可领取任务，请一定要点击；有？号图标的表示此人物给你的任务未完成；有√图标的表示任务完成，请立即点击。<br/>', '0', '0', '0', '0', '0', '0', '0', '0', '0', '2', '0', '5', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('5', '你：“说吧，到底怎么样才能做个万人之上的人？”<br/>\r\n田武师：“name，当今世道动荡不安，皇帝、朝廷腐朽无能，正是咱们江湖好汉立一番事业的大好时机啊！要是运气好的话，name你就是捞个皇帝做做又有什么不可以？！”<br/>\r\n你：“你说得倒轻巧！你以为龙椅长了两只脚，会自己走到我屁股下吗？就凭我家现在这个小庄院，大大小小加起来也不过10来号人，怎么跟人比？”<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '0', '0', '5', '6', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('6', '田武师：“name不必泄气，我在江湖上有些朋友的，你只要按我的话来做，保管你有改朝换代的可能！<br/>\r\n你：“倒忘了，你也是能人异士了。我一直很奇怪，你为什么对我们家如此忠心耿耿，死心塌地？”<br/>\r\n田武师：“我这条命是老庄主救的，我这一生，就是用来报效老庄主的一命之恩，现在老庄主仙逝，我一定会照顾好name你的。好了，废话少说了，我们一步步开始吧。name，我们庄院的庄院广场有那么大块的地方，我看现在可以建一些建筑在上面了，多建些有用的建筑，以后我们庄院也就越来越强大了！”<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '0', '0', '5', '7', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('7', '你：“这个倒合我意，但首先建什么建筑好呢？”<br/>\r\n田武师：“您可以先建一个决议大厅，建好这个决议大厅后，就可以建其他各种各样的建筑了，到时name你再根据任务的提示，对各种建筑的功能做一下了解，就可以自己决定什么建筑多建了。<br/>\r\n银两+50<br/>\r\n潜能+10<br/>\r\n粮草+400<br/>\r\n木材+400<br/>\r\n石料+400<br/>\r\n生铁+400<br/>', '0', '5', '50', '10', '400', '400', '400', '400', '0', '0', '6', '8', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('8', '田武师：“name，您现在先到庄院广场去建设一个决议大厅吧。”(请在主界面点击庄院-立刻建造决议大厅，注意建造建筑的过程需要一定时间，建造决议大厅大约需要1分钟，请等待建造完毕方可找田武师)<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '0', '0', '7', '9', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('9', '田武师：“name，您现在先到庄院广场去建设一个决议大厅吧。”(请在主界面点击庄院-立刻建造决议大厅，注意建造建筑的过程需要一定时间，建造决议大厅大约需要1分钟，请等待建造完毕方可找田武师)<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '1', '0', '8', '10', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('10', '你：“决议大厅已经建好了。”<br/>\r\n田武师：“不错。”<br/>\r\n银两+50<br/>\r\n潜能+40<br/>\r\n', '0', '5', '50', '40', '0', '0', '0', '0', '0', '0', '9', '11', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('11', '田武师：“name您多多注意你的任务栏，按照一些任务的提示，您可以了解各种建筑的用途，完成这些建筑及建筑的升级任务后，还能得到很可观的奖励呢！任务栏有个主线任务，这条主线任务会引导name一步步走入江湖，收伏各种武将，招募大量士兵，最终组成一支独霸天下的庞大军队！name您带领这支军队，说不定真能推翻当今朝廷的腐败统治，当上真龙天子！”<br/>\r\n你：“你说了这么多，不如直接告诉我，我下一步该怎么做吧！”<br/>\r\n<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '0', '0', '10', '12', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('12', '田武师：“周武师是我同乡，作为您的第一个武将，将一直跟随在name身边，name可以将任意一个武将设置为近身军，那么name以后在江湖闯荡过程中，就可以随时命令您的近身军攻打敌人；当然，您也可以设置多个近身军，这样，您就可以以多打一，增加胜算了！”<br/>\r\n你：“听上去不错。”<br/>\r\n田武师：“您的每个武将都可以带领一支军队，您可以随意指挥这些武将及他们的军队！当然，现在您只有一个武将，那就是周武师，所以也只能带领一支军队。不过不要着急，通过完成主线任务，会有更多猛将成为您的武将！”<br/>\r\n<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '0', '0', '11', '13', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('13', '你：“这太好了！我要收服猛将，组建强大的军队！”<br/>\r\n田武师：“好了，name，现在，您先学会做些购买方面的操作吧，您现在可以到庄院广场去，那里有我们庄院收留的三个买卖人，你先到武器那里去买一把长剑吧。”<br/>\r\n<br/>\r\n<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '0', '0', '12', '14', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('14', '你：“这太好了！我要收服猛将，组建强大的军队！”<br/>\r\n田武师：“好了，name，现在，您先学会做些购买方面的操作吧，您现在可以到庄院广场去，那里有我们庄院收留的三个买卖人，你先到武器那里去买一把长剑吧。”<br/>\r\n<br/>\r\n<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '2', '0', '13', '15', '2', '1', '0', '0');
INSERT INTO `task_entity` VALUES ('15', '你：“我已经买到长剑了。”<br/>\r\n田武师：“很好。”<br/>\r\n银两+50<br/>\r\n潜能+40<br/>\r\n粮草+900<br/>\r\n木材+900<br/>\r\n石料+900<br/>\r\n生铁+900<br/>', '0', '5', '50', '40', '900', '900', '900', '900', '0', '0', '14', '16', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('16', '田武师：“武器商只会出售一些比较低级的武器，您可以在庄院广场建设铁匠铺，等级高的铁匠铺可以打造一些高级武器。而以后你在江湖上闯荡的时候，也有可能得到极其厉害的神兵利器！好了，少爷，麻烦你再去庄院广场的防具那里，买一顶布帽吧，买来后我再跟你说说关于防具的一些事情。”<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '0', '0', '15', '17', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('17', '田武师：“武器商只会出售一些比较低级的武器，您可以在庄院广场建设铁匠铺，等级高的铁匠铺可以打造一些高级武器。而以后你在江湖上闯荡的时候，也有可能得到极其厉害的神兵利器！好了，少爷，麻烦你再去庄院广场的防具那里，买一顶布帽吧，买来后我再跟你说说关于防具的一些事情。”<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '2', '0', '16', '18', '13', '1', '0', '0');
INSERT INTO `task_entity` VALUES ('18', '你：“我买到布帽了。”<br/>\r\n田武师：“很好。”<br/>\r\n银两+60<br/>\r\n潜能+65<br/>', '0', '5', '65', '60', '0', '0', '0', '0', '0', '0', '17', '19', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('19', '田武师：“防具商只会出售一些比较低级的防具。防具的类别包括帽子、衣服、手套、戒指、鞋子这几种。你可以在庄院广场建筑裁缝铺，等级高的裁缝铺可以裁剪出一些高级的防具。而以后你在江湖上闯荡的时候，也有可能得到非常好的高级防具。士兵也可以装备兵器和穿上防具，武将带领的一个兵种，无论这个兵种有多少人，只需要装备上类别为士兵的装备即可（即装备名后面带[士兵]字样的装备），士兵类型的装备也可以在你的铁匠铺和裁缝铺里制造出来。”<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '0', '0', '18', '20', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('20', '田武师：“好了，name，现在您再到庄院广场的药品那里，买金创药x10过来。我再跟你说说关于药品和战斗的一些情况。”<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '0', '0', '19', '21', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('21', '田武师：“好了，name，现在您再到庄院广场的药品那里，买金创药x10过来。我再跟你说说关于药品和战斗的一些情况。”<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '2', '0', '20', '22', '20', '10', '0', '0');
INSERT INTO `task_entity` VALUES ('22', '你：“我买到金创药了。”<br/>\r\n田武师：“嗯，不错。”<br/>\r\n银两+80<br/>\r\n潜能+100<br/>', '0', '5', '80', '100', '0', '0', '0', '0', '0', '0', '21', '23', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('23', '田武师：“药品在战斗中是很重要的。战斗的时，你可以给武将指定其动作，你可以要求武将在下一回合出招或者使用药品。如果选择的是使用某种药品，那么在对方出招完毕后(可能对你的很多士兵造成了损伤)，你的武将会立即使用此药品为士兵或者自己疗伤，这样士兵或者武将本身就不会因为损伤而死去。”<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '0', '0', '22', '24', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('24', '田武师：“name，还有一项比较重要的属性您要了解一下，就是你本身的魄力及你的武将的魄力。你自身的魄力越高，才能将一些猛将收于帐下，而且你自身魄力和等级也对军队的战斗力能起到一定的加成作用；武将的魄力和等级越高，他所带领的士兵作战就越勇敢，攻击力也就越高。现在，我这里有一颗魄力丹，这颗丹是一位隐居的名士炼制的，食之可使人精神勃发，提高魄力！name，您顺便也去庄院的良田杀掉田鼠x3，来练习一下您的作战能力，您杀死田鼠x3后，再到我这里来，我把这颗魄力丹给你。从庄院广场往上、上即可到达良田。”<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '0', '0', '23', '25', '0', '0', '0', '0');
INSERT INTO `task_entity` VALUES ('25', '田武师：“name，还有一项比较重要的属性您要了解一下，就是你本身的魄力及你的武将的魄力。你自身的魄力越高，才能将一些猛将收于帐下，而且你自身魄力和等级也对军队的战斗力能起到一定的加成作用；武将的魄力和等级越高，他所带领的士兵作战就越勇敢，攻击力也就越高。现在，我这里有一颗魄力丹，这颗丹是一位隐居的名士炼制的，食之可使人精神勃发，提高魄力！name，您顺便也去庄院的良田杀掉田鼠x3，来练习一下您的作战能力，您杀死田鼠x3后，再到我这里来，我把这颗魄力丹给你。从庄院广场往上、上即可到达良田。”<br/>', '0', '5', '0', '0', '0', '0', '0', '0', '2', '0', '24', '26', '0', '0', '0', '0');

-- ----------------------------
-- Table structure for user_entity
-- ----------------------------
DROP TABLE IF EXISTS `user_entity`;
CREATE TABLE `user_entity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userName` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `isActive` tinyint(4) NOT NULL DEFAULT '1',
  `sign` varchar(255) NOT NULL,
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `IDX_5d1a3df5505df438cd2ad3b002` (`userName`),
  KEY `IDX_08010c127d09fff125be6b3449` (`sign`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of user_entity
-- ----------------------------
INSERT INTO `user_entity` VALUES ('4', 'lw4659564', '0a87da9e3fdeeea0bd0f14415da7114d', '1', 'ccd3f5255c09b867b6be2d18fa3be423', '2024-04-08 15:48:19', '2024-04-09 16:20:47');
INSERT INTO `user_entity` VALUES ('5', '4659564', '7fb1d5f8619ad2c2e0277aaab73a040c', '1', 'f50f7319148b8030815f023980f1c794', '2024-04-11 14:22:41', '2024-04-11 14:22:41');
