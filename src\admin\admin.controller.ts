import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { AdminService } from './admin.service';
import { CreateAdminDto } from './dto/create-admin.dto';
import { UpdateAdminDto } from './dto/update-admin.dto';
import { NpcAdminDto } from './dto/npc-admin.dto';
import { GeneralAdminDto } from './dto/general-admin.dto';
import { SoldierAdminDto } from './dto/soldier-admin.dto';
import { GoodAdminDto } from './dto/good-admin.dto';

@Controller('admin')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Post('getMaps')
  getMaps(@Body() createAdminDto: CreateAdminDto) {
    createAdminDto.id=createAdminDto.id||1
    return this.adminService.getMaps(createAdminDto);
  }
  
  @Post('addMap')
  addMap(@Body() createAdminDto: CreateAdminDto) {
    return this.adminService.addMap(createAdminDto);
  }
  @Post('updateMap')
  updateMap(@Body() createAdminDto: CreateAdminDto) {
    return this.adminService.updateMap(createAdminDto);
  }

  @Post('deleteLine')
  deleteLine(@Body() createAdminDto: CreateAdminDto) {
    return this.adminService.deleteLine(createAdminDto);
  }

  @Post('deleteMap')
  deleteMap(@Body() createAdminDto: CreateAdminDto) {
    return this.adminService.deleteMap(createAdminDto);
  }
  @Post('lineMap')
  lineMap(@Body() createAdminDto: CreateAdminDto) {
    return this.adminService.lineMap(createAdminDto);
  }
  @Post('getNpc')
  getNpc(@Body() createAdminDto: CreateAdminDto) {
    return this.adminService.getNpc(createAdminDto);
  }
  @Post('getGeneral')
  getGeneral(@Body() createAdminDto: CreateAdminDto) {
    return this.adminService.getGeneral(createAdminDto);
  }
  @Post('getSoldier')
  getSoldier(@Body() createAdminDto: CreateAdminDto) {
    return this.adminService.getSoldier(createAdminDto);
  }
  //物品列表
  @Post('getGoodsList')
  getGoodsList(@Body() createAdminDto: CreateAdminDto) {
    return this.adminService.getGoodsList(createAdminDto);
  }
  //添加物品
  @Post('addGood')
  addGood(@Body() generalAdminDto) {
    return this.adminService.addGood(generalAdminDto);
  }
  @Post('delGood')
  delGood(@Body() createAdminDto: CreateAdminDto) {
    return this.adminService.delGood(createAdminDto);
  }
  // @Post('getGeneralSkill')
  // getGeneralSkill(@Body() createAdminDto: CreateAdminDto) {
  //   return this.adminService.getGeneralSkill(createAdminDto);
  // }
  // @Post('getSoldierSkill')
  // getSoldierSkill(@Body() createAdminDto: CreateAdminDto) {
  //   return this.adminService.getSoldierSkill(createAdminDto);
  // }
  // @Post('getSkillTemplate')
  // getSkillTemplate(@Body() createAdminDto: CreateAdminDto) {
  //   return this.adminService.getSkillTemplate(createAdminDto);
  // }
  // @Post('addSkillTemplate')
  // addSkillTemplate(@Body() updateAdminDto: UpdateAdminDto) {
  //   return this.adminService.addSkillTemplate(updateAdminDto);
  // }
  // @Post('delSkillTemplate')
  // delSkillTemplate(@Body() CreateAdminDto: CreateAdminDto) {
  //   return this.adminService.delSkillTemplate(CreateAdminDto);
  // }
  @Post('addNpc')
  addNpc(@Body() npcAdminDto: NpcAdminDto) {
    return this.adminService.addNpc(npcAdminDto);
  }
  @Post('delNpc')
  delNpc(@Body() CreateAdminDto: CreateAdminDto) {
    return this.adminService.delNpc(CreateAdminDto);
  }
  @Post('addGeneral')
  addGeneral(@Body() generalAdminDto: GeneralAdminDto) {
    return this.adminService.addGeneral(generalAdminDto);
  }
  @Post('delGeneral')
  delGeneral(@Body() CreateAdminDto: CreateAdminDto) {
    return this.adminService.delGeneral(CreateAdminDto);
  }
  @Post('addSoldier')
  addSoldier(@Body() soldierAdminDto: SoldierAdminDto) {
    return this.adminService.addSoldier(soldierAdminDto);
  }
  @Post('delSoldier')
  delSoldier(@Body() CreateAdminDto: CreateAdminDto) {
    return this.adminService.delSoldier(CreateAdminDto);
  }
  @Post('copyNpc')
  copyNpc(@Body() CreateAdminDto: CreateAdminDto) {
    return this.adminService.copyNpc(CreateAdminDto);
  }
  @Get('addNpcs')
  addNpcs(@Body() CreateAdminDto: CreateAdminDto) {
    return this.adminService.addNpcs();
  }
  @Get('copyGoods')
  copyGoods(@Body() CreateAdminDto: CreateAdminDto) {
    return this.adminService.copyGoods();
  }
  @Get('getNpcIdByMapName')
  getNpcIdByMapName(@Body() CreateAdminDto: CreateAdminDto) {
    return this.adminService.getNpcIdByMapName(CreateAdminDto);
  }
  @Post('getList')
  getList(@Body() CreateAdminDto: CreateAdminDto) {
    return this.adminService.getList(CreateAdminDto);
  }
  @Post('sendGood')
  sendGood(@Body() CreateAdminDto: CreateAdminDto) {
    return this.adminService.sendGood(CreateAdminDto);
  }
  @Get('clearRedisCache')
  clearRedisCache() {
    return this.adminService.clearRedisCache();
  }
  @Post('getDropGoods')
  getDropGoods(@Body() CreateAdminDto: CreateAdminDto) {
    return this.adminService.getDropGoods(CreateAdminDto);
  }
  @Post('delDropGoods')
  delDropGoods(@Body() CreateAdminDto: CreateAdminDto) {
    return this.adminService.delDropGoods(CreateAdminDto);
  }
  @Post('addDropGoods')
  addDropGoods(@Body() CreateAdminDto: CreateAdminDto) {
    return this.adminService.addDropGoods(CreateAdminDto);
  }
  @Get('addDungeon')
  addDungeon() {
    return this.adminService.addDungeon();
  }
}
