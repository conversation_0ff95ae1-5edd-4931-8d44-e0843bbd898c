import { Modu<PERSON> } from '@nestjs/common';
import { AdminService } from './admin.service';
import { AdminController } from './admin.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MapsEntity } from 'src/entities/maps.entity';
import { NpcEntity } from 'src/entities/npc.entity';
import { NpcGeneralEntity } from 'src/entities/npcGeneral.entity';
import { NpcSoldierEntity } from 'src/entities/npcSoldier.entity';
import { GoodEntity } from 'src/entities/goods.entity';
import { NpcGoodsEntity } from 'src/entities/npcGoods.entity';
import { RoleEntity } from 'src/entities/role.entity';
import { PersonGoodsEntity } from 'src/entities/personGoods.entity';

@Module({
  imports: [TypeOrmModule.forFeature([
    MapsEntity,NpcEntity,
    NpcGeneralEntity,NpcSoldierEntity,NpcGoodsEntity,RoleEntity,PersonGoodsEntity,
    GoodEntity
  ])],
  controllers: [AdminController],
  providers: [AdminService],
})
export class AdminModule {}
