import { IsNumber, IsOptional, IsString } from 'class-validator';
export class CreateAdminDto {
    @IsNumber()
    @IsOptional()
    id: number;
    @IsString()
    @IsOptional()
    title: string;
    @IsString()
    @IsOptional()
    desc: string;
    @IsString()
    @IsOptional()
    directionType:string
    @IsNumber()
    @IsOptional()
    sourceId: number;
    @IsNumber()
    @IsOptional()
    page: number;
    @IsNumber()
    @IsOptional()
    size: number;
    @IsNumber()
    @IsOptional()
    gpsId: number;
    @IsNumber()
    @IsOptional()
    npcId: number;
    @IsNumber()
    @IsOptional()
    leftId: number;
    @IsNumber()
    @IsOptional()
    rightId: number;
    @IsNumber()
    @IsOptional()
    topId: number;
    @IsNumber()
    @IsOptional()
    bottomId: number;
    // 添加副本相关字段
    @IsString()
    @IsOptional()
    dungeonName: string;
    
    @IsNumber()
    @IsOptional()
    conditionType: number;
    
    @IsString()
    @IsOptional()
    mapName: string;
    
    @IsString()
    @IsOptional()
    goodName: string;
    
    @IsNumber()
    @IsOptional()
    maxCount: number;
    
    @IsString()
    @IsOptional()
    msg: string;
    
    @IsString()
    @IsOptional()
    showMapName: string;

    @IsNumber()
    @IsOptional()
    goodId: number;

    @IsString()
    @IsOptional()
    userName: string;

    @IsNumber()
    @IsOptional()
    num: number;
    @IsNumber()
    @IsOptional()
    min: number;
    @IsNumber()
    @IsOptional()
    max: number;

}
