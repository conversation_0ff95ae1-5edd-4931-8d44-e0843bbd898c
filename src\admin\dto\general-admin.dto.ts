import { IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
export class GeneralAdminDto{
    @IsNumber()
    @IsOptional()
    id: number;
    @IsString()
    name: string
    @IsNumber()
    level: number
    @IsNumber()
    hp: number
    @IsNumber()
    attack: number
    @IsNumber()
    defense: number
    @IsNumber()
    count: number
    @IsNumber()
    countAll: number
    @IsNumber()
    population: number
    @IsNumber()
    npcId: number
    @IsString()
    wuqiStr: string
    @IsString()
    fangjuStr: string
    @IsOptional()
    npc: any;
    @IsNumber()
    @IsOptional()
    hpNow: number;
    @IsString()
    @IsOptional()
    skillStr: string
}
