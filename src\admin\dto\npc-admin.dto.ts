import { IsNumber, IsOptional, IsString } from 'class-validator';
export class NpcAdminDto{
    @IsNumber()
    @IsOptional()
    id: number;
    @IsString()
    name: string
    @IsString()
    desc: string
    @IsNumber()
    directAttack: number
    @IsNumber()
    canAttack: number
    @IsNumber()
    canBuy: number
    @IsNumber()
    gpsId: number
    @IsNumber()
    intervalTime: number
    @IsNumber()//等级用于计算默认资源
    @IsOptional()
    level: number
    @IsString()
    config: string
    
    // 添加资源相关字段
    @IsNumber()
    @IsOptional()
    potential: number
    
    @IsNumber()
    @IsOptional()
    gold: number
    
    @IsNumber()
    @IsOptional()
    food: number
    
    @IsNumber()
    @IsOptional()
    wood: number
    
    @IsNumber()
    @IsOptional()
    stone: number
    
    @IsNumber()
    @IsOptional()
    iron: number
}
