import { IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
export class SoldierAdminDto{
    @IsNumber()
    @IsOptional()
    id: number;
    @IsString()
    name: string
    @IsNumber()
    level: number
    @IsNumber()
    hp: number
    @IsNumber()
    attack: number
    @IsNumber()
    defense: number
    @IsNumber()
    count: number
    @IsNumber()
    population: number
    @IsNumber()
    generalId: number
    @IsString()
    wuqiStr: string
    @IsString()
    fangjuStr: string
    @IsString()
    skillStr: string
    @IsOptional()
    general: any;
}
