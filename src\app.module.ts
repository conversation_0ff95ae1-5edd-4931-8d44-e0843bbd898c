import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RedisModule } from '@nestjs-modules/ioredis';
import { UsersModule } from './users/users.module';
import { GcmdModule } from './gcmd/gcmd.module';
import { ScheduleModule } from '@nestjs/schedule';
import { TasksModule } from './schedule/tasks.module';
import { TasksService } from './tasks/tasks.service';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { AdminModule } from './admin/admin.module';
import { WinstonLoggeModule } from './winston-logge/winston-logge.module';
import { CommonModule } from './middleware/common.module';
import { FightSubscriber } from './subscribers/fight.subscriber';
import { TeamRolesSubscriber } from './subscribers/teamRoles.subscriber';
@Module({
  imports: [
    //typeorm
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: '************',
      // host: '127.0.0.1',
      port: 3306,
      username: 'game',
      password: '7pABPD5sjrysJDzb',
      database: 'game',
      synchronize: true,
      autoLoadEntities: true,
      // logging: "all",//启动sql日志
      // logger: "file",
      subscribers: [FightSubscriber,TeamRolesSubscriber],
    }),
    //ioredis
    RedisModule.forRoot({
      type: 'single',
      url: 'redis://127.0.0.1:6379',
    }),
    ScheduleModule.forRoot(),
    EventEmitterModule.forRoot(),
    UsersModule,
    GcmdModule,
    TasksModule,
    AdminModule,
    WinstonLoggeModule,
    CommonModule,
  ],
  controllers: [AppController],
  providers: [AppService, TasksService],
})
export class AppModule {}
