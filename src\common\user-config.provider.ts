import { Injectable, Scope } from '@nestjs/common';

@Injectable({ scope: Scope.REQUEST }) // 声明为请求作用域
export class UserConfigProvider {
    private config;
    private userSetting;
    // 设置配置的方法，通常在身份验证或自定义Guard/Interceptor中调用
    setConfig(config) {
        this.config = config;
    }

    // 获取配置的方法，供其他服务注入使用
    getConfig() {
        return this.config;
    }

    setUserSetting(userSetting) {
        this.userSetting = userSetting;
    }

    getUserSetting() {
        return this.userSetting;
    }
}