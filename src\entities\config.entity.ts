import {Entity , Column ,PrimaryGeneratedColumn, Index} from 'typeorm'

@Entity()
export class ConfigEntity{
  
  @PrimaryGeneratedColumn()
  id:number

  //用户id 唯一
  @Column({ type: 'int',unique:true })
  @Index({ unique: true })
  userId:number

  //旗号
  @Column({ type: 'varchar', length: 9,default:'帝王' })
  flag:string
  //自动拾取 1开启 2关闭
  @Column({ type: 'int',default:1 })
  autoPick:number
  //pk状态 1开启 2关闭
  @Column({ type: 'int',default:2 })
  pkStatus:number
  //允许第三方加入 1开启 2关闭
  @Column({ type: 'int',default:2 })
  allowJoin:number
  //战场自动攻击秒数
  @Column({ type: 'int',default:30 })
  autoAttack:number
  //公共信息
  @Column({ type: 'int',default:1 })
  publicInfo:number
  //帮派信息
  @Column({ type: 'int',default:1 })
  guildInfo:number
  //队伍信息
  @Column({ type: 'int',default:1 })
  teamInfo:number
  //私聊信息
  @Column({ type: 'int',default:1 })
  privateInfo:number
  //是否接受赠送
  @Column({ type: 'int',default:1 })
  acceptGift:number
  //分页列表显示行数
  @Column({ type: 'int',default:20 })
  pageLine:number
  
  

}
