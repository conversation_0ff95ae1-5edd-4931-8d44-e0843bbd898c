import {Entity , Column ,PrimaryGeneratedColumn,Index} from 'typeorm'
//副本记录表
@Entity()
export class DungeonLogEntity{
  
  @PrimaryGeneratedColumn()
  id:number

  @Column({ type: 'varchar', length: 20, comment: '副本名称' })
  @Index()
  name:string
  //玩家id
  @Column({ type: 'int', comment: '玩家id' })
  @Index()
  userId:number
  //进入时间 记录时间戳
  @Column({ type: 'bigint', comment: '进入时间' })
  enterTime:number
  //状态  1:进入 2:通关 3:失败
  @Column({ type: 'int', comment: '状态', default: 1 })
  @Index()
  status:number
  
}