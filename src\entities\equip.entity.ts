import {Entity , Column ,PrimaryGeneratedColumn } from 'typeorm'

@Entity()
export class EquipEntity{
  
  @PrimaryGeneratedColumn()
  id:number
  //兵器 帽子 肩甲 衣服 披风 项链 手套 戒指 特殊戒指 裤子 鞋子 左手套 PK盾
  @Column({ type: 'int',nullable: true})
  bingqiId:number
  //兵器名称
  @Column({ type: 'varchar',nullable: true})
  bingqiName:string
  //兵器类型
  // 兵器类型  //21:刀  22:剑  23:棍  24:斧  25:锤  26:枪
  @Column({ type: 'int',nullable: true})
  weaponType: number
  @Column({ type: 'int',nullable: true})
  maoziId:number
  @Column({ type: 'varchar',nullable: true})
  maoziName:string
  @Column({ type: 'int',nullable: true})
  jianjiaId:number
  @Column({ type: 'varchar',nullable: true})
  jianjiaName:string
  @Column({ type: 'int',nullable: true})
  yifuId:number
  @Column({ type: 'varchar',nullable: true})
  yifuName:string
  @Column({ type: 'int',nullable: true})
  pifengId:number
  @Column({ type: 'int',nullable: true})
  xianglianId:number
  @Column({ type: 'varchar',nullable: true})
  xianglianName:string
  @Column({ type: 'int',nullable: true})
  shoutouId:number
  @Column({ type: 'varchar',nullable: true})
  shoutouName:string
  @Column({ type: 'int',nullable: true})
  jiezhiId:number
  @Column({ type: 'varchar',nullable: true})
  jiezhiName:string
  @Column({ type: 'int',nullable: true})
  jieshuId:number
  @Column({ type: 'varchar',nullable: true})
  jieshuName:string
  @Column({ type: 'int',nullable: true})
  kuziId:number
  @Column({ type: 'varchar',nullable: true})
  kuziName:string
  @Column({ type: 'int',nullable: true})
  xieziId:number
  @Column({ type: 'varchar',nullable: true})
  xieziName:string
  @Column({ type: 'int',nullable: true})
  zuoshoutaoId:number
  @Column({ type: 'varchar',nullable: true})
  zuoshoutaoName:string
  @Column({ type: 'int',nullable: true})
  pkdunId:number
  @Column({ type: 'varchar',nullable: true})
  pkdunName:string
}