import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm'
//装备表
@Entity()
export class EquipmentEntity {

    @PrimaryGeneratedColumn()
    id: number

    @Column({ type: 'varchar', length: 20, comment: '装备名称' })
    name: string
    @Column({ type: 'int', comment: '装备类型' })
    type: number
    @Column({ type: 'int', comment: '重量',default:1 })
    weight: number
    @Column({ type: 'int', comment: '攻击力' })
    attack: number
    @Column({ type: 'int', comment: '防御力' })
    defense: number
    @Column({ type: 'int', comment: '生命' })
    hp: number
    @Column({ type: 'int', comment: '耐久' })
    durability: number
    @Column({ type: 'int', comment: '是否绑定' })//1不绑定 2绑定
    isBind: number 
    @Column({ type: 'int', comment: '武将Id' })//武将id 和士兵id
    @Index()
    generalId: number

    //创建时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', comment: '创建时间' })
    createdAt: Date;
}