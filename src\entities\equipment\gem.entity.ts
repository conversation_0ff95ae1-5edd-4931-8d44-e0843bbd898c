import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm'
//装备上的宝石
@Entity()
export class GemEntity {

    @PrimaryGeneratedColumn()
    id: number

    @Column({ type: 'varchar', length: 20, comment: '宝石名称' })
    name: string
    @Column({ type: 'int', comment: '重量',default:1 })
    weight: number
    @Column({ type: 'int', comment: '攻击力' })
    attack: number
    @Column({ type: 'int', comment: '防御力' })
    defense: number
    @Column({ type: 'int', comment: '生命' })
    hp: number 
    @Column({ type: 'int', comment: '装备Id' })
    equipmentId: number

    //创建时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', comment: '创建时间' })
    createdAt: Date;
}