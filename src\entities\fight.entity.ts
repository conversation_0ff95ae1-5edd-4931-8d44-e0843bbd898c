import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, OneToOne, OneToMany, JoinTable, ManyToMany } from 'typeorm'
import { FightInfoEntity } from './fightInfo.entity'
import { FightGeneralEntity } from './fightGeneral.entity';
import { FightGoodsEntity } from './fightGoods.entity';
import { RoleEntity } from './role.entity';
//战场事件
@Entity()
export class FightEntity {

    @PrimaryGeneratedColumn()
    id: number
    
    //战场名称
    @Column({ type: 'varchar', length: 100 })
    name: string
    //场景id
    @Index()
    @Column({ type: 'int' })
    gpsId: number
    //是否npc  1是 2不是
    @Column({ type: 'int', default: 1 })
    attackIsNpc: number
    //是否npc  1是 2不是
    @Column({ type: 'int', default: 1 })
    defenseIsNpc: number
    //攻击方用户id
    @Column({ type: 'int', nullable: true })
    attackId: number
    //防守方id
    @Column({ type: 'varchar',length:15, nullable: true })
    defenseId: string
    //状态 1进行中 2攻方胜利 3防守方胜利 4攻守都失败
    @Column({ type: 'int', default: 1 })
    @Index()
    status: number 
    //战斗间隔
    @Column({ type: 'int', default: 30000 })
    interval: number
    //攻击方武将str
    @Column({ type: 'varchar',default:'' })
    attackGeneralStr:string
    //防守方武将str
    @Column({ type: 'varchar',default:'' })
    defenseGeneralStr:string
    //更新时间
    @UpdateDateColumn({ name: 'update_date', type: 'timestamp' })
    updateDate: Date;
    //时间
    @CreateDateColumn({ name: 'create_date', type: 'timestamp' })
    createDate: Date;
    //任务所属用户id
    @Column({ type: 'int', nullable: true })
    taskUserId: number
    //备注
    @Column({ type: 'varchar', length: 100, nullable: true })
    remark: string
    //一对多关联 FightInfoEntity
    @OneToMany(() => FightInfoEntity, fightInfos => fightInfos.fightEvent,{ cascade: true })
    fightInfos: FightInfoEntity[];
    //一对多关联 FightGeneralEntity
    @OneToMany(() => FightGeneralEntity, fightGenerals => fightGenerals.fightEvent,{ cascade: true })
    fightGenerals: FightGeneralEntity[];
    @OneToMany(() => FightGoodsEntity, fightGoods => fightGoods.fight)
    fightGoods: FightGoodsEntity[];
    
}