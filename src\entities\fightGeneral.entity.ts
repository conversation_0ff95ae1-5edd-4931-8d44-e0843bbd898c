import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, OneToOne } from 'typeorm'
import { FightEntity } from './fight.entity'
//参与战斗的武将
@Entity()
export class FightGeneralEntity {

    @PrimaryGeneratedColumn()
    id: number
    //攻击方武将id 可为空
    @Column({ type: 'varchar',nullable: true })
    attackGeneralId: string
    //防守方武将id
    @Column({ type: 'varchar' ,nullable: true })
    defenseGeneralId: string
    //要攻击的武将Id
    @Column({ type: 'varchar',nullable: true })
    targetGeneralId: string
    //控制状态 0未控制 1已控制
    @Column({ type: 'int', default: 0 })
    controlStatus: number;
    //用户id
    @Column({ type: 'int',nullable: true  })
    userId: number
    //要使用的攻击方式 没设置就是用默认 默认没有就普通攻击
    @Column({ type: 'varchar',default:'' })
    attackMethod:string
    //武将名称
    @Column({ type: 'varchar', length: 20 })
    name: string
    //玩家名称
    @Column({ type: 'varchar', length: 20,nullable: true  })
    userName: string
    //是否死亡 1否 2是
    @Column({ type: 'int', default: 1 })
    isDead: number
    //潜能
    @Column({ type: 'int',default:0 })
    potential:number
    // 银两
    @Column({ type: 'int',default: 0})
    gold:number
    //粮草
    @Column({ type: 'int',default:0 })
    food:number
    //木材
    @Column({ type: 'int',default:0 })
    wood:number
    //石料
    @Column({ type: 'int',default:0 })
    stone:number
    //生铁
    @Column({ type: 'int',default:0 })
    iron:number
    //时间
    @CreateDateColumn({ name: 'create_date', type: 'timestamp' })
    createDate: Date;
    //多对一关联 FightEventEntity
    @ManyToOne(() => FightEntity, fightEvent => fightEvent.fightGenerals)
    fightEvent: FightEntity;
}