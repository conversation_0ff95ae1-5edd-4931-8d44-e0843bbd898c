import { FightEntity } from './fight.entity';
import {Entity , Column ,PrimaryGeneratedColumn, ManyToOne} from 'typeorm'
//战场掉落物品
@Entity()
export class FightGoodsEntity{
  
  @PrimaryGeneratedColumn()
  id:number
// 数量
  @Column({ type: 'int'})
  count:number
  //状态 1 未被拾取 2 已被拾取
  @Column({ type: 'int', default: 1 })
  status:number
  @ManyToOne(() => FightEntity, fight => fight.fightGoods)
  fight: FightEntity;
  //物品id
  @Column({ type: 'int',nullable:true})
  goodId:number
  //物品名称
  @Column({ type: 'varchar', length: 20 })
  goodName:string
}