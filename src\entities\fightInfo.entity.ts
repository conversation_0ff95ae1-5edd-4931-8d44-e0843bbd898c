import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, OneToOne } from 'typeorm'
import { FightEntity } from './fight.entity'
//战场详情
@Entity()
export class FightInfoEntity {

    @PrimaryGeneratedColumn()
    id: number
    //描述
    @Column({ type: 'text' })
    desc: string
    //武将id
    @Column({nullable:true})
    @Index()
    generalId: number
    //伤害
    @Column({ type: 'int',nullable:true })
    harm: number
    //时间
    @CreateDateColumn({ name: 'create_date', type: 'timestamp' })
    createDate: Date;
    //多对一关联 FightEventEntity
    @ManyToOne(() => FightEntity, fightEvent => fightEvent.fightInfos)
    fightEvent: FightEntity;
}