import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, OneToOne, OneToMany, JoinColumn, BeforeUpdate } from 'typeorm'

//导入技能
import { SkillEntity } from './skill.entity'
import { SoldierEntity } from './soldier.entity'
import { NpcEntity } from './npc.entity'
import { GeneralEquip } from './general/generalequip.entity'
//武将实体
@Entity()
export class GeneralEntity {

  @PrimaryGeneratedColumn()
  id: number
  //名称
  @Column({ type: 'varchar', length: 20 })
  name: string
  //等级
  @Column({ type: 'int', default: 1 })
  level: number
  //魄力
  @Column({ type: 'int', default: 0 })
  soul: number
  //攻击力
  @Column({ type: 'int', default: 20 })
  attack: number
  //额外增加的攻击力  比如吃狂暴一二三增加的攻击力
  @Column({ type: 'int', default: 0 })
  attackAdd: number
  //防御力
  @Column({ type: 'int', default: 40 })
  defense: number
  //额外增加的增加的防御力
  @Column({ type: 'int', default: 0 })
  defenseAdd: number
  //体力
  @Column({ type: 'int', default: 2701 })
  hp: number
  //额外增加的增加的体力
  @Column({ type: 'int', default: 0 })
  hpAdd: number
  // 当前体力  0死亡
  @Column({ type: 'int', default: 2701 })
  hpNow: number
  // 兵器 (Original ID: 30)
  @Column({ type: 'int', nullable: true, comment: '兵器实例ID' })
  bingqiId: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '兵器实例名称' })
  bingqiName: string | null;

  // 帽子 (Original ID: 31)
  @Column({ type: 'int', nullable: true, comment: '帽子实例ID' })
  maoziId: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '帽子实例名称' })
  maoziName: string | null;

  // 衣服 (Original ID: 32)
  @Column({ type: 'int', nullable: true, comment: '衣服实例ID' })
  yifuId: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '衣服实例名称' })
  yifuName: string | null;

  // 裤子 (Original ID: 33)
  @Column({ type: 'int', nullable: true, comment: '裤子实例ID' })
  kuziId: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '裤子实例名称' })
  kuziName: string | null;

  // 鞋子 (Original ID: 34)
  @Column({ type: 'int', nullable: true, comment: '鞋子实例ID' })
  xieziId: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '鞋子实例名称' })
  xieziName: string | null;

  // 项链 (Original ID: 35)
  @Column({ type: 'int', nullable: true, comment: '项链实例ID' })
  xianglianId: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '项链实例名称' })
  xianglianName: string | null;

  // 戒指 (Original ID: 36)
  @Column({ type: 'int', nullable: true, comment: '戒指实例ID' })
  jiezhiId: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '戒指实例名称' })
  jiezhiName: string | null;

  // 手套 (Original ID: 37)
  @Column({ type: 'int', nullable: true, comment: '手套实例ID' })
  shoutaoId: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '手套实例名称' })
  shoutaoName: string | null;

  // 肩甲 (Original ID: 38)
  @Column({ type: 'int', nullable: true, comment: '肩甲实例ID' })
  jianjiaId: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '肩甲实例名称' })
  jianjiaName: string | null;

  // 披风 (Original ID: 39)
  @Column({ type: 'int', nullable: true, comment: '披风实例ID' })
  pifengId: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '披风实例名称' })
  pifengName: string | null;

  // 特殊戒指 (Original ID: 40)
  @Column({ type: 'int', nullable: true, comment: '特殊戒指实例ID' })
  teshuJiezhiId: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '特殊戒指实例名称' })
  teshuJiezhiName: string | null;

  // 左手套 (Original ID: 41) - Assuming this is distinct from '手套' perhaps for dual-wielding or off-hand
  @Column({ type: 'int', nullable: true, comment: '左手套实例ID' })
  zuoShoutaoId: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '左手套实例名称' })
  zuoShoutaoName: string | null;

  // PK盾 (Original ID: 42)
  @Column({ type: 'int', nullable: true, comment: 'PK盾实例ID' })
  pkDunId: number | null;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: 'PK盾实例名称' })
  pkDunName: string | null;

  @BeforeUpdate()
  checkHpNow() {
    if (this.hpNow < 0) {
      this.hpNow = 0;
    } else if (this.hpNow > this.hp) {
      this.hpNow = this.hp;
    }
  }
  //下回合技能
  @Column({ length: 20, nullable: true })
  attackMethod: string
  //近身  1234 最大4
  @Column({ type: 'int', default: 0 })
  near: number
  //梯队 默认第二梯队
  @Column({ type: 'int', default: 2 })
  teamOrder: number
  //排序
  @Column({ type: 'int', default: 0 })
  sort: number
  // 带兵人口:5/5
  //当前人口数量
  @Column({ type: 'int', default: 0 })
  count: number
  //可带兵总人口数量  临时没用到
  @Column({ type: 'int', default: 11 })
  countAll: number
  //个数
  //   带兵数量:5
  @Column({ type: 'int', default: 0 })
  population: number

  //受到的伤害
  @Column({ type: 'int', default: 0 })
  lastHurt: number
  //战斗快捷键1  1 技能 2逃跑 3使用物品  类型&名称&id
  @Column({ type: 'varchar', default: '快捷' })
  cmd1: string
  @Column({ type: 'varchar', default: '快捷' })
  cmd2: string
  @Column({ type: 'varchar', default: '快捷' })
  cmd3: string
  @Column({ type: 'varchar', default: '快捷' })
  cmd4: string
  @Column({ type: 'varchar', default: '快捷' })
  cmd5: string
  @Column({ type: 'varchar', default: '快捷' })
  cmd6: string
  @Column({ type: 'varchar', nullable: true })
  wuqiStr: string
  @Column({ type: 'varchar', nullable: true })
  fangjuStr: string
  @Column({ type: 'varchar', nullable: true })
  skillStr: string
  // 战斗状态 0未战斗 1战斗中
  @Column({ type: 'int', default: 0 })
  fightStatus: number;
  //升级状态  1未升级 2升级中
  @Column({ type: 'int', default: 1 })
  upgradeStatus: number
  @Column({ type: 'int', nullable: true })
  userId: number
  //狂暴属性 满值100
  @Column({ type: 'int', default: 0 })
  raguar: number
  //极度狂暴 满值100
  @Column({ type: 'int', default: 0 })
  extremeRaguar: number
  //龙魄狂暴 满值100
  @Column({ type: 'int', default: 0 })
  longpoRaguar: number
  //金刚属性 满值100
  @Column({ type: 'int', default: 0 })
  jingang: number
  //血印金刚属性 满值100
  @Column({ type: 'int', default: 0 })
  xueyinJingang: number
  //龙魄金刚属性 满值100
  @Column({ type: 'int', default: 0 })
  longpoJingang: number
  //强体属性 满值100
  @Column({ type: 'int', default: 0 })
  qiangti1: number
  //强体属性 满值100
  @Column({ type: 'int', default: 0 })
  qiangti2: number
  //强体属性 满值100
  @Column({ type: 'int', default: 0 })
  qiangti3: number

  //当前开脉名称
  @Column({ type: 'varchar', default: '' })
  kaimaiName: string
  //当前魂魄名称
  @Column({ type: 'varchar', default: '' })
  hunpoName: string
  @ManyToOne(() => NpcEntity, npc => npc.generals)
  npc: NpcEntity;
  @OneToMany(() => SoldierEntity, soldierEntity => soldierEntity.general, { cascade: true })
  soldiers: SoldierEntity[];
  //一对多关联技能实体
  @OneToMany(type => SkillEntity, skills => skills.general)
  skills: SkillEntity[]
  //时间
  @CreateDateColumn({ name: 'create_date', type: 'timestamp' })
  createDate: Date;
  equip: GeneralEquip[];
  //兵器类型
  @Column({ type: 'int', default: 0 })
  weaponType: number
}