import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm'
//祝福状态
@Entity()
export class BlessEntity {
    @PrimaryGeneratedColumn()
    id: number
    @Column({ type: 'int', comment: '用户',nullable:true })
    userId: number
    @Column({ type: 'int', comment: '武将Id',nullable:true })
    generalId: number
    @Column({ type: 'int', comment: '士兵Id',nullable:true })
    soldierId: number
    @Column({ type: 'int', comment: '祝福类型' })//0攻击秘术 1防御秘术 2自动补血丹 3VIP月卡 4四倍潜能卡 5双倍潜能卡 6双倍银两卡 7四倍资源卡 8双倍资源卡 9神农秘籍
    blessType: number
    @Column({ type: 'timestamp', comment: '结束时间' }) //自动补血丹使用时间  一小时一个
    endTime:Date
    @Column({ type: 'int', comment: '状态' })//1未结束 2已结束
    @Index()
    status: number
    @Column({ type: 'int', comment: '剩余量',nullable:true })
    shengyu: number
}