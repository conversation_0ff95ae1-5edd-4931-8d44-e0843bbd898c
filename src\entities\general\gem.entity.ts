import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm'
//装备上的宝石
@Entity()
export class GenEntity {
    @PrimaryGeneratedColumn()
    id: number
    @Column({ type: 'int', comment: '模板宝石Id' })
    equipmentId: number
    @Column({ type: 'int', comment: '用户背包物品id',nullable:true })
    @Index()
    personGoodId: number
    @Column({ type: 'int', comment: '武将装备Id',nullable:true })
    generalId: number
    @Column({ type: 'int', comment: '攻击力',nullable:true})
    attack: number
    @Column({ type: 'int', comment: '防御力',nullable:true })
    defense: number
    @Column({ type: 'int', comment: '生命',nullable:true })
    hp: number
}