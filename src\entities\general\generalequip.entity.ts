import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm'
//武将装备
@Entity()
export class GeneralEquip {
    @PrimaryGeneratedColumn()
    id: number
    @Column({ type: 'int', comment: '模板装备Id' })
    equipmentId: number
    @Column({ type: 'int', comment: '武将Id',nullable:true })
    generalId: number
    @Column({ type: 'int', comment: '士兵Id',nullable:true })
    soldierId: number
    @Column({ type: 'int', comment: '坐骑Id',nullable:true })
    mountId: number
    @Column({ type: 'int', comment: '攻击力',nullable:true  })
    attack: number
    @Column({ type: 'int', comment: '防御力',nullable:true  })
    defense: number
    @Column({ type: 'int', comment: '生命',nullable:true  })
    hp: number
    @Column({ type: 'int', comment: '耐久',nullable:true  })
    durability: number
    @Column({ type: 'int', comment: '是否绑定',nullable:true  })//1绑定 2不绑定
    isBind: number
    @Column({ type: 'int', comment: '装备位置'  })
    //30武器 31:帽子  32:衣服  33:裤子  34:鞋子  35:项链  36:戒指  37:手套  38:肩甲  39:披风  40:特殊戒指 41:左手套 42:PK盾
    //51:蹄铁 52:马鞍 53:缰绳 54:马铠 55:马蹬 56:马嚼
    type: number
}