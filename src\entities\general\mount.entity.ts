import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm'
//坐骑
@Entity()
export class MountEntity {
    @PrimaryGeneratedColumn()
    id: number
    @Column({ type: 'int', comment: '用户'})
    @Index()
    userId: number
    @Column({ type: 'int', comment: '武将Id',nullable:true })
    generalId: number
    @Column({ type: 'int', comment: '士兵Id',nullable:true })
    soldierId: number
    @Column({ type: 'int', comment: '等级' })
    level: number
    //两位小数
    @Column({ type: 'decimal', comment: '攻击力',default:0,precision:10,scale:2 })
    attack: number
    @Column({ type: 'decimal', comment: '防御力',default:0,precision:10,scale:2 })
    defense: number
    @Column({ type: 'decimal', comment: '额外攻击力',default:0,precision:10,scale:2 })//吃攻击丹
    extraAttack: number
    @Column({ type: 'decimal', comment: '额外防御力',default:0,precision:10,scale:2 })//吃防御丹
    extraDefense: number
    @Column({ type: 'varchar', comment: '坐骑类型' })//tiannu
    mountType: string
    //坐骑装备 蹄铁 、马鞍、缰绳、马铠、马蹬、马嚼
    @Column({ type: 'int', comment: '蹄铁id',nullable:true  })
    titieId: number
    @Column({ type: 'varchar', comment: '蹄铁名称',nullable:true  })
    titieName: string
    @Column({ type: 'int', comment: '马鞍id',nullable:true  })
    maanId: number
    @Column({ type: 'varchar', comment: '马鞍名称',nullable:true  })
    maanName: string
    @Column({ type: 'int', comment: '缰绳id',nullable:true  })
    jiangshengId: number
    @Column({ type: 'varchar', comment: '缰绳名称',nullable:true  })
    jiangshengName: string
    @Column({ type: 'int', comment: '马铠id',nullable:true  })
    makaiId: number
    @Column({ type: 'varchar', comment: '马铠名称',nullable:true  })
    makaiName: string
    @Column({ type: 'int', comment: '马蹬id',nullable:true  })
    madengId: number
    @Column({ type: 'varchar', comment: '马蹬名称',nullable:true  })
    madengName: string
    @Column({ type: 'int', comment: '马嚼id',nullable:true  })
    majueId: number
    @Column({ type: 'varchar', comment: '马嚼名称',nullable:true  })
    majueName: string

    //挂售状态
    @Column({ type: 'int', comment: '挂售价格',nullable:true })//挂售价格
    price: number
    @Column({ type: 'int', comment: '挂售价格',default:1 })//挂售状态 1未挂售 2已挂售
    sellStatus: number
    //升级状态
    @Column({ type: 'int', comment: '升级状态',default:1 })//1未升级 2升级中
    upgradeStatus: number
    @Column({ type: 'int', comment: '状态',default:1 })//1闲置 2外出
    @Index()
    status: number
}