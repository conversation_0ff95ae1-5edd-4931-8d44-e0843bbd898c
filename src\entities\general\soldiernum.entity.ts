import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm'
//散部士兵
@Entity()
export class SoldierNum {
    @PrimaryGeneratedColumn()
    id: number
    @Column({ type: 'int', comment: '用户' })
    userId: number
    @Column({ type: 'int', comment: '士兵数量' })
    soldierNum: number
    @Column({ type: 'varchar', comment: '士兵模板类型' })
    soldierType: string
    @Column({ type: 'varchar', comment: '士兵名称' })
    soldierName: string
    @Column({ type: 'int', comment: '挂售价格',nullable: true })
    price: number
    @Column({ type: 'int', comment: '是否挂售中，默认0未挂售', default: 0 })
    isSold: number
}