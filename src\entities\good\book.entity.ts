import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm'
//祝福状态
@Entity()
export class BookEntity {
    @PrimaryGeneratedColumn()
    id: number
    @Column({ type: 'int', comment: '类型' })//1油墨 3坐骑 2丹药
    type: number
    @Column({ type: 'varchar', comment: '名称' })
    title: string
    @Column({ type: 'int', comment: '数量' })//比例研制所需数量
    biliNum: number
    @Column({ type: 'int', comment: '比例' })//成功率 30表示随机数小于30就中
    bili: number
    @Column({ type: 'int', comment: '数量' })//百分百研制所需数量
    count: number
}