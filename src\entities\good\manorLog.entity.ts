import { Entity, Column, PrimaryGeneratedColumn, Index, CreateDateColumn } from 'typeorm'
//加速记录
@Entity()
export class ManorLogEntity {
    @PrimaryGeneratedColumn()
    id: number
    //建筑id
    @Column({ type: 'int', comment: '建筑id' })
    buildId: number
    //使用物品名称
    @Column({ type: 'varchar', comment: '物品名称' })
    goodName: string
    //建筑等级
    @Column({ type: 'int', comment: '建筑等级' })
    buildLevel: number
    //创建时间
    @CreateDateColumn({name:'create_date',type: 'timestamp'})
    createDate: Date;
}
