import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm'
//npc领取物品列表
@Entity()
export class ReceiveListEntity {
    @PrimaryGeneratedColumn()
    id: number
    //领取间隔时间 0是不限制 1是1天 
    @Column({ type: 'int', comment: '领取间隔时间' })
    receiveTime: number
    //领取物品和数量 星光剑|1&星光帽|1 默认1
    @Column({ type: 'text', comment: '领取物品' })
    receiveGood: string
    @Column({ type: 'varchar', comment: '展示的名称' })
    title: string
    //消耗的物品 星光剑|1&星光帽|1 默认1
    @Column({ type: 'varchar', comment: '消耗的物品',nullable:true })
    consumeGood: string
    //指定谁可以领取
    @Column({ type: 'varchar', comment: '用户id多个已&隔开',nullable:true })
    userIds: string
}
