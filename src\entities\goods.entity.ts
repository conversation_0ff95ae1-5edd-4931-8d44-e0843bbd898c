import {Entity , Column ,PrimaryGeneratedColumn,Index,OneToMany, ManyToMany} from 'typeorm'
import{PersonGoodsEntity} from './personGoods.entity'
import{NpcEntity} from './npc.entity'

//物品表
@Entity()
export class GoodEntity{
  
  @PrimaryGeneratedColumn()
  id:number
// 名称
  @Column({ type: 'varchar', length: 20})
  @Index()
  name:string
  //描述
  @Column({ type: 'varchar', length: 255,default:'' })
  desc:string
  //价钱
  @Column({ type: 'int',default:0})
  price:number
  //主类别 1:消耗品 2:兵器 3:防具 4:士兵兵器 5:士兵防具 6:坐骑装备 7:装备宝石 8:其他 
  @Column({ type: 'int'})
  @Index()
  type:number
  //副类别
  //21:刀  22:剑  23:棍  24:斧  25:锤  26:枪
  //31:帽子  32:衣服  33:裤子  34:鞋子  35:项链  36:戒指  37:手套  38:肩甲  39:披风  40:特殊戒指 41:左手套 42:PK盾
  //61:蹄铁 62:马鞍 63:缰绳 64:马铠 6:马蹬 66:马嚼
  //71:商城 72:强化 73:材料 74:训练 75:任务 76:活动 77:神兵  78:其他
  @Column({ type: 'int',nullable:true})
  @Index()
  subType:number
  //抵消伤害//增加血量
  @Column({ type: 'int',default:0})
  addhp:number
  //增加攻击
  @Column({ type: 'int',default:0})
  addatk:number
  //增加防御
  @Column({ type: 'int',default:0})
  adddef:number
  //是否绑定  1绑定 2不绑定
  @Column({ type: 'int',default:2})
  bind:number
  //重量
  @Column({ type: 'int',default:1})
  weight:number
  //使用等级
  @Column({ type: 'int',default:1})
  useLevel:number
  //使用条件
  @Column({ type: 'varchar', length: 100,default:'' })
  useCondition:string
  //玩家使用  0是不能使用 1可以使用
  @Column({ type: 'int',default:0})
  roleUse:number
  //武将使用
  @Column({ type: 'int',default:0})
  generalUse:number
  //士兵使用
  @Column({ type: 'int',default:0})
  soldierUse:number
  //只能战斗中使用  1是
  @Column({ type: 'int',default:0})
  fightUse:number
  //是否可叠加  1不可叠加 2可叠加
  @Column({ type: 'int',default:2})
  isStack:number
  @Column({ type: 'int', comment: '耐久',nullable:true })
  durability: number
  //初始孔数
  @Column({ type: 'int',default:0})
  holeCount:number
  @OneToMany(() => PersonGoodsEntity, personGoods => personGoods.good)
  personGoods: PersonGoodsEntity[];
  @ManyToMany(() => NpcEntity, npc => npc.goods)
  npcs: NpcEntity[];   
  //是否可锻造  0 不可锻造 1 可锻造
  @Column({ type: 'int',default:0})
  @Index()
  isForge:number
  //需要的石头数量
  @Column({ type: 'int',nullable:true})
  forgeMaterialCount:number
  //成功率  20 代表 0-20之间的就成功
  @Column({ type: 'int',nullable:true})
  successRate:number
  //百分百锻造需要的石头
  @Column({ type: 'int',nullable:true})
  forgeMaterialCount100:number
}
