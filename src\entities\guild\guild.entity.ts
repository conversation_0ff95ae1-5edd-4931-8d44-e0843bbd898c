import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm';

// 帮派主表
@Entity()
export class GuildEntity {
    @PrimaryGeneratedColumn()
    id: number;

    // 帮派名称 (唯一)
    @Column({ type: 'varchar', length: 20, unique: true })
    name: string;

    // 帮派描述
    @Column({ type: 'varchar', length: 200, default: '' })
    description: string;

    // 帮派等级
    @Column({ type: 'int', default: 1 })
    level: number;

    // 帮派经验
    @Column({ type: 'bigint', default: 0, unsigned: true })
    exp: number;

    // 最大成员数量
    @Column({ type: 'int', default: 50 })
    maxMembers: number;

    // 当前成员数量
    @Column({ type: 'int', default: 1 })
    currentMembers: number;

    // 帮主ID
    @Column({ type: 'int' })
    @Index()
    leaderId: number;

    // 帮主名称
    @Column({ type: 'varchar', length: 20 })
    leaderName: string;

    // 副帮1主ID
    @Column({ type: 'int' })
    @Index()
    leaderId1: number;

    // 副帮主1名称
    @Column({ type: 'varchar', length: 20 })
    leaderName1: string;
    // 帮主ID
    @Column({ type: 'int' })
    @Index()
    leaderId2: number;

    // 副帮主2名称
    @Column({ type: 'varchar', length: 20 })
    leaderName2: string;
    //副帮主2称谓
    @Column({ type: 'varchar', length: 20,default:"帮主" })
    leaderTitle: string;

    // 帮派公告
    @Column({ type: 'text', nullable: true })
    notice: string;

    // 加入方式 (1:自由加入 2:需要审核 3:禁止加入)
    @Column({ type: 'int', default: 2 })
    joinType: number;

    // 帮派资金
    @Column({ type: 'bigint', default: 0, unsigned: true })
    treasury: number;

    // 帮派声望
    @Column({ type: 'int', default: 0 })
    prestige: number;

    // 帮派状态 (1:正常 2:解散)
    @Column({ type: 'int', default: 1 })
    @Index()
    status: number;

    // 创建时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    // 更新时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    updatedAt: Date;
}