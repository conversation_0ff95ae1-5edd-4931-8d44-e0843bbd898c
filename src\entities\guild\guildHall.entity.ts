import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, OneToMany, Index } from 'typeorm';

// 帮派分堂表
@Entity()
export class GuildHallEntity {
    @PrimaryGeneratedColumn()
    id: number;

    // 关联帮派
    @Column({ type: 'int' })
    guildId: number;

    // 分堂名称
    @Column({ type: 'varchar', length: 20 })
    name: string;
    //堂主头衔
    @Column({ type: 'varchar', length: 20, nullable: true })
    leaderTitle: string;

    // 堂主ID
    @Column({ type: 'int', nullable: true })
    @Index()
    leaderId: number;

    // 堂主名称
    @Column({ type: 'varchar', length: 20, nullable: true })
    leaderName: string;

    // 分堂成员数量
    @Column({ type: 'int', default: 0 })
    memberCount: number;
    // 创建时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    // 更新时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    updatedAt: Date;
}
