// 帮派相关实体统一导出文件

// 帮派主表
export { GuildEntity } from './guild.entity';

// 帮派申请表
export { GuildApplicationEntity } from './guildApplication.entity';

// 帮派分堂表
export { GuildHallEntity } from './guildHall.entity';

// 帮派成员表
export { GuildMemberEntity } from './guildMember.entity';

// 领地表
export { TerritoryEntity } from './territory.entity';

// 导入实体用于数组
import { GuildEntity } from './guild.entity';
import { GuildApplicationEntity } from './guildApplication.entity';
import { GuildHallEntity } from './guildHall.entity';
import { GuildMemberEntity } from './guildMember.entity';
import { TerritoryEntity } from './territory.entity';

// 帮派相关实体数组，用于TypeORM配置
export const GUILD_ENTITIES = [
    GuildEntity,
    GuildApplicationEntity,
    GuildHallEntity,
    GuildMemberEntity,
    TerritoryEntity,
];
