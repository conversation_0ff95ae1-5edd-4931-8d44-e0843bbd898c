import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm';

// 领地实体
@Entity()
export class TerritoryEntity {
    @PrimaryGeneratedColumn()
    id: number;

    // 领地名称
    @Column({ type: 'varchar', length: 30 })
    @Index()
    name: string;

    // 当前占领的帮派ID
    @Column({ type: 'int', nullable: true })
    @Index()
    guildId: number;

    // 当前占领的帮派名称
    @Column({ type: 'varchar', length: 20, nullable: true })
    guildName: string;

    // 驻军ID
    @Column({ type: 'int', nullable: true })
    @Index()
    garrisonId: number;

    // 驻军名称
    @Column({ type: 'varchar', length: 20, nullable: true })
    garrisonName: string;

    // 良田等级
    @Column({ type: 'int', default: 1 })
    farmLevel: number;

    // 伐木场等级
    @Column({ type: 'int', default: 1 })
    lumberLevel: number;

    // 铁矿等级
    @Column({ type: 'int', default: 1 })
    ironLevel: number;

    // 采石场等级
    @Column({ type: 'int', default: 1 })
    stoneLevel: number;

    // 领地状态 (1:可占领 2:已占领 3:战争中)
    @Column({ type: 'int', default: 1 })
    @Index()
    status: number;

    // 占领时间
    @Column({ type: 'timestamp', nullable: true })
    occupiedAt: Date;

    // 创建时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    // 更新时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    updatedAt: Date;
}
