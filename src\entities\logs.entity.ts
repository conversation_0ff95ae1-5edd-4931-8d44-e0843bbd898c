import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm'

@Entity()
export class LogsEntity {
    @PrimaryGeneratedColumn()
    id: number

    @Column({ type: 'varchar'})
    userId: number
    @Column({ type: 'varchar', length: 50 })
    name: string
    //粮草
    @Column({ type: 'int',default:0 })
    food:number
    //木材
    @Column({ type: 'int',default:0 })
    wood:number
    //石料
    @Column({ type: 'int',default:0 })
    stone:number
    //生铁
    @Column({ type: 'int',default:0 })
    iron:number
    // 银两
    @Column({ type: 'int',default:0 })
    gold:number
    //潜能
    @Column({ type: 'int',default:0 })
    potential:number
    //创建时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;
}