import {Entity , Column ,PrimaryGeneratedColumn,CreateDateColumn,UpdateDateColumn, Index,ManyToOne, OneToOne} from 'typeorm'
//庄园建筑
@Entity()
export class ManorEntity{
  
  @PrimaryGeneratedColumn()
  id:number
  //类型 1庄院 2房屋 7铁匠铺8裁缝铺9书院 10兵部 11马棚   12武将攻击术 13武将防御术 14士兵攻击术 15士兵防御术
  @Column({ type: 'int'})
  type:number
  //等级
  @Column({ type: 'int',default:0 })
  level:number
  //用户id
  @Column({ type: 'int'})
  @Index()
  userId:number
  //状态 1升级中 2升级完成

  @Column({ type: 'int',default:1 })
  status:number
  //时间
  @CreateDateColumn({name:'create_date',type: 'timestamp'})
    createDate: Date;
}