import {Entity , Column ,PrimaryGeneratedColumn, Index} from 'typeorm'
//地图表
@Entity()
export class MapsEntity {
  
  @PrimaryGeneratedColumn()
  id:number
// 标题
  @Column({ type: 'varchar', length: 100})
  title:string
  //描述
  @Column({ type: 'varchar', nullable: true})
  desc:string
  //上 四个方向的地图id
  @Column({nullable: true})
  topId:number
  //下
  @Column({nullable: true})
  bottomId:number
  //左
  @Column({nullable: true})
  leftId:number
  //右
  @Column({nullable: true})
  rightId:number
  @Column({nullable: true})
  top:string
  //下
  @Column({nullable: true})
  bottom:string
  //左
  @Column({nullable: true})
  left:string
  //右
  @Column({nullable: true})
  right:string
  //副本名称 默认为空
  @Column({type: 'varchar',length: 20,nullable: true})
  @Index()
  dungeonName:string
  //进入条件类型  1打怪  2需要物品 3副本进入次数 4主线任务
  @Column({ type: 'int',default:0 })
  conditionType:number
  //当条件是物品时 需要验证来源gpsId
  @Column({ type: 'int',nullable: true })
  sourceGpsId:number
  //要清理的地图名称   地图1|地图2
  @Column({type: 'varchar',length: 40,nullable: true})
  mapName:string
  //所需要的物品  物品1|数量&物品2|数量   主线要求|任务id
  @Column({type: 'varchar',length: 40,nullable: true})
  goodName:string
  //最大进入次数  
  @Column({ type: 'int',nullable: true })
  maxCount:number
  //提示信息
  @Column({type: 'varchar',length: 255,nullable: true})
  msg:string
  //页面中显示传送地图  进入树洞|6968  多个用&隔开
  @Column({type: 'varchar',length: 40,nullable: true})
  showMapName:string
  //第三方页面 爬大柱 多个用&隔开
  @Column({type: 'varchar',length: 40,nullable: true})
  otherMap:string
}