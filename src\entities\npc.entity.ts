import {Entity , Column ,PrimaryGeneratedColumn,Index, OneToMany, JoinTable, ManyToMany, ManyToOne, JoinColumn} from 'typeorm'
//物品实体
import { GoodEntity } from './goods.entity'
//武将实体
import { NpcGeneralEntity } from './npcGeneral.entity'
import { NpcGoodsEntity } from './npcGoods.entity'
//npc 信息
@Entity()
export class NpcEntity {
  
  @PrimaryGeneratedColumn()
  id:number
// 名称
  @Column({ type: 'varchar', length: 20})
  name:string
  //描述
  @Column({ type: 'varchar', length: 100, nullable: true })
  desc:string
  //直接攻击 0展示详情页 1直接攻击
  @Column({ type: 'int',default:0})
  directAttack:number
  //可攻击 0 不可攻击 1可攻击
  @Column({ type: 'int',default:0})
  canAttack:number
   //梯队 默认第二梯队
  @Column({ type: 'int',default:2 })
  teamOrder:number
  //可购买物品  0不可购买 1可购买
  @Column({ type: 'int',default:0})
  canBuy:number
  //攻击 防御 体力  当前体力 默认显示第一个怪的数据
  //所属地图id
  @Column({ type: 'int'})
  @Index()
  gpsId:number
  //间隔时间 杀死后间隔显示时间 秒
  @Column({ type: 'int',default:0 })
  intervalTime:number
  //刷新时间是否显示  2显示 1不显示
  @Column({ type: 'int', default:1 })
  showIntervalTime:number
  //类型 p个人 t队伍 a全区
  @Column({ type: 'varchar',default:'p' })
  type:string
  @OneToMany(() => NpcGeneralEntity, generalEntity => generalEntity.npc, {
    cascade: true,
    onDelete: 'CASCADE'
  })
  generals: NpcGeneralEntity[];
  @OneToMany(() => NpcGoodsEntity, npcGoodsEntity => npcGoodsEntity.npc, {
    cascade: true,
    onDelete: 'CASCADE'
  })
  npcGoods: NpcGoodsEntity[];//掉落物品
  //join关联物品表
  @ManyToMany(() => GoodEntity, good => good.npcs, {
    cascade: true,
    onDelete: 'CASCADE'
  })
  @JoinTable()
  goods: GoodEntity[];
  //潜能
  @Column({ type: 'int',default:0 })
  potential:number
  // 银两
  @Column({ type: 'int',default:0})
  gold:number
  //粮草
  @Column({ type: 'int',default:0 })
  food:number
  //木材
  @Column({ type: 'int',default:0 })
  wood:number
  //石料
  @Column({ type: 'int',default:0 })
  stone:number
  //生铁
  @Column({ type: 'int',default:0})
  iron:number
  //配置
  @Column({ type: 'varchar',default:'' })
  config:string
  //副本名称 最后一个怪才写
  @Column({ type: 'varchar',length:20, nullable: true })
  dungeonName:string
  //声望
  @Column({ type: 'int', default:0 })
  reputation:number
  //传送到地图id
  @Column({ type: 'int', default:0 })
  mapId:number
}