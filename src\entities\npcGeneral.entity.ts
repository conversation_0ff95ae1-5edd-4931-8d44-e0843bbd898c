import {Entity , Column ,PrimaryGeneratedColumn,CreateDateColumn,UpdateDateColumn, Index,ManyToOne, OneToOne, OneToMany, JoinColumn, BeforeUpdate} from 'typeorm'

//导入技能
import { NpcSoldierEntity } from './npcSoldier.entity'
import { NpcEntity } from './npc.entity'
//武将实体
@Entity()
export class NpcGeneralEntity{
  
  @PrimaryGeneratedColumn()
  id:number
  //名称
  @Column({ type: 'varchar', length: 20})
  name:string
  //等级
  @Column({ type: 'int',default:1 })
  level:number
  //魄力
  @Column({ type: 'int',default:0 })
  potential:number
  //攻击力
  @Column({ type: 'int',default:20 })
  attack:number
  //防御力
  @Column({ type: 'int',default:40 })
  defense:number
  //体力
  @Column({ type: 'int'})
  hp:number

  // 当前体力  0死亡
  @Column({ type: 'int'})
  hpNow:number
  //近身  1234 最大4
  @Column({ type: 'int',default:0 })
  near:number
  //梯队 默认第二梯队
  @Column({ type: 'int',default:2 })
  teamOrder:number
  //排序
  @Column({ type: 'int',default:0 })
  sort:number
// 带兵人口:5/5
  //当前人口数量
  @Column({ type: 'int',default:0 })
  count:number
  //可带兵总人口数量
  @Column({ type: 'int',default:11 })
  countAll:number
  //人口数量
//   带兵数量:5
  @Column({ type: 'int',default:0 })
  population:number
  
//受到的伤害
  @Column({ type: 'int',default:0 })
  lastHurt:number
  //战斗快捷键1  1 技能 2逃跑 3使用物品  类型&名称&id
  @Column({ type: 'varchar',default:'快捷' })
  cmd1:string
  @Column({ type: 'varchar',default:'快捷' })
  cmd2:string
  @Column({ type: 'varchar',default:'快捷' })
  cmd3:string
  @Column({ type: 'varchar',default:'快捷'})
  cmd4:string
  @Column({ type: 'varchar',default:'快捷'  })
  cmd5:string
  @Column({ type: 'varchar',default:'快捷'  })
  cmd6:string
  @Column({ type: 'varchar',nullable:true })
  wuqiStr:string
  @Column({ type: 'varchar',nullable:true })
  fangjuStr:string
  @Column({ type: 'varchar',default:'普通攻击' })
  skillStr:string
  // 战斗状态 0未战斗 1战斗中
  @Column({ type: 'int', default: 0 })
  fightStatus: number;
  @Column({ type: 'int',nullable:true })
  userId:number
  @ManyToOne(() => NpcEntity, npc => npc.generals)
  npc: NpcEntity;
  @OneToMany(() => NpcSoldierEntity, soldierEntity => soldierEntity.general,{
    cascade: true, // 启用级联操作
    onDelete: 'CASCADE'
  })
  soldiers: NpcSoldierEntity[];
  //时间
  @CreateDateColumn({name:'create_date',type: 'timestamp'})
    createDate: Date;
}