import {Entity , Column ,PrimaryGeneratedColumn,Index, ManyToOne} from 'typeorm'
import { NpcEntity } from './npc.entity'
//npc掉落物品表
@Entity()
export class NpcGoodsEntity{
  
  @PrimaryGeneratedColumn()
  id:number
// 数量
  @Column({ type: 'int'})
  count:number
  //1到100
  //几率
  @Column({ type: 'int'})
  min:number
  @Column({ type: 'int'})
  max:number
  //物品名称
  @Column({ type: 'varchar',length:10})
  goodName:string
  //物品id
  @Column({ type: 'int',nullable:true})
  goodId:number
  @ManyToOne(() => NpcEntity, npcEntity => npcEntity.npcGoods)
  npc: NpcEntity;
  //注释：1到100产生随机数，如果随机数在min到max之间，则掉落物品
}