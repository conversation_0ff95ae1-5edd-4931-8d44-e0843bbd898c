import { NpcGeneralEntity } from './npcGeneral.entity';
import {Entity , Column ,PrimaryGeneratedColumn,CreateDateColumn,UpdateDateColumn, Index,ManyToOne, OneToOne, OneToMany, JoinColumn} from 'typeorm'

//导入技能
//士兵实体
@Entity()
export class NpcSoldierEntity{
  
  @PrimaryGeneratedColumn()
  id:number
  //名称
  @Column({ type: 'varchar', length: 20})
  name:string
  //等级
  @Column({ type: 'int',default:1 })
  level:number
  //攻击力
  @Column({ type: 'int',default:20 })
  attack:number
  //防御力
  @Column({ type: 'int',default:20 })
  defense:number
  //体力
  @Column({ type: 'int',default:520 })
  hp:number
  //梯队 默认第一梯队  1-5
  @Column({ type: 'int',default:1 })
  teamOrder:number
  //占用人口
  @Column({ type: 'int',default:1 })
  population:number
  //士兵数量
  @Column({ type: 'int',default:0 })
  count:number
//受到的伤害
  @Column({ type: 'int',default:0 })
  lastHurt:number
  //上次攻击死亡的个数
  @Column({ type: 'int',default:0 })
  lastDeadCount:number
  //上次攻击产生的多余伤害
  @Column({ type: 'int',default:0 })
  lastDamage:number
  //模板类型
  @Column({ type: 'varchar', length: 20,default:'bubing' })
  type:string
  //用户id 可以为空
  @Column({ type: 'int',nullable:true })
  userId:number
  @Column({ type: 'varchar'})
  wuqiStr:string
  @Column({ type: 'varchar'})
  fangjuStr:string
  @Column({ type: 'varchar',default:'普通攻击'  })
  skillStr:string
  //武将id
  @Column({ type: 'int',nullable:true })
  pipId:number
  @ManyToOne(() => NpcGeneralEntity, generalEntity => generalEntity.soldiers)
  general: NpcGeneralEntity;
  //时间
  @CreateDateColumn({name:'create_date',type: 'timestamp'})
    createDate: Date;
}