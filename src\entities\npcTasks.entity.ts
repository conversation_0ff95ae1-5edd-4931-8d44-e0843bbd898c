import {<PERSON><PERSON><PERSON> , Column ,PrimaryGeneratedColumn, OneToMany, ManyToMany, JoinColumn, OneToOne} from 'typeorm'
import { TaskEntity } from './task.entity'
import { RoleEntity } from './role.entity'
import { RoleTasksEntity } from './roleTasks.entity'
//npc 信息
@Entity()
export class NpcTasksEntity {
  @PrimaryGeneratedColumn()
  id:number
// 名称
  @Column({ type: 'varchar', length: 20})
  name:string
  @OneToMany(() => TaskEntity, task => task.npcTask)
  tasks: TaskEntity[]
  // @OneToMany(() => RoleTasksEntity,  roleTask=> roleTask.npcTask)
  // roleTasks: RoleTasksEntity[]
  @ManyToMany(() => RoleEntity)
  roles: RoleEntity[]
  //第一步的任务id
  @Column({ type: 'int'})
  taskId:number
}