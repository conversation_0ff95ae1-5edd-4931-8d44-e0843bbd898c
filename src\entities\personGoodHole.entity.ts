
import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm'
//玩家物品孔表 一件装备上有多个孔
@Entity()
export class PersonGoodsHoleEntity {

  @PrimaryGeneratedColumn()
  id: number
  @Column({ type: 'int', comment: '装备id'})
  @Index()
  equipId: number
  //孔名称
  @Column({ type: 'varchar', length: 20 })
  holeName: string
  //宝石id
  @Column({ type: 'int', nullable:true })
  gemId: number
  //宝石名称
  @Column({ type: 'varchar', length: 20,nullable:true })
  gemName: string
}