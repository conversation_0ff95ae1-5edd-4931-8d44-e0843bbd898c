import { GoodEntity } from './goods.entity';
import { Entity, Column, PrimaryGeneratedColumn, Index, ManyToOne } from 'typeorm'
//玩家物品表
@Entity()
export class PersonGoodsEntity {

  @PrimaryGeneratedColumn()
  id: number
  // 数量
  @Column({ type: 'int', unsigned: true })
  count: number
  //玩家id
  @Column({ type: 'int' })
  @Index()
  userId: number

  //已装备数量
  @Column({ type: 'int', default: 0 })
  usedNum: number
  @Column({ type: 'int', comment: '攻击力',nullable:true  })
  attack: number
  @Column({ type: 'int', comment: '防御力',nullable:true  })
  defense: number
  @Column({ type: 'int', comment: '生命',nullable:true  })
  hp: number
  @Column({ type: 'int', comment: '宝石攻击力',nullable:true  })
  attackGem: number
  @Column({ type: 'int', comment: '宝石防御力',nullable:true  })
  defenseGem: number
  @Column({ type: 'int', comment: '宝石生命',nullable:true  })
  hpGem: number
  @Column({ type: 'int', comment: '耐久',default:99999 })//999表示永不磨损 0表示磨损完毕
  durability: number
  @Column({ type: 'int', comment: '装备等级',nullable:true})
  uselevel: number
  @Column({ type: 'int', comment: '是否绑定',nullable:true  })//1绑定 2不绑定
  isBind: number
  @ManyToOne(() => GoodEntity, goodEntity => goodEntity.id)
  good: GoodEntity;
  //挂售状态
  @Column({ type: 'bigint', comment: '挂售价格',nullable:true })//挂售价格
  price: number
  @Column({ type: 'int', comment: '挂售价格',default:1 })//挂售状态 1未挂售 2已挂售
  @Index()
  sellStatus: number
}