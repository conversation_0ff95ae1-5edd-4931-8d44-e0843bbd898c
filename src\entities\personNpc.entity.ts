import {Entity , Column ,PrimaryGeneratedColumn,Index, CreateDateColumn} from 'typeorm'
//玩家打npc死亡凭证
@Entity()
export class PersonNpcEntity{
  
  @PrimaryGeneratedColumn()
  id:number

  @Column({ type: 'int',comment:'数量'})
  count:number
  //玩家id
  @Column({ type: 'int',comment:'玩家id'})
  @Index()
  userId:number
  //npc名称
  @Column({ type: 'varchar',length:15,comment:'npc名称'})
  @Index()
  npcName:string
  //创建时间
  @CreateDateColumn({ name: 'create_date', type: 'timestamp',comment:'创建时间'})
  createDate: Date;
}