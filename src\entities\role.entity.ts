import {Entity , Column ,PrimaryGeneratedColumn, Index, ManyToMany, JoinTable, OneToMany} from 'typeorm'
import { RoleTasksEntity } from './roleTasks.entity'
//人物角色表
@Entity()
export class RoleEntity{
  
  @PrimaryGeneratedColumn()
  id:number

  @Column({ type: 'varchar', length: 20 })
  @Index()
  name:string

  @Column({ type: 'int',default:1 })
  gender:number
  // 等级
  @Column({ type: 'int',default:1 })
  level:number
  // 地图id
  @Column({ type: 'int',default:5050 })
  gpsId:number
  // 任务id
  @Column({ type: 'int' ,default:1 })
  taskId:number
  // 已发放任务奖励的id
  @Column({ type: 'int' ,default:0 })
  taskReward:number
  //显示的任务id
  @Column({ type: 'int' ,default:0})
  showTaskId:number
  // 账号id
  @Column()
  @Index()
  userId:number
  //分区id
  @Column()
  @Index()
  areaId:number
  // 临时sid
  @Column({ type: 'varchar', length: 32,nullable: true})
  sid:string
  

  //总人口
  @Column({ type: 'int',default:5 })
  count:number
  //当前人口
  @Column({ type: 'int',default:0 })
  currentCount:number
  //官职
  @Column({ type: 'varchar',default:'平民' })
  office:string
  //魄力
  @Column({ type: 'int',default:0 })
  soul:number
  //声望
  @Column({ type: 'int',default:0 })
  prestige:number
  //潜能 无符号
  @Column({ type: 'bigint',default:0,unsigned: true })
  potential:number
  // 银两
  @Column({ type: 'bigint',default: 1550,unsigned: true })
  gold:number
  //粮草
  @Column({ type: 'bigint',default:1500,unsigned: true })
  food:number
  //木材
  @Column({ type: 'bigint',default:1500,unsigned: true })
  wood:number
  //石料
  @Column({ type: 'bigint',default:1500,unsigned: true })
  stone:number
  //生铁
  @Column({ type: 'bigint',default:1500,unsigned: true })
  iron:number
  //当前负重
  @Column({ type: 'int',nullable: true,default: 0})
  nowWeight:number
  //最大负重
  @Column({ type: 'int',default:85 })
  maxWeight:number
  //升级状态  1未升级 2升级中
  @Column({ type: 'int',default:1 })
  upgradeStatus:number
  //多对多关联task实体
  @OneToMany(() => RoleTasksEntity, (roleTasks) => roleTasks.role)
  roleTasks: RoleTasksEntity[]
  //创建时间
  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;
  //更新时间
  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updatedAt: Date;
}