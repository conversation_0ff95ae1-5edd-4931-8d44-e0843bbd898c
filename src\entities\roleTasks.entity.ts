import {Entity , Column ,PrimaryGeneratedColumn, Index, ManyToMany, JoinTable, OneToMany, OneToOne, JoinColumn, ManyToOne} from 'typeorm'
import { RoleEntity } from './role.entity'
// 定义一个枚举类型来表示状态
import {NpcTaskStatus} from '../utils/types'
//人物角色任务表
@Entity()
export class RoleTasksEntity{
  
  @PrimaryGeneratedColumn()
  id:number
  
  //状态
  @Column({
    type: 'enum',
    enum: NpcTaskStatus,
    default: NpcTaskStatus.IN_PROGRESS
  })
  status:NpcTaskStatus
  //关联角色
  @ManyToOne(() => RoleEntity,(role)=>role.roleTasks)
  role:RoleEntity
  //任务id
  @Column()
  taskId:number
  //步骤id
  @Column()
  stepId:number
  @Column({nullable:true})
  stepId1:number
  //任务名称
  @Column()
  taskName:string

}