import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm'
//好友
@Entity()
export class FriendsEntity {
    @PrimaryGeneratedColumn()
    id: number
    //状态 1 正常 2 黑名单
    @Column({ type: 'int', comment: '状态',default:1 })
    status: number
    //用户id
    @Column({ type: 'varchar', comment: '用户id' })
    @Index()
    userId: string
    //好友id
    @Column({ type: 'varchar', comment: '好友id' })
    @Index()
    friendId: string
    //好友名称
    @Column({ type: 'varchar', comment: '好友名称',nullable:true })
    friendName: string
    //创建时间
    @Column({ type: 'datetime', comment: '创建时间',default:()=>'CURRENT_TIMESTAMP' })
    createTime: Date
}