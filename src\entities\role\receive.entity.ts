import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm'
//领取物品
@Entity()
export class ReceiveEntity {
    @PrimaryGeneratedColumn()
    id: number
    //用户id
    @Column({ type: 'int', comment: '用户id' })
    @Index()
    userId: number
    //领取名称
    @Column({ type: 'varchar', comment: '领取名称' })
    @Index()
    name: string
    //创建时间
    @Column({ type: 'datetime', comment: '创建时间',default:()=>'CURRENT_TIMESTAMP' })
    createTime: Date
}