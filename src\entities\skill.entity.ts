import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, OneToOne, OneToMany } from 'typeorm'
//导入武将实体
import { SoldierEntity } from './soldier.entity';
import { GeneralEntity } from './general.entity'
//导入npc
//技能实体
@Entity()
export class SkillEntity {

  @PrimaryGeneratedColumn()
  id: number
  //名称
  @Column({ type: 'varchar', length: 20 })
  name: string
  // 伤害系数
  @Column({ type: 'int', default: 2 })
  damage: number
  // 攻击距离
  @Column({ type: 'int', default: 1 })
  distance: number
  // 伤害数量
  @Column({ type: 'int', default: 1 })
  damageNum: number
  // 兵器类型  //21:刀  22:剑  23:棍  24:斧  25:锤  26:枪  0 任意
  @Column({ type: 'int', default: 0 })
  weaponType: number
  //技能类型  1技能  2必杀  普通攻击是0
  @Column({ type: 'int', default: 1 })
  type: number
  //时间间隔
  @Column({ type: 'int', default: 0 })
  interval: number
  //是否默认技能 1是默认 
  @Column({ type: 'int', default: 0 })
  isDefault: number
  //多对一管理武将实体
  @ManyToOne(type => GeneralEntity, general => general.skills)
  general: GeneralEntity
  //多对一管理士兵实体
  @ManyToOne(type => SoldierEntity, soldier => soldier.skills)
  soldier: SoldierEntity
  //时间
  @CreateDateColumn({ name: 'create_date', type: 'timestamp' })
  createDate: Date;
}