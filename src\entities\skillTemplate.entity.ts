import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, OneToOne, OneToMany } from 'typeorm'
@Entity()
export class SkillTemplateEntity {
  //名称/伤害系数/攻击距离/攻击数量/武器类型/恢复时间
  @PrimaryGeneratedColumn()
  id: number
  //名称
  @Column({ type: 'varchar', length: 20 })
  //唯一
  @Index({ unique: true })
  name: string
  // 伤害系数
  @Column({ type: 'int', default: 2 })
  damage: number
  // 攻击距离
  @Column({ type: 'int', default: 1 })
  distance: number
  // 伤害数量
  @Column({ type: 'int', default: 1 })
  damageNum: number
  // 兵器类型  //21:刀  22:剑  23:棍  24:斧  25:锤  26:枪  0 任意
  @Column({ type: 'int', default: 0 })
  weaponType: number
  //时间间隔  0表示不是必杀  
  @Column({ type: 'int', default: 0 })
  interval: number
  //时间
  @CreateDateColumn({ name: 'create_date', type: 'timestamp' })
  createDate: Date;
}