import {Entity , Column ,PrimaryGeneratedColumn,CreateDateColumn,UpdateDateColumn, ManyToMany, ManyToOne} from 'typeorm'
import { RoleEntity } from './role.entity'
import { NpcTasksEntity } from './npcTasks.entity'
//任务表
@Entity()
export class TaskEntity{
  
  @PrimaryGeneratedColumn()
  id:number

  //任务名称
  @Column({ type: 'varchar',length:30,default:'主线任务' })
  name:string
// 任务详情
  @Column({ type: 'text',nullable:true })
  content:string
  //任务npc
  @Column({ type: 'int',nullable:true })
  npcId:number
  //触发场景id
  @Column({ type: 'int',nullable:true})
  gpsId:number
  //上级任务id
  @Column({ type: 'int',nullable:true})
  taskIdPrev:number
  //下级任务id
  @Column({ type: 'int',nullable:true})
  taskIdNext:number
  //条件   1建设庄院  2物品  3怪  0是没有条件
  @Column({ type: 'int',default:0 })
  condition:number
  //物品
  @Column({ type: 'int',nullable:true})
  goodsId:number
  //物品数量
  @Column({ type: 'int',nullable:true})
  goodsNum:number
  //怪物名称
  @Column({ type: 'text',nullable:true})
  monsterName:string
  //怪物数量
  @Column({ type: 'int',nullable:true})
  monsterNum:number
  //银两
  @Column({ type: 'int',default:0})
  gold:number
  //潜能
  @Column({ type: 'int',default:0})
  potential:number
  //粮草
  @Column({ type: 'int',default:0})
  food:number
  //木材
  @Column({ type: 'int',default:0})
  wood:number
  //石料
  @Column({ type: 'int',default:0})
  stone:number
  //生铁
  @Column({ type: 'int',default:0})
  iron:number
  //奖励物品id
  @Column({ type: 'int',nullable:true})
  rewardId:number
  //奖励物品数量
  @Column({ type: 'int',nullable:true})
  rewardNum:number 
  //类型 0主线 1支线
  @Column({ type: 'int',default:0 })
  type:number
  @ManyToOne(()=>NpcTasksEntity,(npmTask)=>npmTask.tasks)
  npcTask:NpcTasksEntity
}

//发布任务
//校验任务
//下个任务  发放奖励