import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm'
import { TeamsEntity } from './teams.entity';
//队伍角色
@Entity()
@Index(['team', 'userId'],{unique:true})  // 添加联合索引
export class TeamJoinEntity {
    @PrimaryGeneratedColumn()
    id: number 
    
    @Column({})
    @Index()
    userId:number
    @ManyToOne(() => TeamsEntity, team => team.teamJoins)
    team: TeamsEntity
    @Column()
    userName:string
    //状态  1申请中 和2 已同意 3 已拒绝
    @Column({default:1})
    @Index()
    status: number
    //创建时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date; 
}