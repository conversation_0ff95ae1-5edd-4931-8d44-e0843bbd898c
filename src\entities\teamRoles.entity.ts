import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToOne, Index } from 'typeorm'
import { TeamsEntity } from './teams.entity';
import {IsCaptain} from '../utils/types'
//队伍角色
@Entity()
export class TeamRolesEntity {
    @PrimaryGeneratedColumn()
    id: number
    @ManyToOne(() => TeamsEntity, team => team.teamRoles)
    team: TeamsEntity
    @Column({type:'int'})
    @Index()
    userId:number
    @Column({type:'varchar',length:20})
    userName:string
    //是否队长
    @Column({type: 'enum',enum: IsCaptain,default: IsCaptain.NO})
    isCaptain:IsCaptain
    //潜能
    @Column({ type: 'int',default:0 })
    potential:number
    //创建时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;
}