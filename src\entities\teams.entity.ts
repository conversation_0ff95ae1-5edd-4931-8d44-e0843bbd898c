import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany, JoinColumn } from 'typeorm'
import { TeamRolesEntity } from './teamRoles.entity';
import { IsTrue } from 'src/utils/types';
import { TeamJoinEntity } from './teamJoin.entity';

@Entity()
export class TeamsEntity {
    @PrimaryGeneratedColumn()
    id: number
    //队伍名称
    @Column({ type: 'varchar', length: 20 })
    name: string
    @OneToMany(() => TeamRolesEntity, teamRoles => teamRoles.team, { cascade: true })
    teamRoles: TeamRolesEntity[]
    @OneToMany(() => TeamJoinEntity, teamJoins => teamJoins.team, { cascade: true })
    teamJoins: TeamJoinEntity[]
    //禁止加入
    @Column({type: 'enum',enum: IsTrue,default: IsTrue.NO})
    stopEnterTeam: IsTrue
    //创建时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;
}