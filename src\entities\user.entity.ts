import { Entity, Column, PrimaryGeneratedColumn, Index, Unique } from 'typeorm';
//用户账号
@Entity()
export class UserEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    @Index()
    userName: string;

    @Column()
    password: string;
    //是否激活
    @Column({ default: true })
    isActive: boolean;
    
    @Column()
    @Index()
    sign:string

    //创建时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;
    //更新时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    updatedAt: Date;
}