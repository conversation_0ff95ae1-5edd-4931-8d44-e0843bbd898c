import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { GeneralEntity } from 'src/entities/general.entity';
import { BlessEntity } from 'src/entities/general/bless.entity';
import { PersonGoodsEntity } from 'src/entities/personGoods.entity';
import { SoldierEntity } from 'src/entities/soldier.entity';
import { CommonService } from 'src/middleware/common.service';
import { DataSource, EntityManager, Equal, Repository, Column, MoreThan, In } from 'typeorm';
import { GcmdService } from '../gcmd.service';
import { RedisService } from 'src/middleware/redis.service';
import { RoleTasksEntity } from 'src/entities/roleTasks.entity';
import { generalAttrHandel, generalEquip, mountConfig, mountEquip, taskListConfig } from 'src/utils/config1';
import { GeneralService } from '../general.service';
import { RoleEntity } from 'src/entities/role.entity';
import { getGoodType, getHpByLevel, hunpofn, jingmaiFn, soldierTemplate } from 'src/utils/config';
import { TeamsEntity } from 'src/entities/teams.entity';
import { FriendsEntity } from 'src/entities/role/friends.entity';
import { ManorEventEntity } from 'src/entities/manorEvent.entity';
import { MountEntity } from 'src/entities/general/mount.entity';
import { FightEntity } from 'src/entities/fight.entity';
import { PersonGoodsHoleEntity } from 'src/entities/personGoodHole.entity';
@Injectable()
export class OtherSeaService {
    private readonly serviceName = 'otherSeaService'
    constructor(
        private readonly commonService: CommonService,
        private eventEmitter: EventEmitter2,
        private redisService: RedisService,

        @Inject(forwardRef(() => GcmdService))private readonly gcmdService: GcmdService,
        @Inject(forwardRef(() => GeneralService))private readonly generalService: GeneralService,

        @InjectRepository(GeneralEntity) private readonly generalEntity: Repository<GeneralEntity>,
        @InjectRepository(ManorEventEntity) private readonly manorEventEntity: Repository<ManorEventEntity>,
        @InjectRepository(MountEntity) private readonly mountEntity: Repository<MountEntity>,
        @InjectRepository(FightEntity) private readonly fightEntity: Repository<FightEntity>,
        @InjectRepository(PersonGoodsHoleEntity) private readonly personGoodsHoleEntity: Repository<PersonGoodsHoleEntity>,
        
        @InjectRepository(TeamsEntity) private readonly teamsEntity: Repository<TeamsEntity>,
        @InjectRepository(RoleEntity) private readonly roleEntity: Repository<RoleEntity>,
        @InjectRepository(SoldierEntity) private readonly soldierEntity: Repository<SoldierEntity>,
        @InjectRepository(BlessEntity) private readonly blessEntity: Repository<BlessEntity>,
        @InjectRepository(PersonGoodsEntity) private readonly personGoodsEntity: Repository<PersonGoodsEntity>,
        @InjectRepository(RoleTasksEntity) private readonly roleTasksEntity: Repository<RoleTasksEntity>,
        @InjectRepository(FriendsEntity) private readonly friendsEntity: Repository<FriendsEntity>,
        private dataSource: DataSource,
    ) { }
    //我的页面
    async myStatus(sid: string, cmd: number, userId: string,{targetUserId,type=''}) {
        //获取用户信息
        let userInfo: RoleEntity =await this.roleEntity.findOneBy({ id: targetUserId })
        let friends=await this.friendsEntity.findOne({where:{userId,friendId:targetUserId}})
        let isFriend=friends&&friends.status==1?true:false
        let isBlack=friends&&friends.status==2?true:false
        if(type=='handelFriend'){
            if(isFriend){
                let res=await this.friendsEntity.delete(friends.id)
                console.log('删除好友',res);
            }else{
                if(friends){
                    friends.status=1
                    await this.friendsEntity.save(friends)
                }else{
                    let friendInfo=new FriendsEntity()
                    friendInfo.userId=userId
                    friendInfo.friendId=targetUserId
                    friendInfo.friendName=userInfo.name
                    friendInfo.status=1
                    await this.friendsEntity.save(friendInfo)
                }
            }
            friends=await this.friendsEntity.findOne({where:{userId,friendId:targetUserId}})
        }
        if(type=='handelBlack'){
            if(isBlack){
                await this.friendsEntity.delete(friends)
            }else{
                //加入黑名单
                if(isFriend){
                    friends.status=2
                    await this.friendsEntity.save(friends)
                }else{
                    let friendInfo=new FriendsEntity()
                    friendInfo.userId=userId
                    friendInfo.friendId=targetUserId
                    friendInfo.friendName=userInfo.name
                    friendInfo.status=2
                    await this.friendsEntity.save(friendInfo)
                }
            }
            friends=await this.friendsEntity.findOne({where:{userId,friendId:targetUserId}})
        }
        isFriend=friends&&friends.status==1?true:false
        isBlack=friends&&friends.status==2?true:false
        let pageTitle='状态'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {targetUserId},pageTitle,'myStatus') 
        let myuserInfo: RoleEntity =await this.roleEntity.findOneBy({ id: Number(userId) })
        let userTeam=await this.teamsEntity.findOne({where:{teamRoles:{userId:targetUserId}}})
        let msg=''
        let upgradeStr='',str1='';
        let infoRedis=await this.redisService.hget('user'+targetUserId,'userInfo')
        let content=''
        if(!infoRedis){
            content=`${userInfo.name}${isFriend?'[好友]':''}${isBlack?'[黑]':''}(离线)<br/>
            性别:${userInfo.gender === 0 ? '男' : '女'}<br/>
            等级:${userInfo.level}<br/>
            官职:平民<br/>
            魄力:${userInfo.soul}<br/>
            人口:${userInfo.currentCount}/${userInfo.count}<br/>
            <a href="${this.commonService.seturlOther(params, urlObj, `${isFriend?'删除好友':'加为好友'}`, 'myStatus', {targetUserId,type:'handelFriend'},this.serviceName)}">${isFriend?'删除好友':'加为好友'}</a>
            <a href="${this.commonService.seturlOther(params, urlObj, `${isBlack?'删除黑名单':'加黑名单'}`, 'myStatus', {targetUserId,type:'handelBlack'},this.serviceName)}">${isBlack?'删除黑名单':'加黑名单'}</a>
            <br/>
            ${backRouter}`
            let users=JSON.parse(await this.redisService.hget('mapInfo','users'+userInfo.gpsId))
            if(users?.length){
                users=users.filter(item=>item.id!=targetUserId)
                await this.redisService.hset('mapInfo','users'+userInfo.gpsId,JSON.stringify(users))//清除用户在线状态
            }
        }else{
            let generalList=await this.generalEntity.find({where:{userId:targetUserId,near:MoreThan(0)},order:{near:'ASC'}})
            let generalStr=''
            generalList.forEach(item=>{
                generalStr+=`<a href="${this.commonService.seturlOther(params, urlObj, '武将梯队', 'armyGeneral', { generalId: item.id },'general')}">${item.name}部</a>，`
                generalStr&&(generalStr=generalStr.slice(0,-1))
            })
            str1=`<a href="${this.commonService.seturlOther(params, urlObj, '发送消息', 'chatPage', {chattype:4,targetID:targetUserId,hidden:true},'goods')}">发送消息</a></br>
            <a href="${this.commonService.seturlOther(params, urlObj, '物品列表', 'useGoods', {targetUserId},'general')}">赠送物品</a> 
            <a href="${this.commonService.seturlOther(params, urlObj, '赠送银两', 'formInit', {obj:{targetUserId,title:`你身上有${myuserInfo.gold}银两，请输入你要赠送的数量`,type:'number',label:'银两数量'}},'generalother')}">赠送银两</a><br/>
            <a href="${this.commonService.seturlOther(params, urlObj, '购买物品', 'goodsList', {sellStatus:2,targetUserId},'goods')}">购买物品</a><br/>
            <a href="${this.commonService.seturlOther(params, urlObj, `${isFriend?'删除好友':'加为好友'}`, 'myStatus', {targetUserId,type:'handelFriend'},this.serviceName)}">${isFriend?'删除好友':'加为好友'}</a>
            <a href="${this.commonService.seturlOther(params, urlObj, `${isBlack?'删除黑名单':'加黑名单'}`, 'myStatus', {targetUserId,type:'handelBlack'},this.serviceName)}">${isBlack?'删除黑名单':'加黑名单'}</a>
            <br/>
            `
            content = `${msg}
            <p>${userInfo.name}${isFriend?'[友]':''}${isBlack?'[黑]':''}<br/>
            性别:${userInfo.gender === 0 ? '男' : '女'}<br/>
            等级:${userInfo.level}<br/>
            官职:${userInfo.office}<br/>
            潜能:${userInfo.potential}<br/>
            魄力:${userInfo.soul}<br/>
            人口:${userInfo.currentCount}/${userInfo.count}<br/>
            声望:${userInfo.prestige}<br/>
            PK数:0(已关闭)<br/>
            粮草:${userInfo.food} <br/>
            木材:${userInfo.wood} <br/>
            石料:${userInfo.stone} <br/>
            生铁:${userInfo.iron} <br/>
            银两:${userInfo.gold}<br/>
            近身军队:${generalStr}<br/>
            队伍:${userTeam?`<a href="${this.commonService.seturlOther(params, urlObj, '队伍', 'team', {teamId:userTeam.id},'manor')}">${userTeam.name}</a>`:'暂无队伍'}<br/>
            ${str1}
            ${backRouter}`
        }
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj),1800)
        return content;
    }
    //武将详情页面
    async generalInfo(sid: string, cmd: number, userId: string, { generalId}) {
        let generalInfo = await this.generalEntity.findOne({
            where: { id: generalId },
            relations: ['soldiers']
        });
        let msg = '';
        let pageTitle='武将详情'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {generalId},pageTitle,'generalInfo')
        //所带兵种
        let str2 = '';
        generalInfo.soldiers.forEach((item, index) => {
            str2 += `<a href="${this.commonService.seturlOther(params, urlObj, item.name, 'soldierInfo', { generalId, soldierId: item.id },this.serviceName)}">${item.name}</a>,`
        })
        str2&&(str2=str2.slice(0,-1))
        let equipNameStr=''
        for (const key in generalEquip) {
            let item=generalEquip[key]
            if(generalInfo[item.id]&&key!='2'){
                equipNameStr+=generalInfo[item.title]+','
            }
        }
        equipNameStr&&(equipNameStr=equipNameStr.slice(0,-1))
        let fighting = ''
        if (generalInfo.fightStatus == 1) {
            let fightInfo = await this.fightEntity.findOne({
                where: [
                    { status: 1, fightGenerals: { attackGeneralId: String(generalInfo.id) } }
                ], select: ['id']
            })
            if (fightInfo) {
                fighting += `${generalInfo.name}正在战斗中...<a href="${this.commonService.seturlOther(params, urlObj, '战场', 'fighting', { fightingId: fightInfo.id }, this.serviceName)}">查看战况</a></br>`
            }
        }
        let {opened}=jingmaiFn(generalInfo.kaimaiName)
        let {opened : openedHunpo}=hunpofn(generalInfo.hunpoName)
        let mountInfo:any = await this.mountEntity.findOne({ where: { generalId }})||''
        let content = `${msg}${generalInfo.name}${generalInfo.near ? '(近身)' : ''}${generalAttrHandel('general',generalInfo)}<br/>
        等级:${generalInfo.level}<br/>
        魄力:${generalInfo.soul}<br/>
        生命:${generalInfo.hpNow}/${generalInfo.hp}<br/>
        攻击力:${generalInfo.attack}<br/>
        防御力:${generalInfo.defense}<br/>
        已通经脉:${opened}<br/>
        五行魂魄:${openedHunpo}<br/>
        兵器:${generalInfo.bingqiName||''}<br/>
        防具:${equipNameStr}<br/>
        可带兵种:${str2}<br/>
        坐骑:${mountInfo&&(`<a href="${this.commonService.seturlOther(params, urlObj, '坐骑详情', 'mountDetail', {mountId:mountInfo.id},this.serviceName)}">${mountConfig[mountInfo.mountType].name}</a>`)}<br/>
        带领军队:<a href="${this.commonService.seturlOther(params, urlObj, generalInfo.name+'部', 'armyGeneral', {generalId:generalInfo.id},'general')}">${generalInfo.name}部</a><br/>
        带兵数量:${generalInfo.population}<br/>
        带兵人口:${generalInfo.count}/${generalInfo.countAll}<br/>
        <a href="${this.commonService.seturlOther(params, urlObj, '武将装备', 'generalEquipDetail', { generalId },this.serviceName)}">装备</a><br/>
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //武将装备列表页面
    async generalEquipDetail(sid: string, cmd: number, userId: string, { generalId }) {
        let msg = '',pageTitle='装备列表'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {generalId},pageTitle,'generalEquipDetail')
        let generalInfo = await this.generalEntity.findOne({where:{id:Equal(generalId)}})
        let contentStr=''
        Object.keys(generalEquip).forEach(item=>{
            let element=generalEquip[item]
            contentStr+=`${element.name}:`
            if(generalInfo[element.id]){
                contentStr+=`<a href="${this.commonService.seturlOther(params, urlObj, '装备详情', 'wearingEquip', { id:generalInfo[element.id] },'otherSeaService')}">${generalInfo[element.title]}</a>`
            }
            contentStr+='<br/>'
        })
        let content = `${msg}${generalInfo.name}的装备：<br/>
        ${contentStr}
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //装备详情页面
    async wearingEquip(sid: string, cmd: number, userId: string, { id }) {
        let msg = '',pageTitle='装备列表'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {id},pageTitle,'wearingEquip')
        let goodInfo = await this.personGoodsEntity.findOne({ where: { id },relations:['good'] })
        let str=''
        if(goodInfo.attack||goodInfo.attackGem){
            !goodInfo.attack&&(goodInfo.attack=0)
            str+=`攻击力：${goodInfo.attack}`
            !goodInfo.attackGem&&(goodInfo.attackGem=0)
            if(goodInfo.attackGem){
                str+=`+${goodInfo.attackGem}`
            }
            str+='<br/>'
        }
        if(goodInfo.defense||goodInfo.defenseGem){
            !goodInfo.defense&&(goodInfo.defense=0)
            str+=`防御力：${goodInfo.defense}`
            !goodInfo.defenseGem&&(goodInfo.defenseGem=0)
            if(goodInfo.defenseGem){
                str+=`+${goodInfo.defenseGem}`
            }
            str+='<br/>'
        }
        if(goodInfo.hp||goodInfo.hpGem){
            !goodInfo.hp&&(goodInfo.hp=0)
            str+=`生命：${goodInfo.hp}`
            !goodInfo.hpGem&&(goodInfo.hpGem=0)
            if(goodInfo.hpGem){
                str+=`+${goodInfo.hpGem}`
            }
            str+='<br/>'
        }
        let holeInfo=await this.personGoodsHoleEntity.find({where:{equipId:id}})
        let content = `${goodInfo.good.name}<br/>
            ${goodInfo.good.desc ? goodInfo.good.desc + '<br/>' : ''}
            类别:${getGoodType(goodInfo.good.type, goodInfo.good.subType)}<br/>
            重量:${goodInfo.good.weight}<br/>
            价格:${goodInfo.good.price}<br/>
            绑定：${goodInfo.good.bind===1?'绑定':'不绑定'}<br/>
            ${str}
            ${goodInfo.durability==99999?'':`耐久度：${goodInfo.durability}/${goodInfo.good.durability}<br/>`}`

        // 显示宝石孔信息
        if(holeInfo.length){
            let holeStr = ''
            let hasGem = false
            holeInfo.forEach(hole => {
                if(hole.gemId && hole.gemName) {
                    holeStr += `孔${hole.holeName}:${hole.gemName} <a href="${this.commonService.seturlOther(params, urlObj, '宝石详情', 'gemInfo', { holdId: hole.id }, 'otherSeaService')}">详情</a><br/>`
                    hasGem = true
                } else {
                    holeStr += `孔${hole.holeName}:[空]<br/>`
                }
            })
            content += `宝石孔数:${holeInfo.length}<br/>${holeStr}`
        }
        content+=backRouter
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //镶嵌到装备的宝石详情页
    async gemInfo(sid: string, cmd: number, userId: string, { holdId}) {
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {holdId})
        let holeInfo=await this.personGoodsHoleEntity.findOne({where:{id:holdId}})
        let goodInfo = await this.personGoodsEntity.findOne({
            where: { id:holeInfo.equipId },
            relations: ['good'],
        })
        let gemInfo=await this.personGoodsEntity.findOne({where:{id:holeInfo.gemId},relations:['good']})
        let content = `宝石详情：<br/>
            ${goodInfo.good.name}.孔${holeInfo.holeName}<br/>
            已嵌宝石：${holeInfo.gemName}<br/>
            攻击力：${gemInfo.attack}<br/>
            防御力：${gemInfo.defense}<br/>
            生命：${gemInfo.hp}<br/>
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //坐骑页面
    async mountDetail(sid: string, cmd: number, userId: number, { manorId,mountId,accelerate=null }) {
        let msg = '',pageTitle='坐骑详情'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, String(userId),this.serviceName, {mountId},pageTitle,'mountDetail')
        let mountInfo=await this.mountEntity.findOne({ where: { id: Number(mountId) }})
        let equipNameStr=''
        for (const key in mountEquip) {
            let item=mountEquip[key]
            if(mountInfo[item.id]){
                equipNameStr+=mountInfo[item.title]+','
            }
        }
        equipNameStr&&(equipNameStr=equipNameStr.slice(0,-1))
        let info=mountConfig[mountInfo.mountType]
        mountInfo.attack=mountInfo.attack*1+mountInfo.extraAttack*1
        mountInfo.defense=mountInfo.defense*1+mountInfo.extraDefense*1
        let content = `${msg}${info.name} <a href="${this.commonService.seturlOther(params, urlObj, '坐骑详情', 'mountDetail',{mountId},this.serviceName)}">刷新</a><br/>
            等级:${mountInfo.level}<br/>
            增加攻击:${mountInfo.attack}%<br/>
            增加防御:${mountInfo.defense}%<br/>
            装备:${equipNameStr}<br/>
            <a href="${this.commonService.seturlOther(params, urlObj, '坐骑装备', 'mountEquip', { mountId },this.serviceName)}">装备</a><br/>
            ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //坐骑装备列表页面
    async mountEquip(sid: string, cmd: number, userId: number, { mountId}) {
        let msg = '',pageTitle='装备详情'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, String(userId),this.serviceName, {mountId},pageTitle,'mountEquip')
        let mountInfo=await this.mountEntity.findOne({ where: { id: Number(mountId) }})
        let info=mountConfig[mountInfo.mountType]
        let contentStr=''
        Object.keys(mountEquip).forEach(item=>{
            let element=mountEquip[item]
            contentStr+=`${element.name}:`
            if(mountInfo[element.id]){
                contentStr+=`<a href="${this.commonService.seturlOther(params, urlObj, '装备详情', 'wearingEquip', { id:mountInfo[element.id] },this.serviceName)}">${mountInfo[element.title]}</a>`
            }
            contentStr+='<br/>'
        })
        let content = `${info.name}的装备<br/>
            ${contentStr}
            ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //士兵详情页面
    async soldierInfo(sid: string, cmd: number, userId: string, { soldierId}) {
        let msg = '',pageTitle='士兵详情'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {soldierId},pageTitle,'soldierInfo')
        let soldierInfo=await this.soldierEntity.findOne({where:{id:Number(soldierId)},relations:['general']})
        //装备
        let equipNameStr=''
        for (const key in generalEquip) {
            let item=generalEquip[key]
            if(soldierInfo[item.id]&&key!='2'){
                equipNameStr+=soldierInfo[item.title]+','
            }
        }
        equipNameStr&&(equipNameStr=equipNameStr.slice(0,-1))
        //其他属性处理
        const otherMsgFn=()=>{
            let otherMsg = '';
            if(soldierInfo.raguar){
                if(soldierInfo.extremeRaguar){
                    otherMsg=soldierInfo.extremeRaguar>=100?'[极度狂暴]':`[极度狂暴:${soldierInfo.extremeRaguar}/100]`;
                }else{
                    otherMsg=soldierInfo.raguar>=100?'[狂暴]':`[狂暴:${soldierInfo.raguar}/100]`;
                }
            }
            if(soldierInfo.jingang){
                otherMsg+=soldierInfo.jingang>=100?'[金刚]':`[金刚:${soldierInfo.jingang}/100]`;
            }
            return otherMsg;
        }
        let {opened,notOpen}=jingmaiFn(soldierInfo.kaimaiName)
        let {opened : openedHunpo,notOpen:notOpenHunpo}=hunpofn(soldierInfo.hunpoName)
        let blessCount = await this.blessEntity.count({ where: { soldierId, status: 1 } })
        let mountInfo = await this.mountEntity.findOne({ where: { soldierId }})
        let content = `${msg}${soldierInfo.name}${otherMsgFn()}<br/>
            ${soldierTemplate[soldierInfo.type].desc}<br/>
            兵种等级:${soldierInfo.level}<br/>
            所属武将:<a href="${this.commonService.seturlOther(params, urlObj, soldierInfo.general.name, 'generalInfo', { generalId: soldierInfo.general.id },this.serviceName)}">${soldierInfo.general.name}</a><br/>
            攻击力:${soldierInfo.attack}<br/>
            五行攻击:无<br/>
            防御力:${soldierInfo.defense}<br/>
            五行抗性:无<br/>
            兵器:${soldierInfo?.bingqiName||''}<br/>
            防具:${equipNameStr}<br/>
            最大生命:${soldierInfo.hp}<br/>
            占用人口:${soldierInfo.population}<br/>
            士兵数量:${soldierInfo.count}<br/>
            已通经脉:${opened}<br/>
            五行魂魄:${openedHunpo}<br/>
            坐骑:${mountInfo?
                    `<a href="${this.commonService.seturlOther(params, urlObj, '坐骑详情', 'mountDetail', {mountId:mountInfo.id},'generalother')}">${mountConfig[mountInfo.mountType].name}</a>`
                    :''
                }<br/>
                招募代价: 粮草x${soldierTemplate[soldierInfo.type].recruitFood} 银两x${soldierTemplate[soldierInfo.type].recruitGold} 时间x${soldierTemplate[soldierInfo.type].recruitTime}秒<br/>
            <a href="${this.commonService.seturlOther(params, urlObj, soldierInfo.name+'的装备', 'generalEquipDetail', { soldierId },this.serviceName)}">装备</a>(<a href="${this.commonService.seturlOther(params, urlObj, '装备套装介绍', 'suitInfo',{},'general')}">?</a>)<br/>
            ${backRouter}`
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //挂售物品详情页面
    async sellGoodInfo(sid: string, cmd: number, userId: string, { thingId }) {
        let msg = '',pageTitle='挂售物品详情'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {thingId},pageTitle,'sellGoodInfo')
        let goodInfo = await this.personGoodsEntity.findOne({ where: { id: Number(thingId),sellStatus:2 },relations:['good']})
        let userInfo=await this.roleEntity.findOne({where:{id:Number(userId)}})
        let canBuyNum=goodInfo.count
        if(canBuyNum*goodInfo.price>userInfo.gold){
            canBuyNum=Math.floor(userInfo.gold/goodInfo.price)
        }
        if(canBuyNum*goodInfo.good.weight>(userInfo.maxWeight-userInfo.nowWeight)){
            canBuyNum=Math.floor((userInfo.maxWeight-userInfo.nowWeight)/goodInfo.good.weight)
            canBuyNum=canBuyNum>0?canBuyNum:0
        }
        let obj={
            thingId:goodInfo.id,
            label:'购买物品'
        }
        let content = `${goodInfo.good.name}x${goodInfo.count}<br/>
            ${goodInfo.good.desc ? goodInfo.good.desc + '<br/>' : ''}
            类别:${getGoodType(goodInfo.good.type, goodInfo.good.subType)}<br/>
            重量:${goodInfo.good.weight}<br/>
            价格:${goodInfo.good.price}<br/>
            销售价格:${goodInfo.price}<br/>
            <br/>
            你的负重:${userInfo.nowWeight}/${userInfo.maxWeight}<br/>
            你身上有${userInfo.gold}银两<br/>
            请输入你要购买的数量：<br/>
            <form action="${this.commonService.seturlOther(params, urlObj,'提交', 'sellGoodGuaPost', { thingId:obj,obj },this.serviceName)}" method="post">
            数量(最多${canBuyNum}):<input name="count13" type="text" value="${canBuyNum}" format="*N" style="-wap-input-format:*N" maxlength="10"/><br/>
            <input type="submit" value="购买"/></form>
            </form><br/>
        ${backRouter}`
        await this.redisService.hset('user' + userId, 'token', userId, 1800)
        await this.redisService.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //士兵详情页面
    //士兵装备列表页面
    //士兵装备详情页面
    //士兵坐骑页面
    //坐骑装备列表页面
    //坐骑装备详情页面
    //怪物武将梯队页面
}