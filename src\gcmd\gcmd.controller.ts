import { Body, Controller, Get, Inject, Post, Query, Req, Res } from '@nestjs/common';
import { GcmdService } from './gcmd.service';
import { SidDto } from 'src/dto/sid.dto';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';
import { filterRouter,defaultConfig } from 'src/utils/config';
import { ManorService } from './manor.service';
import { GeneralService } from './general.service';
import { GoodsService } from './goods.service';
import { Request,Response } from 'express';
import { FightService } from './fight.service';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { RedisService } from 'src/middleware/redis.service';
import { GeneralotherService } from './generalother.service';
import { OtherSeaService } from './config/otherSea.service';
import {GuildService} from './guild.service'
@Controller('gcmd')
export class GcmdController {
  
  constructor(
    private readonly gcmd: GcmdService,
    private readonly manor: ManorService,
    private readonly general: GeneralService,
    private readonly goods: GoodsService,
    private readonly fight: FightService,
    private readonly guild: GuildService,

    private readonly generalother: GeneralotherService,
    private readonly otherSeaService: OtherSeaService,
    @InjectRedis() private readonly redisService: RedisService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}
  @Get('ceshi')
  async ceshi(){  
    let num=await this.general.moban('',2,'')
    return num
  }
  @Get()
  async getcmd(@Query() query:SidDto){
    // this.logger.error('This is an error message.');
    let startTime = performance.now();
    let userId = await this.redisService.hget('sids',query.sid);
    let routers = JSON.parse(await this.redisService.hget('user'+userId,'routers'));
    let cmdObj=(query.cmd in routers)?routers[query.cmd]:routers[Object.keys(routers)[0]];
    //设置路由数组
    let routerList=JSON.parse(await this.redisService.hget('user'+userId,'routerList'));
    routerList=filterRouter(routerList,cmdObj)
    await this.redisService.hset('user'+userId,'routerList',JSON.stringify(routerList))
    let str='</br>服务:'+cmdObj.service+',方法:'+cmdObj.name+',参数:'+JSON.stringify(cmdObj.params)+'</br>';
    let htmlContent=defaultConfig.htmlHeader+await this[cmdObj.service][cmdObj.name](query.sid,query.cmd,userId,cmdObj.params)+str+defaultConfig.htmlfooter;
    let endTime = performance.now();
    let str1=`脚本运行时间：${endTime - startTime}毫秒</br>`;
    htmlContent+=str1
    // htmlContent+=`看看路由${await this.redisService.hget('user'+userId,'routers')}`
    return htmlContent 
  }
  @Post()
  async gcmdPost(@Query() query,@Body() bodyattr,@Req() request: Request,@Res() response: Response){
    let userId = await this.redisService.hget('sids',query.sid);
    let routers = JSON.parse(await this.redisService.hget('user'+userId,'routers'));
    let token = JSON.parse(await this.redisService.hget('user'+userId,'token'));
    //如果没有token 就get方式重定向到页面
    if(!token)return response.redirect(request.url)
    let cmdObj=(query.cmd in routers)?routers[query.cmd]:routers[Object.keys(routers)[0]];
    let container='' 
    // 发送消息
    if(bodyattr.hasOwnProperty('msg')){
      container=await this.goods.sendMsgPost(query.cmd,query.sid,Number(userId),cmdObj.params.chattype,bodyattr.msg,cmdObj.params.targetID)
      return response.send(defaultConfig.htmlHeader+container+defaultConfig.htmlfooter)
    }
    /**
     * count 购买物品
     * count1 出售物品
     * count2 招募士兵
     */
    //购买物品
    let arr=[
      {name:'count',value:'购买物品',fn:'buyPost',service:'gcmd'},
      {name:'count1',value:'出售物品',fn:'sellPost',service:'goods'},
      {name:'count3',value:'升级',fn:'levelUpPost',service:'general'},
      {name:'count4',value:'士兵挂出销售',fn:'soldierSellPost',service:'generalother'},
      {name:'count5',value:'调离士兵',fn:'buchongSoldierPost',service:'generalother'},
      {name:'count6',value:'补充士兵',fn:'buchongSoldierPost',service:'generalother'},
      {name:'count7',value:'调整武将顺序',fn:'adjustGeneralPost',service:'generalother'},
      {name:'count8',value:'坐骑挂出销售',fn:'mountSellPost',service:'manor'},
      {name:'count9',value:'物品挂出销售',fn:'sellGoodGuaPost',service:'goods'},
      {name:'count2',value:'招募士兵',fn:'recruitSoldiersPost',service:'general'},
      {name:'count10',value:'战场设置',fn:'battleSettingPost',service:'generalother'},
      {name:'count11',value:'查找玩家',fn:'searchPlayerPost',service:'generalother'},
      {name:'count12',value:'使用物品',fn:'useNumPagePost',service:'general'},
      {name:'count13',value:'公共',fn:'formSubmit',service:'generalother'},
    ]
    let obj=arr[0]
    arr.forEach(item=>{
      if(bodyattr.hasOwnProperty(item.name))obj=item
    })
    container=await this[obj.service][obj.fn](Number(userId),cmdObj.params.thingId,bodyattr[obj.name],query.cmd,query.sid)
    return response.send(defaultConfig.htmlHeader+container+defaultConfig.htmlfooter)
    // if(bodyattr.hasOwnProperty('count')){
    //   container=await this.gcmd.buyPost(Number(userId),cmdObj.params.thingId,bodyattr.count,query.cmd,query.sid)
    //   return response.send(defaultConfig.htmlHeader+container+defaultConfig.htmlfooter)
    //   // return defaultConfig.htmlHeader+container+defaultConfig.htmlfooter
    // }
    // //出售物品
    // if(bodyattr.hasOwnProperty('count1')){
    //   container=await this.goods.sellPost(Number(userId),cmdObj.params.thingId,bodyattr.count1,query.cmd,query.sid)
    //   return response.send(defaultConfig.htmlHeader+container+defaultConfig.htmlfooter)
    //   // return defaultConfig.htmlHeader+container+defaultConfig.htmlfooter
    // }
    // //招募士兵
    // if(bodyattr.hasOwnProperty('count2')){
    //   container=await this.general.recruitSoldiers(queryattr,bodyattr,rediscmd[queryattr.cmd].params,rediscmd[queryattr.cmd].url)
    //   return response.send(defaultConfig.htmlHeader+container+defaultConfig.htmlfooter)
    // }
  }
}
