import { NpcGoodsEntity } from './../entities/npcGoods.entity';
import { PersonGoodsEntity } from './../entities/personGoods.entity';
import { GoodEntity } from './../entities/goods.entity';
import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { GcmdService } from './gcmd.service';
import { GcmdController } from './gcmd.controller';
import { CheckMiddleware } from 'src/middleware/check.middleware';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MapsEntity } from 'src/entities/maps.entity';
import { RoleEntity } from 'src/entities/role.entity';
import { NpcEntity } from 'src/entities/npc.entity';
import { ManorEntity } from 'src/entities/manor.entity';
import { ManorService } from './manor.service';
import { LogsEntity } from 'src/entities/logs.entity';
import { ManorEventEntity } from 'src/entities/manorEvent.entity';
import { TasksService } from 'src/tasks/tasks.service';
import { GeneralEntity } from 'src/entities/general.entity';
import { GeneralService } from './general.service';
import { SoldierEntity } from 'src/entities/soldier.entity';
import { SkillEntity } from 'src/entities/skill.entity';
import { TaskEntity } from 'src/entities/task.entity';
import { GoodsService } from './goods.service';
import { CommonService } from 'src/middleware/common.service';
import { ChatEntity } from 'src/entities/chat.entity';
import { EquipEntity } from 'src/entities/equip.entity';
import { FightService } from './fight.service';
import { FightEntity } from 'src/entities/fight.entity';
import { FightInfoEntity } from 'src/entities/fightInfo.entity';
import { FightGeneralEntity } from 'src/entities/fightGeneral.entity';
import { SkillTemplateEntity } from 'src/entities/skillTemplate.entity';
import { FightGoodsEntity } from 'src/entities/fightGoods.entity';
import { NpcGeneralEntity } from 'src/entities/npcGeneral.entity';
import { NpcSoldierEntity } from 'src/entities/npcSoldier.entity';
import { WinstonLoggeModule } from 'src/winston-logge/winston-logge.module';
import { PersonNpcEntity } from 'src/entities/personNpc.entity';
import { NpcTasksEntity } from 'src/entities/npcTasks.entity';
import { RoleTasksEntity } from 'src/entities/roleTasks.entity';
import { TeamsEntity } from 'src/entities/teams.entity';
import { TeamRolesEntity } from 'src/entities/teamRoles.entity';
import { TeamJoinEntity } from 'src/entities/teamJoin.entity';
import { EquipmentEntity } from 'src/entities/equipment/equipment.entity';
import { GemEntity } from 'src/entities/equipment/gem.entity';
import { GeneralEquip } from 'src/entities/general/generalequip.entity';
import { SoldierNum } from 'src/entities/general/soldiernum.entity';
import { GeneralotherService } from './generalother.service';
import { BlessEntity } from 'src/entities/general/bless.entity';
import { MountEntity } from 'src/entities/general/mount.entity';
import { PersonGoodsHoleEntity } from 'src/entities/personGoodHole.entity';
import { OtherService } from './config/other.service';
import { BookEntity } from 'src/entities/good/book.entity';
import { ConfigEntity } from 'src/entities/config.entity';
import { DungeonLogEntity } from 'src/entities/dungeonLog.entity';
import { FightSubscriber } from '../subscribers/fight.subscriber';
import { TeamRolesSubscriber } from 'src/subscribers/teamRoles.subscriber';
import { FriendsEntity } from 'src/entities/role/friends.entity';
import { OtherSeaService } from './config/otherSea.service';
import { UserConfigProvider } from 'src/common/user-config.provider';
import { OtherSea1Service } from './config/otherSea1.service';
import { ReceiveEntity } from 'src/entities/role/receive.entity';
import { ReceiveListEntity } from 'src/entities/good/receiveList.entity';
import { ManorLogEntity } from 'src/entities/good/manorLog.entity';
import { GUILD_ENTITIES } from 'src/entities/guild';
import { GuildService } from './guild.service';

@Module({
  imports: [TypeOrmModule.forFeature([
    MapsEntity,RoleEntity,NpcEntity,GoodEntity,PersonGoodsEntity,ManorEntity,LogsEntity,ManorEventEntity,
    GeneralEntity,SoldierEntity,SkillEntity,TaskEntity,ChatEntity,EquipEntity,FightEntity,FightInfoEntity,FightGeneralEntity,
    SkillTemplateEntity,NpcGoodsEntity,FightGoodsEntity,NpcGeneralEntity,NpcSoldierEntity,PersonNpcEntity,NpcTasksEntity,RoleTasksEntity,
    TeamsEntity,TeamRolesEntity,TeamJoinEntity,EquipmentEntity,GemEntity,GeneralEquip,SoldierNum,BlessEntity,MountEntity,PersonGoodsHoleEntity,
    BookEntity,ConfigEntity,DungeonLogEntity,FriendsEntity,ReceiveEntity,ReceiveListEntity,ManorLogEntity,
    ...GUILD_ENTITIES
  ]),WinstonLoggeModule],
  controllers: [GcmdController],
  providers: [GcmdService,ManorService,TasksService,GeneralService,GoodsService,CommonService,GuildService,
    FightService,GeneralotherService,OtherService,FightSubscriber,TeamRolesSubscriber,OtherSeaService,UserConfigProvider,OtherSea1Service],
})
export class GcmdModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(CheckMiddleware)
      .forRoutes('gcmd');
  }
}
