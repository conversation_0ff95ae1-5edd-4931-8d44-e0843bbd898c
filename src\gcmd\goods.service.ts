import { InjectRedis } from '@nestjs-modules/ioredis';
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import Redis from 'ioredis';
import { RoleEntity } from 'src/entities/role.entity';
import { DataSource, Equal, In, Like, MoreThan, Repository } from 'typeorm';
import { buildconfig, filterRouter, formatDate, formatDuring, getGoodType, isPureNumber, manorEventfn } from 'src/utils/config';
import { GoodEntity } from 'src/entities/goods.entity';
import { PersonGoodsEntity } from 'src/entities/personGoods.entity';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { ChatEntity } from 'src/entities/chat.entity';
import { GcmdService } from './gcmd.service';
import SensitiveWordTool from 'sensitive-word-tool';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { level, Logger } from 'winston';
import { RoleTasksEntity } from 'src/entities/roleTasks.entity';
import { NpcTaskStatus } from 'src/utils/types';
import * as fs from 'fs';
import { TasksType } from 'src/utils/types';
import { GeneralEntity } from 'src/entities/general.entity';
import { SoldierEntity } from 'src/entities/soldier.entity';
import { RedisService } from 'src/middleware/redis.service';
import { PersonGoodsHoleEntity } from 'src/entities/personGoodHole.entity';
import * as ejs from 'ejs'
import { CommonService } from 'src/middleware/common.service';
import { BookEntity } from 'src/entities/good/book.entity';
import { NpcEntity } from 'src/entities/npc.entity';
import { MapsEntity } from 'src/entities/maps.entity';
import { OtherService } from './config/other.service';
import { UserConfigProvider } from 'src/common/user-config.provider';
import { OtherSeaService } from './config/otherSea.service';
import { formatNumberWithCommas, mountConfig } from 'src/utils/config1';
import { FriendsEntity } from 'src/entities/role/friends.entity';
import { MountEntity } from 'src/entities/general/mount.entity';
@Injectable()
//物品  聊天
export class GoodsService {
    private readonly serviceName = 'goods'
    private taskJson: TasksType;
    private userSetting:any
    constructor(
        private eventEmitter: EventEmitter2,
        @Inject(forwardRef(() => GcmdService)) private readonly gcmd: GcmdService,
        @Inject(forwardRef(() => OtherSeaService)) private readonly otherSeaService: OtherSeaService,
        private readonly redis: RedisService,
        private readonly commonService: CommonService,
        private readonly otherService: OtherService,
        private readonly userConfigProvider: UserConfigProvider,
        @InjectRepository(GoodEntity) private readonly goodEntity: Repository<GoodEntity>,
        @InjectRepository(MapsEntity) private readonly mapsEntity: Repository<MapsEntity>,
        @InjectRepository(NpcEntity) private readonly npcEntity: Repository<NpcEntity>,
        @InjectRepository(ChatEntity) private readonly chatEntity: Repository<ChatEntity>,
        @InjectRepository(RoleEntity) private readonly roleEntity: Repository<RoleEntity>,
        @InjectRepository(PersonGoodsEntity) private readonly personGoodsEntity: Repository<PersonGoodsEntity>,
        @InjectRepository(PersonGoodsHoleEntity) private readonly personGoodsHoleEntity: Repository<PersonGoodsHoleEntity>,
        @InjectRepository(RoleTasksEntity) private readonly roleTasksEntity: Repository<RoleTasksEntity>,
        @InjectRepository(GeneralEntity) private readonly generalEntity: Repository<GeneralEntity>,
        @InjectRepository(SoldierEntity) private readonly soldierEntity: Repository<SoldierEntity>,
        @InjectRepository(BookEntity) private readonly bookEntity: Repository<BookEntity>,
        @InjectRepository(FriendsEntity) private readonly friendsEntity: Repository<FriendsEntity>,
        @InjectRepository(MountEntity) private readonly mountEntity: Repository<MountEntity>,
        private dataSource: DataSource,
        @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    ) { 
        this.userSetting=this.userConfigProvider.getUserSetting()
    }
    //我的物品 列表 usegoodId使用物品id sellStatus挂售状态
    async goodsList(sid: string, cmd: number, userId: string, { type=null, subType=null, page = 1,usegoodId=null,sellStatus=1,handelType=null,handelId=null,targetUserId=null,goodName=null}) {
        let userId1=targetUserId||userId
        let msg=''
        //物品使用
        if(usegoodId){
            let personGoodInfo=await this.personGoodsEntity.findOne({where:{id:usegoodId},relations:['good']})
            if(!personGoodInfo){
                msg='物品不存在'
            }else{
                if(['无字天书','坐骑召唤卡','炼丹炉','装备锻造炉'].includes(personGoodInfo.good.name)){
                    let type=1;
                    personGoodInfo.good.name=='坐骑召唤卡'&&(type=3);
                    personGoodInfo.good.name=='炼丹炉'&&(type=2);
                    personGoodInfo.good.name=='装备锻造炉'&&(type=4);
                    let routerList=JSON.parse(await this.redis.hget('user'+userId,'routerList'));
                    if(personGoodInfo.good.name=='装备锻造炉'){
                        routerList.push({
                            "name": "selectEquipType",
                            "service": "goods",
                            "params": {},
                            "title": "选择要锻造的装备种类"
                        })
                        await this.redis.hset('user'+userId,'routerList',JSON.stringify(routerList))
                        return await this.selectEquipType(sid,cmd,userId,{})
                    }else{
                        routerList.push({
                            "name": "researchBook",
                            "service": "goods",
                            "params": {type},
                            "title": "研制"
                        })
                        await this.redis.hset('user'+userId,'routerList',JSON.stringify(routerList))
                        return await this.researchBook(sid,cmd,userId,{type})
                    }
                    
                }
                msg=await this.canUse('user',Number(userId),usegoodId)
                if(!msg){
                    let res=await this.otherService.useGoodsLogic('user',Number(userId),personGoodInfo)
                    msg=res.msg
                }
            }
        }
        //撤销挂售
        if(handelType=='cancelSell'){
            let res=await this.personGoodsEntity.update({id:handelId},{sellStatus:1,price:0})
            msg=res.affected==0?'撤销销售失败':'撤销销售成功'
            msg+='<br/>'
        }
        let userSetting=this.userSetting
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'goodsList', title: '物品列表', service: 'goods', params: {type, subType, page,sellStatus} } };
        //获取返回路由
        let backRouter = await this.commonService.backRouter(userId, params, urlObj,'goodsList','goods')
        // let userInfo: RoleEntity = await this.roleEntity.findOneBy({ id: Number(userId) });
        // //获取物品列表 数量大于0
        // let [goodsList, totalItems] = await this.personGoodsEntity.findAndCount({
        //     where: { userId: Number(userId1), count: MoreThan(0),sellStatus, good: { type, subType,name:Like(`%${goodName||''}%`) } },
        //     relations: ['good'],
        //     take: userSetting.pageLine, skip: (page - 1) * userSetting.pageLine
        // })
        let where:any={type, subType}
        if(goodName)where.name=Like(`%${goodName}%`)
        let [userInfo,[goodsList, totalItems]]=await Promise.all([
            this.roleEntity.findOneBy({ id: Number(userId) }),
            this.personGoodsEntity.findAndCount({
                where: { userId: Number(userId1), count: MoreThan(0),sellStatus, good: where },
                relations: ['good'],
                take: userSetting.pageLine, skip: (page - 1) * userSetting.pageLine
            })
        ])
        //分页
        let str3 = this.setpage(params, urlObj, page, totalItems, {type, subType, page,goodName}, 'goodsList', '物品列表')
        //分类 str物品列表 str3 分页
        let str2 = '', str = '';
        switch (type) {
            case 2:
            case 4:
                str2 += `<a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 21 })}">刀</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 22 })}">剑</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 23 })}">棍</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 24 })}">斧</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 25 })}">锤</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 26 })}">枪</a>`
                break;
            case 3:
            case 5:
                str2 += `<a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 31 })}">帽子</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 32 })}">衣服</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 33 })}">裤子</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 34 })}">鞋子</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 35 })}">项链</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 36 })}">戒指</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 36 })}">手套</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 36 })}">肩甲</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 36 })}">披风</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 36 })}">特殊戒指</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 36 })}">左手套</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 36 })}">PK盾</a>
                `
                break
            case 6:
                str2 += `
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 61 })}">蹄铁</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 62 })}">马鞍</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 63 })}">缰绳</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 64 })}">马铠</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 65 })}">马蹬</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 66 })}">马嚼</a>`
                break
            case 8:
                str2 += `
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 72 })}">强化</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 73 })}">材料</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 74 })}">训练</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 75 })}">任务</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 77 })}">神兵</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 76 })}">活动</a>
                <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType: 78 })}">其它</a>`
                break
            default:
                break;
        }
        let num=(page - 1) * userSetting.pageLine
        goodsList.forEach((item, index) => {
            if(!item.good){
                str += `物品id:${item.id}不存在，请联系管理员<br/>`
            }else{
                if(targetUserId){
                    str += `
                    <a href="${this.seturlOther(params, urlObj, item?.good.name, 'sellGoodInfo', { thingId: item.id },'otherSeaService')}">${index + 1+num}.${item?.good.name}x${item.count}(${formatNumberWithCommas(item.price)})</a><br/>`
                }else{
                    str += `
                    <a href="${this.seturlOther(params, urlObj, item?.good.name, 'goodInfo', { thingId: item.id,isBag:true })}">${index + 1+num}.${item?.good.name}x${item.count}</a>
                    ${item.sellStatus==2?
                        `*<a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType, page,sellStatus,handelType:'cancelSell',handelId:item.id })}">撤销挂售</a>`:
                        `<a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type, subType, page,sellStatus,usegoodId:item.id,updateRouter:false })}">使用</a>`}
                    </br>`
                }
                
            }
        })
        let content = `${msg}
            ${!targetUserId?`负重:${userInfo.nowWeight}/${userInfo.maxWeight}<br/>
            <form action="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', {thingId:{label:'物品搜索'}})}" method="post">
                <input name="count13" type="text" maxlength="20" value="${goodName||''}"/>
                <br/>
                <input name="submit" type="submit" title="搜索" value="搜索"/>
            </form>
            <br/>`:''}
            
            <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type: 1 })}">消耗品</a>
            <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type: 2 })}">兵器</a>
            <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type: 3 })}">防具</a>
            <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type: 4 })}">士兵兵器</a>
            <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type: 5 })}">士兵防具</a>
            <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type: 6 })}">坐骑装备</a>
            <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type: 7 })}">装备宝石</a>
            <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', { type: 8 })}">其它</a>
            <a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', {})}">全部</a>
            <br/>
            ${str ? str : ''}
            ${str2 ? str2 + '<br/>' : ''}
            ${str3 ? str3 + '<br/>' : ''}
            ${!targetUserId?`
                ${sellStatus==1?`<a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', {sellStatus:2})}">我挂售的物品</a>`:`<a href="${this.seturlOther(params, urlObj, '物品列表', 'goodsList', {})}">我的物品</a>`}
               <br/> `:''}
            ${backRouter}
            <br/>
        `
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        await this.redis.hset('user' + userId, 'token', userId, 1800)
        this.updateUserWeight(Number(userId))
        return content;
    }
    //物品详情
    async goodInfo(sid: string, cmd: number, userId: string, { thingId,usegoodId=null,type=null,equipId=null,isBag=false }) {
        
        let msg=''
        if(type=='xiangbaoshi'){//thingId孔的id equipId背包宝石id
            let personHole=await this.personGoodsHoleEntity.findOne({where:{id:thingId}})
            thingId=personHole.equipId
            let goodInfo=await this.personGoodsEntity.findOne({ where: { id:equipId },relations: ['good'] })
            msg='镶嵌宝石失败'
            if(goodInfo?.count>=1){
                await this.dataSource.transaction(async (entityManager) => {
                    goodInfo.count-=1
                    goodInfo.usedNum+=1
                    await entityManager.save(goodInfo)
                    personHole.gemId=goodInfo.id
                    personHole.gemName=goodInfo.good.name
                    await entityManager.save(personHole)
                    msg='镶嵌宝石成功'
                })
                await this.calculateEquip(thingId)
            }
            this.updateUserWeight(Number(userId))
            msg+='<br/>'
        }
        if(type=='cancelGem'){
            // msg='取出所有镶嵌宝石失败<br/>';
            let {msg:msg1,personGoodList}=await this.handelGoodNum('高级锻造石',20,Number(userId))
            if(!msg1){
                msg='取出所有镶嵌宝石失败<br/>';
                let holeList=await this.personGoodsHoleEntity.find({where:{equipId:thingId}})
                holeList=holeList.filter(item=>item.gemId)
                let ids1=holeList.map(item=>item.gemId)
                holeList.forEach(item=>{
                    item.gemId=null
                    item.gemName=null
                })
                await this.dataSource.transaction(async (entityManager) => {
                    let goodList1=await this.personGoodsEntity.find({where:{id:In(ids1)}})
                    goodList1.forEach(item=>{
                        item.count+=1
                        item.usedNum-=1
                    })
                    await entityManager.save(goodList1)
                    await entityManager.save(personGoodList)
                    await entityManager.save(holeList)
                    msg='高级锻造石-20，取出所有镶嵌宝石成功<br/>'
                })
            }else{
                msg=msg1
            }
        }
        if(type=='xiuli'){
            let count=await this.repairEquip(thingId)
            if(count>0){
                await this.dataSource.transaction(async (entityManager) => {
                    let goodInfo=await this.personGoodsEntity.findOne({where:{id:thingId},relations:['good']})
                    let userInfo=await this.roleEntity.findOne({where:{id:Number(userId)}})
                    if(userInfo.gold<count){
                        msg=`修理装备需要${count}银两，银两不足，修理失败</br>`
                    }else{
                        userInfo.gold-=count
                        await entityManager.save(userInfo)
                        goodInfo.durability=goodInfo.good.durability
                        await entityManager.save(goodInfo)
                        msg=`你花费${count}银两修理装备</br>`
                        this.eventEmitter.emit('writeLogs', { userId, name: '修理装备:' + goodInfo.good.name, gold: count })
                    }
                })
            }else{
                msg=`没有需要修理的装备</br>`
            }
        }
        if(type=='cancelSell'){
            let res=await this.personGoodsEntity.update({id:thingId},{sellStatus:1,price:0})
            msg=res.affected==0?'撤销挂售失败':'撤销挂售成功'
            msg+='<br/>'
        }
        let goodInfo = await this.personGoodsEntity.findOne({ 
            where: { id:thingId },
            relations: ['good'],
        })
        if(!goodInfo||!thingId){
            return '物品不存在<br/>'+await this.goodsList(sid, cmd, userId,{type:null,subType:null})
        }
        let holeInfo=await this.personGoodsHoleEntity.find({where:{equipId:thingId}})
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'goodInfo', title: goodInfo.good.name, service: 'goods', params: {thingId,isBag} } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        const handleStr = ()=>{
            let str=''
            if(goodInfo.attack||goodInfo.attackGem){
                !goodInfo.attack&&(goodInfo.attack=0)
                str+=`攻击力：${goodInfo.attack}`
                !goodInfo.attackGem&&(goodInfo.attackGem=0)
                if(goodInfo.attackGem){
                    str+=`+${goodInfo.attackGem}`
                }
                str+='<br/>'
            }
            if(goodInfo.defense||goodInfo.defenseGem){
                !goodInfo.defense&&(goodInfo.defense=0)
                str+=`防御力：${goodInfo.defense}`
                !goodInfo.defenseGem&&(goodInfo.defenseGem=0)
                if(goodInfo.defenseGem){
                    str+=`+${goodInfo.defenseGem}`
                }
                str+='<br/>'
            }
            if(goodInfo.hp||goodInfo.hpGem){
                !goodInfo.hp&&(goodInfo.hp=0)
                str+=`生命：${goodInfo.hp}`
                !goodInfo.hpGem&&(goodInfo.hpGem=0)
                if(goodInfo.hpGem){
                    str+=`+${goodInfo.hpGem}`
                }
                str+='<br/>'
            }
            if(holeInfo.length){
                let str1='',showCancel=false;
                holeInfo.forEach((item,index)=>{
                    let str2=''
                    if(item.gemId){
                        showCancel=true
                        str2=`<a href="${this.seturlOther(params, urlObj, '宝石','gemInfo', { holdId: item.id })}">${item.gemName}</a><br/>`
                    }else{
                        str2=`<a href="${this.seturlOther(params, urlObj, '选择宝石','checkEquip', {type:7, itemId: item.id,title:'选择宝石' },'generalother')}">[空]</a><br/>`
                    }
                    str1+=`孔${item.holeName}:${str2}`
                })
                str+=`宝石孔数:${holeInfo.length}<br/>
                ${str1}
                ${showCancel?`<a href="${this.seturlOther(params, urlObj, '取消镶嵌','goodInfo', { type:'cancelGem',thingId: goodInfo.id,updateRouter:false })}">取出所有镶嵌宝石</a><br/>`:''}`
            }
            return str
        }
        let str=handleStr()
        let content = `${msg}${goodInfo.good.name}${isBag?`x${goodInfo.count}`:''}<br/>
        ${goodInfo.good.desc?goodInfo.good.desc+'<br/>':''}
        类别: ${getGoodType(goodInfo.good.type, goodInfo.good.subType)}<br/>
        ${isBag?
            `重量: ${goodInfo.good.weight}<br/>
        价格: ${goodInfo.good.price}<br/>
        ${goodInfo.sellStatus==2?`销售价格:${formatNumberWithCommas(goodInfo.price)}<br/>`:''}`
        :''}
        ${goodInfo.durability!=99999?`耐久:${goodInfo.durability}/${goodInfo.good.durability}<br/>`:''}
        绑定: ${goodInfo.good.bind==1?'绑定':'不绑定'}<br/>
        ${str}
        ${goodInfo.durability!=99999?`<a href="${this.seturlOther(params, urlObj, '修理','goodInfo', { thingId,type:'xiuli',updateRouter:false })}">修理</a><br/>`:''}
        ${isBag?`<a href="${this.seturlOther(params, urlObj, '出售物品','sellGood', { thingId: goodInfo.id,hidden:true })}">卖掉</a><br/>
        ${goodInfo.sellStatus==2?
            `<a href="${this.seturlOther(params, urlObj, '撤销销售','goodInfo', { thingId: goodInfo.id,type:'cancelSell' })}">撤销销售</a><br/>`:
            `${(goodInfo.isBind==1||goodInfo.good.bind==1)?``:`<a href="${this.seturlOther(params, urlObj, '挂出销售','sellGoodGua', { thingId: goodInfo.id })}">挂出销售</a><br/>`}`
        }`:''}
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //计算装备属性
    async calculateEquip(personGoodId) {
        let holeList=await this.personGoodsHoleEntity.find({where:{equipId:personGoodId}})
        let attack=0,defense=0,hp=0;
        let ids=holeList.filter(item=>item.gemId).map(item=>item.gemId)
        let personGoods=await this.personGoodsEntity.find({where:{id:In(ids)}})
        personGoods.forEach(item=>{
            attack+=item.attack
            defense+=item.defense
            hp+=item.hp
        })
        await this.personGoodsEntity.update({id:personGoodId},{attackGem:attack,defenseGem:defense,hpGem:hp})
    }
    //选择要锻造的装备种类
    async selectEquipType(sid: string, cmd: number, userId: string, {}) {
        let pageTitle = '选择要锻造的装备种类'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {},pageTitle,'selectEquipType')

        let content=`${pageTitle}<br/>
        <a href="${this.seturlOther(params, urlObj, '武将武器','selectEquipInfo', {type:2})}">武将武器</a><br/>
        <a href="${this.seturlOther(params, urlObj, '武将防具','selectEquipInfo', {type:3})}">武将防具</a><br/>
        <a href="${this.seturlOther(params, urlObj, '士兵武器','selectEquipInfo', {type:4})}">士兵武器</a><br/>
        <a href="${this.seturlOther(params, urlObj, '士兵防具','selectEquipInfo', {type:5})}">士兵防具</a><br/>
        <a href="${this.seturlOther(params, urlObj, '坐骑装备','selectEquipInfo', {type:6})}">坐骑装备</a><br/>
        <a href="${this.seturlOther(params, urlObj, '装备宝石','selectEquipInfo', {type:7})}">宝石</a><br/>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //选择要锻造的装备信息
    async selectEquipInfo(sid: string, cmd: number, userId: string, {type,page=1}) {
        let pageTitle = '选择要锻造的装备'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {type,page},pageTitle,'selectEquipInfo')
        let userSetting=JSON.parse(await this.redis.hget('user'+userId,'userSetting'))

        // 根据锻造类型筛选装备
        let whereCondition: any = { type, isForge: 1 }

        let [goodsList, totalItems] = await this.goodEntity.findAndCount({
            where: whereCondition,
            take: userSetting.pageLine, skip: (page - 1) * userSetting.pageLine
        })

        //分页
        let str3 = this.setpage(params, urlObj, page, totalItems, {type, page}, 'selectEquipInfo', pageTitle)

        let str=''
        let num=(page - 1) * userSetting.pageLine
        goodsList.forEach((item, index) => {
            str += `${index + 1+num}.${item.name}<a href="${this.seturlOther(params, urlObj, item.name, 'forgeEquipInfo', { goodId:item.id, forgeType:1})}">锻造</a>
            <a href="${this.seturlOther(params, urlObj, item.name, 'forgeEquipInfo', { goodId:item.id, forgeType:2})}">百分百锻造</a><br/>`
        })

        let content=`${pageTitle}<br/>
        ${str}
        ${str3?str3 + '<br/>' : ''}
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //装备锻造详情页面
    async forgeEquipInfo(sid: string, cmd: number, userId: string, { goodId,type=null,forgeType=1 }) {
        let msg=''
        let goodInfo = await this.goodEntity.findOne({ where: { id:goodId } })
        let isHundredPercent = forgeType == 2 // 是否百分百锻造

        if(type=='yanzhi'){//研治
            let userInfo=await this.roleEntity.findOne({where:{id:Number(userId)}})
            let needNum = isHundredPercent ? goodInfo.forgeMaterialCount100 : goodInfo.forgeMaterialCount
            let num=await this.getGoodNumByName(Number(userId), '高级锻造石')

            if(num<needNum){
                msg= `高级锻造石数量不足,需要${needNum},你当前拥有${num}<br/>`
            }else{
                msg=`其他错误<br/>`
                await this.dataSource.transaction(async (manager) => {
                    let bool=true;

                    // 百分百锻造必定成功，普通锻造需要判断成功率
                    if(!isHundredPercent){
                        let randomNum=Math.floor(Math.random()*100)
                        if(randomNum>goodInfo.successRate){
                            bool=false
                        }
                    }

                    if(bool){
                        await this.changePersonGoodByName(Number(userId),goodInfo.name,1,'add')
                        await this.changePersonGoodByName(Number(userId),'高级锻造石',needNum,'sub')
                        await this.changePersonGoodByName(Number(userId),'装备锻造炉',1,'sub')

                        this.eventEmitter.emit('writeLogs', { userId, name: (isHundredPercent ? '百分百锻造:' : '锻造:') + goodInfo.name, gold: 0 })
                        let chat=new ChatEntity()
                        chat.content=`${userInfo.name}${isHundredPercent ? '百分百' : ''}成功锻造${goodInfo.name}`
                        chat.type=5
                        chat.userId=Number(userId)
                        this.sendMsgToRole(chat)
                    }
                    msg=`高级锻造石-${needNum}，${isHundredPercent ? '百分百' : ''}锻造${goodInfo.name}${bool?'成功':'失败'}<br/>`
                })
            }
        }

        let pageTitle = isHundredPercent ? '百分百锻造详情' : '锻造详情'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {goodId,forgeType},pageTitle)

        let needNum = isHundredPercent ? goodInfo.forgeMaterialCount100 : goodInfo.forgeMaterialCount
        let successRateText = isHundredPercent ? '100%' : `${goodInfo.successRate}%`
        let forgeTypeText = isHundredPercent ? '百分百锻造' : '普通锻造'

        let content = `${msg}你正准备${forgeTypeText}${goodInfo.name}：<br/>
            需要高级锻造石×${needNum}<br/>
            成功率：${successRateText}<br/>
            <a href="${this.seturlOther(params, urlObj, '确定'+forgeTypeText,'forgeEquipInfo', { goodId,type:'yanzhi',forgeType })}">确定${forgeTypeText}</a><br/>
            ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //无字天书研究页面 
    async researchBook(sid: string, cmd: number, userId: string, {type=1,page=1}) {
        let pageTitle='研制'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {},pageTitle,'researchBook') 
        let userSetting=JSON.parse(await this.redis.hget('user'+userId,'userSetting'))
        let [goodsList, totalItems] = await this.bookEntity.findAndCount({
            where: { type },
            take: userSetting.pageLine, skip: (page - 1) * userSetting.pageLine
        })
        //分页
        let str3 = this.setpage(params, urlObj, page, totalItems, {type, page}, 'researchBook', pageTitle)
        let str=''
        goodsList.forEach((item, index) => {
            str += `${index + 1}.${item.title}
            <a href="${this.seturlOther(params, urlObj, item.title, 'researchBookInfo', { bookId:item.id,bili:true })}">研制</a>
            <a href="${this.seturlOther(params, urlObj, item.title, 'researchBookInfo', { bookId:item.id })}">百分百研制</a><br/>`
        })
        let content = `可以研制的列表包括：<br/>
            ${str}
            ${str3?str3 + '<br/>' : ''}
            ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //无字天书详情页
    async researchBookInfo(sid: string, cmd: number, userId: string, { bookId,bili=false,type='' }) {
        let msg=''
        let bookInfo = await this.bookEntity.findOne({ where: { id:bookId } })
        let name='天书油墨',name1='无字天书',title1='研制';
        bookInfo.type==2&&(name='神奇丹药',name1='炼丹炉');
        bookInfo.type==3&&(name='高级坐骑饲料',name1='坐骑召唤卡',title1='坐骑召唤');
        if(type=='yanzhi'){
            let userInfo=await this.roleEntity.findOne({where:{id:Number(userId)}})
            let needNum=bili?bookInfo.biliNum:bookInfo.count
            let num=await this.getGoodNumByName(Number(userId), name)
            if(num<needNum){
                msg= `${name}数量不足,需要${needNum},你当前拥有${num}<br/>`
            }else{
                msg=`其他错误<br/>`
                await this.dataSource.transaction(async (entityManager) => {
                    let bool=true;
                    if(bili){
                        let randomNum=Math.floor(Math.random()*100)
                        if(randomNum>bookInfo.bili){
                            bool=false
                        }
                    }
                    let personGoodInfo=await this.personGoodsEntity.findOne({where:{userId:Number(userId),good:{name}}})
                    personGoodInfo.count-=needNum
                    await entityManager.save(personGoodInfo)
                    personGoodInfo=await this.personGoodsEntity.findOne({where:{userId:Number(userId),good:{name:name1}}})
                    personGoodInfo.count-=1
                    await entityManager.save(personGoodInfo)
                    if(bool){
                        if(bookInfo.type==3){
                            let mountTeap:any={},mountType='';
                            for (const key in mountConfig) {
                                if (Object.prototype.hasOwnProperty.call(mountConfig, key)) {
                                    const element = mountConfig[key];
                                    if(element.name==bookInfo.title){
                                        mountTeap=element
                                        mountType=key
                                        break;
                                    }
                                }
                            }
                            let mountInfo=new MountEntity()
                            mountInfo.userId=Number(userId)
                            mountInfo.mountType=mountType
                            mountInfo.level=1
                            mountInfo.attack=mountTeap.attack
                            mountInfo.defense=mountTeap.defense
                            await entityManager.save(mountInfo)
                        }else{
                            let goodInfo=await this.personGoodsEntity.findOne({where:{userId:Number(userId),good:{name:bookInfo.title}}})
                            if(!goodInfo){
                                goodInfo=new PersonGoodsEntity()
                                goodInfo.userId=Number(userId)
                                goodInfo.good=await this.goodEntity.findOne({where:{name:bookInfo.title}})
                                goodInfo.count=1
                            }else{
                                goodInfo.count+=1
                            }
                            await entityManager.save(goodInfo)
                        }
                        this.eventEmitter.emit('writeLogs', { userId, name: bookInfo.type==3?'召唤:' + bookInfo.title:'研制:' + bookInfo.title, gold: 0 })
                        let chat=new ChatEntity()
                        chat.content=`${userInfo.name}成功${bookInfo.type==3?'召唤':'研制'}${bookInfo.title}`
                        chat.type=5
                        chat.userId=Number(userId)
                        this.sendMsgToRole(chat)
                    }
                    
                    
                    msg=`${name}-${needNum},${name1}-1，${title1}${bookInfo.title}${bool?'成功':'失败'}<br/>`
                })
            }
        }
        let pageTitle='详情'
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {},pageTitle) 
        let content = `${msg}你正准备${title1}${bookInfo.title}：<br/>
            需要${name}x${bili?bookInfo.biliNum:bookInfo.count}<br/>
            成功率：${bili?bookInfo.bili:100}%<br/>
            <a href="${this.seturlOther(params, urlObj, '研制','researchBookInfo', { bookId,bili,type:'yanzhi' })}">确定${title1}</a><br/>
            ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content
    }
    //任务列表
    async tasksList(sid: string, cmd: number, userId: string, {page=1}) {
        let userSetting=JSON.parse(await this.redis.hget('user'+userId,'userSetting'))
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'tasksList', title: '任务列表', service: 'goods', params: {} } };
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)

        let userInfo = await this.roleTasksEntity.find({
            where:{role:{id:Number(userId)},status:NpcTaskStatus.IN_PROGRESS},
        })
        //分页
        let str3 = this.setpage(params, urlObj, page, userInfo.length, {page}, 'tasksList', '任务列表')
        let str=''
        userInfo.forEach((item,index)=>{
            if(index>=((page-1)*userSetting.pageLine)&&index<(page*userSetting.pageLine)){
                str+=`<a href="${this.seturlOther(params, urlObj, '任务详情', 'taskInfo', { taskId: item.taskId,taskStepId:item.stepId })}">${index+1}、${item.taskName}</a><br/>`
            }
        })
        let content = `目前的任务有:<br/> 
        ${str}
        ${str3?str3 + '<br/>' : ''}
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //任务详情
    async taskInfo(sid: string, cmd: number, userId: string, { taskId,taskStepId }) {
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'taskInfo', title: '任务详情', service: 'goods', params: {taskId} } }
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let taskList=this.otherService.getTaskListByTaskId(taskId,taskStepId)
        let taskInfo=taskList.steps[taskStepId-taskList.initIndex]
        taskInfo.desc=taskInfo.desc.replace(/(?:银两|潜能|粮草|木材|石料|生铁)\+\d+<br\/>/g, '');
        let taskStatus=await this.gcmd.checkTaskStatus(taskInfo.condition,Number(userId)) 
        let content = `任务详情：<br/>
        ${taskInfo.desc}
        ${taskStatus.msg?('任务：'+taskStatus.msg):''}</br>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //挂出销售
    async sellGoodGua(sid: string, cmd: number, userId: string, { thingId }) {
        let {backRouter,params,urlObj} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {thingId})
        let goodInfo = await this.personGoodsEntity.findOne({ 
            where: { id:thingId},
            relations: ['good'],
        })
        let content = `请输入${goodInfo.good.name}的销售价格：<br/>
        <form action="${this.seturlOther(params, urlObj,'挂出销售', 'sellGoodGuaPost', { thingId })}" method="post">
        价格<input name="count9" type="number" value="" maxlength="10"/><br/>
        <input type="submit" value="挂出销售"/></form>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj))
        await this.redis.hset('user' + userId, 'token', userId, 1800)
        return content;
    }
    //挂出销售 逻辑
    async sellGoodGuaPost(userId:number,thingId:number,count:number, cmd: number,sid: string) {
        count=Number(count);
        if(count>9999999999){
            return '最大挂售金额为9999999999,请重新输入<br/>'+(await this.goodInfo(sid, cmd, String(userId), { thingId }));
        }
        let res=await this.personGoodsEntity.update({id:thingId},{sellStatus:2,price:count})
        let msg=''
        if(res.affected==0){
            msg='挂售失败<br/>'
        }else{
            msg=`挂售成功,挂售价格为：${count}<br/>`
        }
        return msg+(await this.goodInfo(sid, cmd, String(userId), { thingId }));
    }
    //出售物品
    async sellGood(sid: string, cmd: number, userId: string, { thingId }) {
        let goodInfo = await this.personGoodsEntity.findOne({ 
            where: { id:thingId },
            relations: ['good'],
        })
        const params = { sid, cmd }
        const urlObj = { [cmd]: { name: 'sellGood', title: '出售物品', service: 'goods', params: {thingId} } }
        
        //获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        let content = `你将以${goodInfo.good.price}x0.8=${(goodInfo.good.price*0.8).toFixed(1)}银两的价格卖掉${goodInfo.good.name}<br/>
        <form action="${this.seturlOther(params, urlObj,'购买物品', 'sellPost', { thingId })}" method="post">
        请输入你要卖掉的数量：<input name="count1" type="number" value="1" maxlength="10"/><br/>
        <input name="submit" type="submit" title="卖掉" value="卖掉"/></form>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj))
        await this.redis.hset('user' + userId, 'token', userId, 1800)
        return content;
    }
    //出售物品 成功返回上一页 否则返回错误提示
    async sellPost(userId:number,thingId:number,count:number, cmd: number,sid: string){
        count=Number(count);
        let msg='',content='';
        let userInfo: RoleEntity = await this.roleEntity.findOneBy({ id: userId });
        let goodInfo=await this.personGoodsEntity.findOne({ 
            where: { id:thingId },
            relations: ['good'],
        })
        if(!isPureNumber(count)||count<1||count>9999||goodInfo.count<count){
            msg='数量不正确<br/>'
        }
        if(!msg){
            let price=Number((goodInfo.good.price*count*0.8).toFixed(1))
            try {
                await this.dataSource.transaction(async (manager) => {
                    if(goodInfo.good.holeCount){
                        let personGoodHole=await this.personGoodsHoleEntity.find({where:{equipId:goodInfo.id}})
                        await manager.remove(personGoodHole)
                    }
                    if(count==goodInfo.count){
                        await manager.remove(goodInfo) 
                    }else{
                        await manager.update(PersonGoodsEntity, { id: goodInfo.id }, { count: () => "count - "+count })
                    }
                    //扣除用户银两
                    userInfo.gold=Number(userInfo.gold)+price
                    userInfo.nowWeight-=count
                    userInfo=await manager.save(RoleEntity,userInfo)
                })
                await this.updateUserWeight(userId)
                await this.redis.hset('user' + userId, 'userInfo',JSON.stringify(userInfo))
                await this.redis.hdel('user' + userId, 'token')
                //写入日志
                this.eventEmitter.emit('writeLogs', { userId, name: '出售物品:' + goodInfo.good.name, gold: price })
                msg=`你卖掉了${goodInfo.good.name}×${count},获得${price}银两</br>`
            } catch (error) {
                console.log(error)
            }
        }
        let routerInfo = await this.commonService.getNotHiddenRouter(userId)
        if(routerInfo.service=='goods'){
            content+=(await this[routerInfo.name](sid,cmd,userId,routerInfo.params))
        }else{
            content+=(await this[routerInfo.service][routerInfo.name](sid,cmd,userId,routerInfo.params))
        }
        return msg+content 
    }
    /**
     * 修理装备
     * @param personGoodId 玩家背包物品id
     * @returns 返回花费的修理费
     */
    async repairEquip(personGoodId) {
        let personGood = await this.personGoodsEntity.findOne({ where: { id: Equal(personGoodId) }, relations: ['good'] })
        let count = 0
        if (personGood) {
            if(personGood.durability===999){
                return 0
            }else{
                count=(personGood.good.durability-personGood.durability)*5
            }
        }
        return count
    }
    //镶嵌到装备的宝石详情页
    async gemInfo(sid: string, cmd: number, userId: string, { holdId}) {
        const {params,urlObj,backRouter} = await this.commonService.initLink(sid, cmd, userId,this.serviceName, {holdId})
        let holeInfo=await this.personGoodsHoleEntity.findOne({where:{id:holdId}})
        let goodInfo = await this.personGoodsEntity.findOne({
            where: { id:holeInfo.equipId },
            relations: ['good'],
        })
        let gemInfo=await this.personGoodsEntity.findOne({where:{id:holeInfo.gemId},relations:['good']})
        let content = `宝石详情：<br/>
            ${goodInfo.good.name}.孔${holeInfo.holeName}<br/>
            已嵌宝石：${holeInfo.gemName}<br/>
            攻击力：${gemInfo.attack}<br/>
            防御力：${gemInfo.defense}<br/>
            生命：${gemInfo.hp}<br/>
        ${holeInfo.gemName?'':`<a href="${this.seturlOther(params, urlObj, '镶嵌宝石','gemInfo', { holdId })}">镶嵌宝石</a><br/>`}
        ${backRouter}`
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //更新玩家负重
    @OnEvent('updateUserWeight')
    async updateUserWeight(userId:number){
        let goodList=await this.personGoodsEntity.find({where:{userId,count:MoreThan(0)},relations:['good']})
        let count=goodList.reduce((total,item)=>{
            if(item.good){
                return total+item.good.weight*item.count
            }else{
                return total
            }
        },0)
        await this.roleEntity.save({id:userId,nowWeight:count})
    }
    //聊天频道
    /**
     * 
     * @param chattype 类型
     * @param targetID 目标id 发送私聊
     * @returns 
     */
    async chatPage(sid: string, cmd: number, userId: string,{chattype,targetID,hidden=false}) {
        const params = {sid, cmd}
        const urlObj = { [cmd]: { name: 'chatPage', title: '聊天频道',service:'goods', params:{chattype,hidden} } }
        let backRouter = await this.backRouter(userId, params, urlObj)
        let str='',str1='',str2='';
        let chatarr=['公共','帮派','队伍']
        if(!targetID){
            chatarr.forEach((item,index)=>{
                if(index==chattype){
                    str+=item
                }else{
                    str+=` <a href="${this.seturlOther(params,urlObj,'聊天频道','chatPage',{chattype:index})}">${item}</a> `
                }
            })
            str+='</br>'
            str2=`请输入你要向${chatarr[chattype]}广播的信息<br/>`
            str1+=` <a href="${this.seturlOther(params,urlObj,`查看最近${chatarr[chattype]}信息`,'chatRecordPage',{chattype})}">查看最近${chatarr[chattype]}信息</a> <br/>`
        }else{
            let userInfo=await this.roleEntity.findOneBy({id:Number(targetID)})
            str2=`请输入你要向${userInfo.name}发送的信息<br/>`
        }
        
        let content = `<p>[聊天频道]<br/>
            ${str}
            ${str2}
            </p>
            <form action="${this.seturlOther(params,urlObj,'聊天频道','sellGood',{chattype,targetID})}" method="post">
            信息内容：<textarea name="msg" maxlength="200" rows="4" cols="20"></textarea><br/>
            <input name="submit" type="submit" title="发送信息" value="发送信息"/></form><br/>
            ${str1}
            <a href="${this.seturlOther(params,urlObj,'私聊信息','chatRecordPage',{chattype:4})}">最近私聊信息</a><br/>
            <a href="${this.seturlOther(params,urlObj,'好友列表','friendList',{},'generalother')}">好友列表</a><br/>
            <a href="${this.seturlOther(params,urlObj,'黑名单','friendList',{type:'black'},'generalother')}">黑 名 单</a><br/>
        ${backRouter}`
        await this.redis.hset('user' + userId, 'token', userId)
        await this.redis.hset('user'+userId,'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    //发送消息逻辑
    /**
     * 
     * @param sid 
     * @param cmd 
     * @param userId 发送人id
     * @param chattype 消息类型  1234
     * @param msg 消息内容
     * @param toUserId 接收人id  私聊消息 可以为空
     * @returns 
     */
    async sendMsgPost(cmd: number,sid: string, userId: number,chattype:number,msgcontent:string,toUserId) {
        msgcontent=msgcontent.replace(/<[^>]*>/g, '');
        let userInfo: RoleEntity = await this.roleEntity.findOneBy({ id: userId });
        let touserInfo: RoleEntity = JSON.parse(await this.redis.hget('user' + toUserId, 'userInfo'));
        const sensitiveWordTool = new SensitiveWordTool({
            useDefaultWords: true
        })
        msgcontent=msgcontent.length>100?msgcontent.substring(0,200):msgcontent
        let chat=new ChatEntity()
        chat.userName=userInfo.name
        chat.userId=userId
        chat.content=sensitiveWordTool.filter(msgcontent)
        chat.type=chattype
        let msg='你在公共频道说: '
        let routerInfo=await this.commonService.getNotHiddenRouter(userId)
        if(chattype==4){
            let friends=await this.friendsEntity.findOne({where:{userId:toUserId,friendId:String(userId)}})
            if(friends&&friends.status==2){
                let content='对方已将你加入黑名单,无法发起私聊';
                if(routerInfo.service=='goods'){
                    content+=(await this[routerInfo.name](sid,cmd,userId,routerInfo.params))
                }else{
                    content+=(await this[routerInfo.service][routerInfo.name](sid,cmd,userId,routerInfo.params))
                }
                return content 
            }
            chat.toUserId=toUserId
            msg='你对'+touserInfo.name+'说'
        }
        await this.chatEntity.save(chat)
        this.sendMsgToRole(chat)
        await this.redis.hdel('user' + userId, 'token')
        let content='';
        content=msg+chat.content
        if(routerInfo.service=='goods'){
            content+=(await this[routerInfo.name](sid,cmd,userId,routerInfo.params))
        }else{
            content+=(await this[routerInfo.service][routerInfo.name](sid,cmd,userId,routerInfo.params))
        }
        return content 
    }
    //给指定用户发送消息
    @OnEvent('sendMsgToRole')
    async sendMsgToRole(chatEntity:ChatEntity){
        //私聊
        if(chatEntity.toUserId){
            await this.redis.rpush('chatUser'+chatEntity.toUserId,JSON.stringify(chatEntity))
        }
        //公聊
        if(chatEntity.type==0){
            let users=await this.redis.keys('user*')
            for(let key of users){
                if(key=='user'+chatEntity.userId)continue
                await this.redis.rpush('chatUser'+key.slice(4),JSON.stringify(chatEntity))
            }
        }
        //系统 个人
        if(chatEntity.type==3){
            await this.redis.rpush('chatUser'+chatEntity.userId,JSON.stringify(chatEntity))
        }
        //系统 公聊
        if(chatEntity.type==5){
            let users=await this.redis.keys('user*')
            chatEntity.type=3
            for(let key of users){
                // if(key=='user'+chatEntity.userId)continue
                await this.redis.rpush('chatUser'+key.slice(4),JSON.stringify(chatEntity))
            }
        }
    }
    //聊天记录页面
    async chatRecordPage(sid: string, cmd: number, userId: string,{page=1,chattype}) {
        let userSetting=JSON.parse(await this.redis.hget('user'+userId,'userSetting'))
        const params = {sid, cmd}
        let chatarr=['公共','帮派','队伍','系统','私聊']
        const urlObj = { [cmd]: { name: 'chatRecordPage', title: '聊天记录',service:'goods', params:{page,chattype} } }//获取返回路由
        let backRouter = await this.backRouter(userId, params, urlObj)
        //获取消息列表
        let [chatList, totalItems] = await this.chatEntity.findAndCount({
            where: { 
                type: chattype,
                ...(chattype==4?{toUserId:Number(userId)}:{}),
            },
            order: { id: 'DESC' },
            take: userSetting.pageLine, skip: (page - 1) * userSetting.pageLine
        })
        let chatContent=''
        chatList.forEach((item) => {
            chatContent+=`[${formatDate(item.createdAt)}]<a href="${this.seturlOther(params,urlObj,item.userName,'myStatus',{targetUserId:item.userId},'gcmd')}">${item.userName}</a>: ${item.content.replace(/<[^>]*>/g, '')}<br/>`
        })
        //分页
        let pageItem = this.setpage(params, urlObj, page, totalItems, {chattype, page}, 'chatRecordPage', '聊天记录')
        let content = `[最近${chatarr[chattype]}信息] <a href="${this.seturlOther(params, urlObj, '聊天记录', 'chatRecordPage', { page, chattype})}">刷新</a><br/>
            ------<br/>
            ${chatContent}
            ${pageItem?pageItem+'<br/>':''}
            ------<br/>
        ${backRouter}`
        await this.redis.hset('user'+userId,'routers', JSON.stringify(urlObj), 1800)
        return content;
    }
    /**
     * 
     * @param params 
     * @param urlObj 
     * @param page 第几页
     * @param totalItems 分页数据 
     * @param attrs 参数
     * @param router 页面service名称
     * @param routerName 页面名称
     * @returns 
     */
    setpage(params, urlObj, page, totalItems, attrs, router, routerName?: string) {
        let pageStr = '';
        let totalPages = Math.ceil(totalItems / this.userSetting.pageLine);
        const createPageLink = (newPage, text) => {
            let newAttrs = { ...attrs, page: newPage };
            pageStr += `<a href="${this.seturlOther(params, urlObj, routerName, router, newAttrs)}">${text}</a> `;
        };
        if (page > 2) createPageLink(1, '首页');
        if (page !== 1) createPageLink(page - 1, '上一页');
        if (page < totalPages) createPageLink(page + 1, '下一页');
        if (page < totalPages - 1 && totalPages > 1) createPageLink(totalPages, '末页');
        return pageStr
    }
    //获取玩家物品数量
    async getGoodNum(userId: number, goodId: number) {
        let personGood: PersonGoodsEntity = await this.personGoodsEntity.findOne({ where: { userId, good: { id: goodId } },relations: ['good'] })
        if (personGood) {
            return personGood
        } else {
            let goodInfo=await this.getGoodDetail(goodId)
            return { count: 0, id: 0,good:{name:goodInfo.name} }
        }
        
    }
    //根据地图名称获取到npcid
    async getNpcIdByMapNames(mapNames: string[]) {
        let mapInfo = await this.mapsEntity.find({ where: { title: In(mapNames) },select:['id'] })
        let res = await this.npcEntity.find({ where: { gpsId: In(mapInfo.map(item=>item.id))},select:['id'] })
        return res.map(item=>item.id)
    }

    //根据物品名称获取背包物品数量
    async getGoodNumByName(userId: number, goodName: string,goodNameArr=[],ent=false) {
        if(goodNameArr.length>0){
            let personGoodList=await this.personGoodsEntity.find({where:{userId,good:{name:In(goodNameArr)}}})
        }
        //物品有两种 绑定和不绑定
        let googList=await this.goodEntity.find({where:{name:Equal(goodName)},select:{id:true}})
        let ids=googList.map(item=>item.id)
        let personGoodList=await this.personGoodsEntity.find({where:{userId,good:{id:In(ids)}}})
        let num=personGoodList.reduce((total,item)=>total+item.count,0)
        personGoodList=personGoodList.filter(item=>item.count>0)
        // let pids=personGoodList.map(item=>item.good.id)
        return num;
    }
    async getGoodEntityByName(userId: number, goodName: string) {
        return await this.personGoodsEntity.findOne({where:{userId,good:{name:goodName}}})
    }
    async getGoodNumByNameArr(userId: number,goodNameArr=[]) {
        let personGoodList=await this.personGoodsEntity.find({where:{userId,good:{name:In(goodNameArr)}},relations:['good']})
        let arr=[]
        goodNameArr.forEach(item=>{
            let good=personGoodList.find(item1=>item1.good.name==item)
            if(good){
                arr.push({name:good.good.name,count:good.count,id:good.id})
            }else{
                arr.push({name:item,count:0})
            }
        })
        return arr
    }
    //传入物品名称和所需数量，返回处理后的物品实体  有为0的情况 
    async handelGoodNum(goodName:string,goodNum:number,userId:number){
        let msg=''
        let goodList=await this.goodEntity.find({where:{name:goodName}})
        let ids=goodList.map(item=>item.id)
        let personGoodList=await this.personGoodsEntity.find({where:{userId,good:{id:In(ids)}},relations:['good']})
        personGoodList=personGoodList.sort((a,b)=>a.good.bind-b.good.bind)
        let num=personGoodList.reduce((total,item)=>total+item.count,0)
        if(num<goodNum){
            msg='需要消耗'+goodName+'*20,当前拥有'+goodName+'*'+num+'<br/>'
        }else{
            personGoodList.forEach(item=>{
                if(item.count>=goodNum){
                    item.count-=goodNum
                    goodNum=0
                }else{
                    goodNum-=item.count
                    item.count=0
                }
            })
        }
        return {
            msg,
            personGoodList
        }
    }
    //根据id获取物品名称
    async getGoodsName(goodsId: number[]) {
        let goodNames=[]
        for (const element of goodsId) {
            let goodInfo = await this.getGoodDetail(element)
            if (goodInfo) {
                goodNames.push({
                    id: goodInfo.id,
                    name: goodInfo.name
                })
            }
        }
        return goodNames
    }
    async getGoodsNum(userId: number, goodsId: number[]) {
        let personGoods=await this.personGoodsEntity.find({where:{userId,good:{id:In(goodsId)}},relations:['good']})
        for (const element of goodsId) {
            if(personGoods.findIndex(item=>item.good.id==element)==-1){
                let goodInfo=await this.getGoodDetail(element)
                let pg=new PersonGoodsEntity()
                pg.good=goodInfo
                pg.userId=userId
                pg.count=0
                personGoods.push(pg)
            }
        }
        return personGoods
        
    }
    //根据物品id获取物品详情
    async getGoodDetail(goodId: number) {
        let npcInfo = await this.redis.hget('goodInfo', String(goodId))
        if (npcInfo==null||npcInfo=='null') {
            let npcInfo = await this.goodEntity.findOne({ where: { id: goodId } })
            await this.redis.hset('goodInfo', String(goodId), JSON.stringify(npcInfo))
            return npcInfo
        }else{
            return JSON.parse(npcInfo) as GoodEntity
        }
    }
    //根据物品id获取物品详情
    async getGoodDetailByName(goodName: string) {
        let goodId=await this.commonService.getGoodIdByName(goodName)
        let npcInfo = await this.redis.hget('goodInfo', String(goodId))
        if (npcInfo==null||npcInfo=='null') {
            let npcInfo = await this.goodEntity.findOne({ where: { id: goodId } })
            await this.redis.hset('goodInfo', String(goodId), JSON.stringify(npcInfo))
            return npcInfo
        }else{
            return JSON.parse(npcInfo) as GoodEntity
        }
    }
    // 加/减物品数量 不做判断
    /**
     * 
     * @param userId 用户id
     * @param goodId 物品id
     * @param count 数量
     * @param type add加 sub减
     * @returns 
     */
    async changePersonGood(userId:number, goodId:number, count:number,type:'add'|'sub') {
        // let good = await this.getGoodNum(Number(userId), Number(goodId))
        let info=await this.personGoodsEntity.findOneBy({userId:userId,good:{id:goodId}})
        if(!info&&type=='add'){
            info=new PersonGoodsEntity()
            info.userId=userId
            info.good=await this.getGoodDetail(goodId)
            info.count=count
        }else{
            type=='add'?info.count+=count:info.count-=count
        }
        return await this.personGoodsEntity.save(info)
    }
    //根据物品名称加/减物品数量 不做判断
    async changePersonGoodByName(userId:number, goodName:string, count:number,type:'add'|'sub') {
        // let good = await this.getGoodNum(Number(userId), Number(goodId))
        let goodInfo=await this.getGoodDetailByName(goodName)
        let info=await this.personGoodsEntity.findOneBy({userId:userId,good:{name:goodName}})
        if(!info||goodInfo.isStack==1){
            info=new PersonGoodsEntity()
            info.userId=userId
            //如果是装备 就复制攻击防御生命 耐久 孔数
            if([2,3,4,5,6,7].includes(goodInfo.type)){
                info.attack=goodInfo.addatk
                info.defense=goodInfo.adddef
                info.hp=goodInfo.addhp
                info.uselevel=goodInfo.useLevel
                info.durability=goodInfo.durability||99999
            }
            info.good=goodInfo
            if(!info.good){
                this.logger.error('物品名称不存在'+goodName)
            }
            info.count=Number(count)
        }else{
            type=='add'?info.count+=Number(count):info.count-=Number(count)
        }
        await this.dataSource.transaction(async (manager) => {
            let res=await manager.save(info)
            if(goodInfo.holeCount){
                let arr=[]
                for (let index = 0; index < goodInfo.holeCount; index++) {
                    let personGoodHole=new PersonGoodsHoleEntity()
                    personGoodHole.equipId=res.id
                    personGoodHole.holeName=String(index+1)
                    arr.push(personGoodHole)
                }
                await manager.save(arr)
                this.eventEmitter.emit('updateUserWeight',userId)
            }
        })
    }

    //判断该物品是否能使用
    /**
     * 
     * @param type 类型 general solider user
     * @param itemId 玩家id/武将id/士兵id
     * @param goodId 物品id
     * @param fightIng 是否战斗中
     */
    async canUse(type: string,itemId:number, goodId: number,fightIng=false) {
        let msg=''
        let personGoodInfo=await this.personGoodsEntity.findOne({where:{id:goodId},relations:['good']});
        let goodInfo=personGoodInfo?.good;
        let itemInfo:any;
        //玩家使用  武将使用 npc使用
        if(type=='general'){
            if(!goodInfo.generalUse)return '武将不能使用'+goodInfo.name+'</br>'
            itemInfo=await this.generalEntity.findOne({ where: { id: Number(itemId) },select:{level:true} })

        }
        if(type=='soldier'){
            if(!goodInfo.soldierUse)return '士兵不能使用'+goodInfo.name+'</br>'
            itemInfo=await this.soldierEntity.findOne({ where: { id: Number(itemId) },select:{level:true} })
        }
        if(type=='user'){
            if(!goodInfo.roleUse)return '玩家不能使用'+goodInfo.name+'</br>'
            itemInfo=await this.roleEntity.findOne({ where: { id: Number(itemId) },select:{level:true} })
        }
        if(goodInfo.fightUse){
            if(!fightIng)return '战斗中才能使用'+goodInfo.name+'</br>'
        }
        if(goodInfo.useLevel&&goodInfo.useLevel>itemInfo.level)return '等级不够，不能使用'+goodInfo.name+'</br>'
        return ''
    }
    // //使用物品逻辑处理
    // /**
    //  *
    //  * @param type 类型 general solider user
    //  * @param userId 玩家id
    //  * @param usegoodId 物品id
    //  */ 
    // async useGood(type: string,userId:number, usegoodId: number) {
    //     let personGoodInfo=await this.personGoodsEntity.findOne({where:{id:usegoodId},relations:['good']})
    //     if(!personGoodInfo||personGoodInfo.count<1){
    //         return '物品数量不足<br/>';
    //     }
    //     let msg=await this.canUse('user',Number(userId),usegoodId)
    //     if(!msg){
    //         const goodInfo = personGoodInfo.good
    //         await this.dataSource.transaction(async (manager) => {
    //             switch (goodInfo.name) {
    //                 case '魄力丹':
    //                     let userInfo=await this.roleEntity.findOneBy({id:Number(userId)})
    //                     if(userInfo.soul>=userInfo.level){
    //                         return msg='魄力丹使用失败，魄力值不能超过玩家等级</br>'
    //                     }else{
    //                         await manager.update(RoleEntity, { id: Number(userId) }, { soul: () => `soul + 1` })
    //                         msg='使用魄力丹成功，增加1点魄力</br>'
    //                     }
    //                     break; 
    //                 case '狂暴礼包':
    //                     const items = ['狂暴一','狂暴二','狂暴三','狂暴奇书','极度狂暴奇书'];
    //                     let psersonGoods=await manager.find(PersonGoodsEntity,{where:{userId,good:{name:In(items)}},relations:['good']})
    //                     for (const item of items) {
    //                         let pg=psersonGoods.find(item1=>item1.good.name==item)
    //                         let count=0;
    //                         switch (item) {
    //                             case '狂暴一':
    //                                 count=5
    //                                 break;
    //                             case '狂暴二':
    //                                 count=5
    //                                 break;
    //                             case '狂暴三':
    //                                 count=5
    //                                 break;
    //                             case '狂暴奇书':
    //                                 count=10
    //                                 break;
    //                             case '极度狂暴奇书':
    //                                 count=10
    //                                 break;
    //                         }
    //                         if(pg){
    //                             pg.count+=count
    //                         } else{
    //                             pg=new PersonGoodsEntity()
    //                             pg.userId=userId
    //                             pg.count=count
    //                             pg.good=await this.goodEntity.findOneBy({name:item})
    //                             psersonGoods.push(pg)
    //                         }
    //                     }
    //                     await manager.save(psersonGoods)
    //                     msg='使用狂暴礼包成功，得到狂暴一*5、狂暴二*5、狂暴三*5、狂暴奇书*10、极度狂暴奇书*10、</br>'
    //                     break;
    //                 default:
    //                     break;
    //             }
    //             if(personGoodInfo.count>1){
    //                 await manager.update(PersonGoodsEntity, { id:usegoodId }, { count: () => `count - 1` }) 
    //             }else{
    //                 await manager.delete(PersonGoodsEntity, { id:usegoodId })
    //             }
    //         })
    //         await this.updateUserWeight(Number(userId))
    //     }
    //     return msg
    // }
    //设置返回路由
    async backRouter(userId, params, urlObj) {
        let str = ''
        //返回路由
        let routerList = JSON.parse(await this.redis.hget('user' + userId, 'routerList'))
        routerList.reverse().shift()
        routerList.forEach(element => {
            if(!element.params.hidden){
                str += `<a href="${this.seturlOther(params, urlObj, element.title, element.name, element.params, element.service)}">返回${element.title}</a><br/>`
            }
        });
        return str 
    }
    //获取上一级路由
    async getPrevRouter(userId) {
        let str = ''
        //返回路由
        let routerList = JSON.parse(await this.redis.hget('user' + userId, 'routerList'))
        routerList.reverse().shift()
        routerList.reverse().shift()
        let result = routerList.shift()
        return result
    }
    /**
     * 
     * @param params cnd sid参数 
     * @param urlObj url集合
     * @param title 页面标题
     * @param name 页面名称
     * @param param 请求参数
     * @param service 页面服务名
     * @returns string 页面url
}
     */
    seturlOther(params, urlObj, title: string, name: string, attr = {}, service = 'goods') {
        params.cmd++
        urlObj[params.cmd] = { title, name, service, params: attr }
        return `/gcmd?sid=${params.sid}&cmd=${params.cmd}`
    }
}
