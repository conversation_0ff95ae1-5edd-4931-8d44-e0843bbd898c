import { forwardRef, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Equal, In, Like, MoreThan, Repository, UpdateResult, MoreThanOrEqual, Not } from 'typeorm';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { CommonService } from 'src/middleware/common.service';
import { RedisService } from 'src/middleware/redis.service';
import { RoleEntity } from 'src/entities/role.entity';
import { GuildEntity } from 'src/entities/guild/guild.entity';
import { GuildMemberEntity } from 'src/entities/guild/guildMember.entity';
import { GuildApplicationEntity } from 'src/entities/guild/guildApplication.entity';
import { GuildHallEntity } from 'src/entities/guild/guildHall.entity';
import { TerritoryEntity } from 'src/entities/guild/territory.entity';
import { UserConfigProvider } from 'src/common/user-config.provider';

@Injectable()
// 帮派相关服务
export class GuildService {
    private readonly serviceName = 'guild'
    private readonly size = 20
    private userSetting: any

    constructor(
        private readonly commonService: CommonService,
        private eventEmitter: EventEmitter2,
        private readonly redis: RedisService,
        private readonly userConfigProvider: UserConfigProvider,
        @InjectRepository(RoleEntity) private readonly roleEntity: Repository<RoleEntity>,
        @InjectRepository(GuildEntity) private readonly guildEntity: Repository<GuildEntity>,
        @InjectRepository(GuildMemberEntity) private readonly guildMemberEntity: Repository<GuildMemberEntity>,
        @InjectRepository(GuildApplicationEntity) private readonly guildApplicationEntity: Repository<GuildApplicationEntity>,
        @InjectRepository(GuildHallEntity) private readonly guildHallEntity: Repository<GuildHallEntity>,
        @InjectRepository(TerritoryEntity) private readonly territoryEntity: Repository<TerritoryEntity>,
        private dataSource: DataSource,
    ) {
        this.userSetting = this.userConfigProvider.getUserSetting()
    }

    // 帮派主页
    async guildMain(sid: string, cmd: number, userId: string,{guildId}) {
        let msg = '';
        let pageTitle = '帮派';
        const { params, urlObj, backRouter } = await this.commonService.initLink(sid, cmd, userId, this.serviceName, {}, pageTitle);
        
        // 获取用户信息
        let userInfo = await this.roleEntity.findOne({ where: { id: Number(userId) } });
        let guildInfo = await this.guildEntity.findOne({ where: { id: guildId } });
        let content = `[${guildInfo.name}]
            宗旨:${guildInfo.description||''}
            级别:${guildInfo.level} [${guildInfo.exp}/9261]
            成员数:${guildInfo.currentMembers}/${guildInfo.maxMembers}
            领地:2/5 [沂领领地,景阳冈领地]
            ${guildInfo.leaderTitle}:<a href="">${guildInfo.leaderName}</a>
            副${guildInfo.leaderTitle}:<a href="">小姐便宜点</a>
            ${guildInfo.name}下设有:
            1.自由帮众[10/~]:-
            <a href="">帮派管理</a>`
        content += backRouter;
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800);
        return content;
    }
}
