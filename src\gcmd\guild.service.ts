import { forwardRef, HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Equal, In, Like, MoreThan, Repository, UpdateResult, MoreThanOrEqual, Not } from 'typeorm';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { CommonService } from 'src/middleware/common.service';
import { RedisService } from 'src/middleware/redis.service';
import { RoleEntity } from 'src/entities/role.entity';
import { GuildEntity } from 'src/entities/guild/guild.entity';
import { GuildMemberEntity } from 'src/entities/guild/guildMember.entity';
import { GuildApplicationEntity } from 'src/entities/guild/guildApplication.entity';
import { GuildHallEntity } from 'src/entities/guild/guildHall.entity';
import { TerritoryEntity } from 'src/entities/guild/territory.entity';
import { UserConfigProvider } from 'src/common/user-config.provider';
import { guildConfig } from 'src/utils/config1';

@Injectable()
// 帮派相关服务
export class GuildService {
    private readonly serviceName = 'guild'
    private userSetting: any

    constructor(
        private readonly commonService: CommonService,
        private eventEmitter: EventEmitter2,
        private readonly redis: RedisService,
        private readonly userConfigProvider: UserConfigProvider,
        @InjectRepository(RoleEntity) private readonly roleEntity: Repository<RoleEntity>,
        @InjectRepository(GuildEntity) private readonly guildEntity: Repository<GuildEntity>,
        @InjectRepository(GuildMemberEntity) private readonly guildMemberEntity: Repository<GuildMemberEntity>,
        @InjectRepository(GuildApplicationEntity) private readonly guildApplicationEntity: Repository<GuildApplicationEntity>,
        @InjectRepository(GuildHallEntity) private readonly guildHallEntity: Repository<GuildHallEntity>,
        @InjectRepository(TerritoryEntity) private readonly territoryEntity: Repository<TerritoryEntity>,
        private dataSource: DataSource,
    ) {
        this.userSetting = this.userConfigProvider.getUserSetting()
    }

    // 帮派主页
    async guildMain(sid: string, cmd: number, userId: string,{guildId}) {
        let msg = '';
        let pageTitle = '帮派';
        const { params, urlObj, backRouter } = await this.commonService.initLink(sid, cmd, userId, this.serviceName, {}, pageTitle,'guildMain');
        
        // 获取用户信息
        let [userInfo,guildInfo,guildHallList,territoryList]=await Promise.all([
            this.roleEntity.findOne({ where: { id: Number(userId) } }),
            this.guildEntity.findOne({ where: { id: guildId } }),
            this.guildHallEntity.find({ where: { guildId } }),
            this.territoryEntity.find({ where: { guildId } }),
        ])
        let str=''
        guildHallList.forEach((item,index)=>{
            str+=`${index+1}.${item.name}(${item.memberCount}人)`
            if(item.leaderTitle!='自由帮众'){
                str+=`:${item.leaderName?`<a href=''>${item.leaderName}</a>`:''}`
            }
            str+='<br/>'
        })
        let str1=''
        if(guildInfo.leaderName1){
            str1+=`<a href="${this.commonService.seturlOther(params, urlObj, guildInfo.leaderName1, 'myStatus', { targetUserId:guildInfo.leaderId1 },'gcmd')}">${guildInfo.leaderName1}</a><br/>`
        }
        if(guildInfo.leaderName2){
            str1+=` <a href="${this.commonService.seturlOther(params, urlObj, guildInfo.leaderName2, 'myStatus', { targetUserId:guildInfo.leaderId2 },'gcmd')}">${guildInfo.leaderName2}</a><br/>`
        }
        let content = `[${guildInfo.name}]<br/>
            宗旨:${guildInfo.description||''}<br/>
            级别:${guildInfo.level} [${guildInfo.exp}/${guildConfig.exp[guildInfo.level-1]}]<br/>
            成员数:${guildInfo.currentMembers}/${guildConfig.maxMembers[guildInfo.level-1]}<br/>
            领地:${territoryList.length}/${guildConfig.territoryNum[guildInfo.level-1]} [${territoryList.map(item=>item.name).join()}]<br/>
            ${guildInfo.leaderTitle}:<a href="${this.commonService.seturlOther(params, urlObj, guildInfo.leaderName, 'myStatus', { targetUserId:userId },'gcmd')}">${guildInfo.leaderName}</a><br/>
            副${guildInfo.leaderTitle}:${str1}<br/>
            ${guildInfo.name}下设有:<br/>
            ${str}
            <a href="${this.commonService.seturlOther(params, urlObj, '帮派管理', 'guildSetting', { targetUserId:userId },this.serviceName)}">帮派管理</a><br/>`
        content += backRouter;
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800);
        return content;
    }
    //帮派管理
    async guildSetting(sid: string, cmd: number, userId: string,{guildId}) {
        let msg = '';
        let pageTitle = '帮派管理';
        const { params, urlObj, backRouter } = await this.commonService.initLink(sid, cmd, userId, this.serviceName, {}, pageTitle,'guildSetting');

        let content = `<a href="">帮派状态</a><br/>
            <a href="">帮派资源</a><br/>
            <a href="">帮派仓库</a><br/>
            <a href="">帮派成员</a><br/>
            <a href="">帮派领地</a><br/>
            <a href="">职务任免</a><br/>
            <a href="">基本资料</a><br/>
            <a href="">分堂机构</a><br/>
            <a href="">退出帮派</a><br/>`
        content += backRouter;
        await this.redis.hset('user' + userId, 'routers', JSON.stringify(urlObj), 1800);
        return content;
    }
}
