import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import rateLimit from 'express-rate-limit';
import { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';
import { NestExpressApplication } from '@nestjs/platform-express';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  const corsOptions: CorsOptions = {
    origin: true, // 允许所有来源
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE', // 允许的 HTTP 方法
    preflightContinue: false,
    optionsSuccessStatus: 204,
  };
  app.useStaticAssets('public', {
    prefix: '/static/'
  })
  app.enableCors(corsOptions);
  app.useGlobalPipes(new ValidationPipe({
    disableErrorMessages: false,//关闭错误信息提示
    transform: true,
    whitelist: true,//开启白名单模式，只验证包含在白名单中的字段 不存在的删除
  }));
  // 设置访问频率
  app.use(
    rateLimit({
      windowMs: 1000,
      max: 5, // 限制每秒最多访问3次
      message: '访问过于频繁，请稍后再试',
    }),
  );
  await app.listen(5140);
}
bootstrap();
