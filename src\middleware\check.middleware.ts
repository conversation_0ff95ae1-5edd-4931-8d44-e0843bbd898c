import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { RedisService } from './redis.service';
import { UserConfigProvider } from 'src/common/user-config.provider';
import { ConfigEntity } from 'src/entities/config.entity';
import { Equal, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class CheckMiddleware implements NestMiddleware {
    constructor(
        private readonly redis: RedisService,
        private readonly userConfigProvider: UserConfigProvider,
        @InjectRepository(ConfigEntity) private readonly configEntity: Repository<ConfigEntity>
    ) {}
    async use(req: Request, res: Response, next: NextFunction) {
        const sid = req.query.sid as string;
        const cmd = req.query.cmd as string;
        if (!sid || !cmd) {
            return res.status(400).json({
                code: 400,
                message: '小伙子，你是不是忘记什么东西了？'
            });
        }
        let userId = await this.redis.hget('sids',sid);
        if (!userId) {
            return res.status(401).json({
                code: 401,
                message: '账号已下线，请重新登录'
            });
        }
        let routers = await this.redis.hget('user'+userId,'routers');
        if(!routers){
            return res.status(401).json({
                code: 401,
                message: '账号已下线，请重新登录'
            });
        }
        this.userConfigProvider.setConfig({
            userId,sid,cmd
        });
        let userSetting=await this.getUserSetting(Number(userId))
        this.userConfigProvider.setUserSetting(userSetting)
        next();
    }
    //获取用户设置
    async getUserSetting(userId:number){
        let userSetting:any=await this.redis.hget('user'+userId,'userSetting')
        if(!userSetting){
            let configInfo=await this.configEntity.findOneBy({userId:Equal(userId)})
            if(!configInfo){
                configInfo=new ConfigEntity()
                configInfo.userId=userId
                let res=await this.configEntity.save(configInfo)
                userSetting=res
                await this.redis.hset('user'+userId,'userSetting',JSON.stringify(userSetting))
            }else{
                await this.redis.hset('user'+userId,'userSetting',JSON.stringify(configInfo))
                userSetting=configInfo
            }
        }else{
            userSetting=JSON.parse(userSetting)
        }
        return userSetting
    }
}

//验证 sid和cmd是否存在