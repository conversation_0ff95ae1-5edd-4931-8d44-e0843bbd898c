import { ExceptionFilter, Catch, ArgumentsHost, HttpException } from '@nestjs/common';
import { Request, Response } from 'express';

@Catch(HttpException)
//处理异常 刷新当前页面
export class HttpExceptionFilter implements ExceptionFilter {
    catch(exception: HttpException, host: ArgumentsHost) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();
        let responseObj:any = exception.getResponse();
        let str=''
        responseObj.message?.forEach((item:any)=>{
            str+=`<p style="color:red;">${item}</p>`
        })
        response.send(`
            ${str}
            <a href="javascript:void(0)" id="myLink">即将跳转中，立即跳转</a>
            <script>
                document.getElementById('myLink').addEventListener('click', function(event) {
                    event.preventDefault();
                    jump()
                });
                function jump(){
                    window.location.href = window.location.href;
                }
                setTimeout(jump, 2000)
            </script>
            `);
    }
}