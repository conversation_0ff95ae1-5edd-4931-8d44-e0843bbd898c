import { InjectRedis } from '@nestjs-modules/ioredis';
import { Injectable } from '@nestjs/common';
import Redis from 'ioredis';

@Injectable()
export class RedisService {
    constructor(@InjectRedis() private readonly redis: Redis) { 
    }
    async selectDb(dbIndex: number) {
        await this.redis.select(dbIndex);
    }
    //存储哈希
    async hset(name: string,key: string, value: string,expire=0) {

        
        // value=JSON.stringify(value);
        await this.redis.hset(name,key, value);
        if(expire){
            await this.redis.expire(name,expire); 
        }
        
    }
    async hget(name: string,key: string) {
        
        let result = await this.redis.hget(name,key);
        return result;
    }
    
    // 支持通配符的hget方法
    async hgetByPattern(name: string, pattern: string) {
        const keys = await this.redis.hkeys(name);
        const matchedKeys = keys.filter(key => {
            // 将通配符模式转换为正则表达式
            const regexPattern = pattern.replace(/\*/g, '.*').replace(/\?/g, '.');
            const regex = new RegExp(`^${regexPattern}$`);
            return regex.test(key);
        });
        
        if (matchedKeys.length === 0) {
            return {keyName:null,keyValue:null};
        }
        
        // 返回第一个匹配的结果
        return {keyName:matchedKeys[0],keyValue:await this.redis.hget(name, matchedKeys[0])};
    }
    
    // 获取所有匹配的field-value对
    async hgetAllByPattern(name: string, pattern: string) {
        const keys = await this.redis.hkeys(name);
        const matchedKeys = keys.filter(key => {
            const regexPattern = pattern.replace(/\*/g, '.*').replace(/\?/g, '.');
            const regex = new RegExp(`^${regexPattern}$`);
            return regex.test(key);
        });
        
        if (matchedKeys.length === 0) {
            return {};
        }
        
        const result = {};
        for (const key of matchedKeys) {
            result[key] = await this.redis.hget(name, key);
        }
        return result;
    }
    async hdel(name: string,key: string) {
        
        await this.redis.hdel(name,key);
    }
    async lpush(name: string,value: string) {
        
        await this.redis.lpush(name,value);
    }
    async rpush(name: string,value: string) {
        
        await this.redis.rpush(name,value);
    }
    async lrange(name: string,start: number,end: number) {
        
        let result = await this.redis.lrange(name,start,end);
        return result;
    }
    async ltrim(name: string,start: number,end: number) {
        
        await this.redis.ltrim(name,start,end); 
    }
    async setnx(name: string,value='lock') {
        
        return await this.redis.setnx(name,value);
    }
    async del(name: string) {
        
        await this.redis.del(name); 
    }
    async delAll() {
        //删除所有db0的key
        
        await this.redis.flushdb();
        //删除所有db1的key
        await this.selectDb(1);
        await this.redis.flushdb();
    }
    async ttl(name: string) {
        
        return await this.redis.ttl(name); 
    }
    async keys(name:string){
        
        return await this.redis.keys(name);
    }
    //存储字符串
    async setstr(name: string,value: string,expire=0) {
        // 't'+npcInfo.id+'u'+String(teamRole.team.id)+'d'+dungeonName
        await this.selectDb(1);
        await this.redis.set(name,value);
        if(expire){
            await this.redis.expire(name,expire); 
        }
        await this.selectDb(0);

    }
    async getstr(name: string) {
        await this.selectDb(1);
        let result = await this.redis.get(name);
        await this.selectDb(0);
        return result; 
    }
    //获取字符串过期时间
    async getstrTTL(name: string) {
        await this.selectDb(1);
        let result = await this.redis.ttl(name);
        await this.selectDb(0);
        return result; 
    }
    //删除字符串
    async delstr(name: string) {
        await this.selectDb(1);
        await this.redis.del(name);
        await this.selectDb(0);
    }

}
