import { CommonService } from './../middleware/common.service';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { Injectable } from '@nestjs/common';
import { Cron, SchedulerRegistry,Interval} from '@nestjs/schedule';
import Redis from 'ioredis';
import { RedisService } from 'src/middleware/redis.service';
@Injectable()
export class TasksService {
    constructor(
        private schedulerRegistry: SchedulerRegistry,
        private readonly commonService: CommonService,
        @InjectRedis() private readonly redisService: RedisService,
    ) {}
    
    // @Cron('45 * * * * *')
    // handleCron() {
    //     // console.log('该方法将在45秒标记处每分钟运行一次');
    // }
    //5分钟检查过期如果快过期更新玩家的gps到数据库
    @Interval(1000*60*5)
    async handleInterval() {
        const keys = await this.redisService.keys('user*');
        // console.log('Checking expiration for keys,5 minutes', keys);
        keys.forEach(async (key) => {
            const ttl = await this.redisService.ttl(key);
            if (ttl < 300) {
                this.commonService.userOffline(Number(key.split('user')[1]));
                console.log(`Key ${key} has no expiration`);
            } 
        });
    }

    // @Timeout(5000)
    // handleTimeout() {
    //     this.logger.debug('3');
    // }
}