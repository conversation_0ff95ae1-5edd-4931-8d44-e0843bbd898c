import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EventSubscriber, EntitySubscriberInterface, UpdateEvent } from 'typeorm';
import { FightEntity } from '../entities/fight.entity';
import { RedisService } from '../middleware/redis.service';
import { TasksService } from '../tasks/tasks.service';

@Injectable()
@EventSubscriber()
export class FightSubscriber implements EntitySubscriberInterface<FightEntity> {
    constructor(
        @InjectRepository(FightEntity)
        private readonly fightEntity: typeof FightEntity,
        private readonly redisService: RedisService,
        private readonly tasksService: TasksService,
    ) {}

    listenTo() {
        return FightEntity;
    }

    async afterUpdate(event: UpdateEvent<FightEntity>) {
        // 确保实体存在
        if (!event.entity || !event.databaseEntity) {
            // console.log('实体不存在');
            return;
        }

        const fight = event.entity;
        const previousFight = event.databaseEntity;

        // 检查战场状态是否发生变化
        if (fight.status !== previousFight.status) {
            // 如果战场状态变为结束状态(2,3,4)
            if (fight.status > 1) {
                console.log('战场结束，开始清理资源', {
                    fightId: fight.id,
                    status: fight.status
                });

                // 清理战场定时器
                // this.tasksService.clearInterval('fight' + fight.id);
                
                // // 清理地图战场缓存
                // await this.redisService.hdel('gpsfight', String(fight.gpsId));

                // // 如果是NPC战斗，清理NPC信息缓存
                // if (fight.defenseIsNpc === 1) {
                //     const npcId = fight.defenseId.split('npc')[1]?.split('user')[0];
                //     if (npcId) {
                //         await this.redisService.hdel('npmInfoTemp', npcId);
                //     }
                // }
            }
        }
    }
} 