import { Injectable } from '@nestjs/common';
import { EventSubscriber, EntitySubscriberInterface, RemoveEvent } from 'typeorm';
import { TeamRolesEntity } from '../entities/teamRoles.entity';

@Injectable()
@EventSubscriber()
export class TeamRolesSubscriber implements EntitySubscriberInterface<TeamRolesEntity> {
    listenTo() {
        return TeamRolesEntity;
    }

    async beforeRemove(event: RemoveEvent<TeamRolesEntity>) {
        const teamRole = event.entity;
        if (!teamRole) {
            console.log('要删除的TeamRoles实体不存在');
            return;
        }
        //修改用户的gpsId
        
    }
} 