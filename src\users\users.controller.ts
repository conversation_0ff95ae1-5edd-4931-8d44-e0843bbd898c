import { Controller, Get, Post, Body, Patch, Param, Delete, HttpException, HttpStatus, Query, ParseIntPipe, Redirect,Response, UsePipes, ValidationPipe, UseFilters } from '@nestjs/common';
import { UsersService } from './users.service';
import { LoginUserDto } from 'src/dto/login-user.dto'
import { RegisterUserDto } from 'src/dto/register-user.dto';
import { showMsg } from 'src/utils/htmlCode';
import { SignDto } from 'src/dto/sign.dto';
import { SelectRoleDto } from 'src/dto/selectRole.dto';
import { CreateRoleDto } from 'src/dto/create-role.dto';
import { HttpExceptionFilter } from 'src/middleware/http-exception.filter';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}
  @Get('ceshi1')
  ceshi1() {
    return this.usersService.ceshi1();
  }
  //登录
  @Get('login')
  getLogin() {
    return this.usersService.getLogin();
  }
  //注册
  @Get('register')
  getRegister() {
    return this.usersService.getRegister();
  }
  //重置密码
  @Get('updatePass')
  getUpdatePass() {
    return this.usersService.getUpdatePass();
  }
  //选择分区
  @Get('selectArea/:sign')
  getSelectArea(@Param() params:SignDto) {
    return this.usersService.getSelectArea(params.sign);
  }
  //选择角色
  @Get('selectRole/:sign/:areaId')
  getSelectRole(@Param() params:SelectRoleDto) {
    return this.usersService.getSelectRole(params.sign,params.areaId);
  }
  //创建角色
  @Get('createRole/:sign/:areaId')
  getCreateRole(@Param() params:SelectRoleDto) {
    return this.usersService.getCreateRole(params.sign,params.areaId);
  }
  //进入游戏
  @Get('gcmdRole/:sign')
  async getGcmdRole(@Param() params:SignDto,@Response() res) {
    let result=await this.usersService.getGcmdRole(params.sign);
    if(typeof result=='string'){
      return res.send(result)
    }else{
      return res.redirect('/gcmd?sid='+result.sid+'&cmd=0')
    }
  }



  @Post('login')
  @UseFilters(HttpExceptionFilter)
  async postLogin(@Body() loginUserDto: LoginUserDto) {
    let result = await this.usersService.postLogin(loginUserDto);
    return showMsg(result)
  }
  @Post('register')
  @UseFilters(HttpExceptionFilter)
  async postRegister(@Body() registerUserDto: RegisterUserDto) {
    let result = await this.usersService.postRegister(registerUserDto);
    console.log(result)
    return showMsg(result)
  }
  @Post('updatePass')
  @UseFilters(HttpExceptionFilter)
  async postUpdatePass(@Body() registerUserDto: RegisterUserDto) {
    let result = await this.usersService.postUpdatePass(registerUserDto);
    return showMsg(result)
  }
  @Post('createRole/:sign/:areaId')
  async postCreateRole(@Param() params:SelectRoleDto,@Body() createRoleDto: CreateRoleDto) {
    let result = await this.usersService.postCreateRole(params.sign,params.areaId,createRoleDto);
    return result
  }
}
