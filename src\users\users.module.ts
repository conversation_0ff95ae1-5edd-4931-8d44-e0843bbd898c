import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { UserEntity } from 'src/entities/user.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AreaEntity } from 'src/entities/area.entity';
import { RoleEntity } from 'src/entities/role.entity';
@Module({
  imports: [
    TypeOrmModule.forFeature([UserEntity,AreaEntity,RoleEntity])
  ],
  controllers: [UsersController],
  providers: [UsersService],
})
export class UsersModule {}
