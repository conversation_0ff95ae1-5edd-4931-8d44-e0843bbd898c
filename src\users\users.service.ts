import { Injectable, Param } from '@nestjs/common';
import { htmlLogin,htmlRegister,htmlUpdatePass, showMsg } from 'src/utils/htmlCode';
import Redis from 'ioredis';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { LoginUserDto } from 'src/dto/login-user.dto'
import { UserEntity } from 'src/entities/user.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import * as CryptoJS from 'crypto-js';
import { RegisterUserDto } from 'src/dto/register-user.dto';
import { AreaEntity } from 'src/entities/area.entity';
import { RoleEntity } from 'src/entities/role.entity';
import { defaultConfig } from 'src/utils/config';
import { CreateRoleDto } from 'src/dto/create-role.dto';
import SensitiveWordTool from 'sensitive-word-tool'
import { RedisService } from 'src/middleware/redis.service';
@Injectable()
export class UsersService {
  constructor(
    private readonly redisService: RedisService,
    @InjectRepository(UserEntity) private readonly userEntity:Repository<UserEntity>,
    @InjectRepository(AreaEntity) private readonly areaEntity:Repository<AreaEntity>,
    @InjectRepository(RoleEntity) private readonly roleEntity:Repository<RoleEntity>,
  ) {
  }
  async ceshi1(){
    console.log('ceshi1')
    await this.redisService.hset('myname','name','lw')
    // this.redis.ttl('myname').then((ttl) => {
    //   console.log('TTL for mykey:', ttl);
    // });
    return '设置成功'
  }
  // 登录
  async getLogin(){
    return htmlLogin
  }
  // 注册
  async getRegister(){
    return htmlRegister
  }
  // 修改密码
  async getUpdatePass(){
    return htmlUpdatePass
  }
  //选择分区
  async getSelectArea(sign:string){
    let result=await this.userEntity.findOne({where:{sign:sign}}) 
    if(!result){
      return showMsg({msg:'书签不存在或已失效',url:'/users/login'})
    }
    let areas=await this.areaEntity.find()
    let as=''
    areas.forEach(item=>{
      as+=`<a href="/users/selectRole/${sign}/${item.id}">${item.name}</a><br />`
    })
    let html=`<div style="background:blue; color:#FFF">
                <b>
                    <font size="5">选择帝王分区:</font>
                </b>
            </div>
            <br />
            (十五年经典帝王,十五年青春回忆)<br /><br />
            [${result.userName}]请存此页为书签<br />

            <br />
            ----<br />
            请选择分区
            <br /><br />
            ${as}
            <br />
            ======
            <br />
            <a href="">令此前书签失效</a>
            <br />
            <br />
            [纵横天地，一世帝王]
            <br />
            将此页设为书签，下次可以直接进入游戏。
            <br />`
    return defaultConfig.htmlHeader+html+defaultConfig.htmlfooter
  }
  //选择角色
  async getSelectRole(sign:string,areaId:number){
    let obj={msg:'未知错误',url:'/users/login'}
    let userId=await this.checkSign(sign,areaId)
    if(userId){
      let resultrole=await this.roleEntity.findOne({where:{areaId,userId}})
      let str=''
      if(!resultrole){
        str+='<a href="/users/createRole/'+sign+'/'+areaId+'">创建角色</a><br/>'
      }else{
        str+=`<a href="/users/gcmdRole/${resultrole.sid}">${resultrole.name}(${resultrole.level}级,${resultrole.gender?'女':'男'})</a><br/>`
      }
      let html=`<p>[请选择角色进入游戏]：<br/>
      ${str}
      ----------------<br/>
      经典游戏,文字艺术的魅力！<br/>
      <br/>
      如果遇到问题请不要重复提交，我们会在工作日内为您服务<br/>
      </p>`
      return defaultConfig.htmlHeader+html+defaultConfig.htmlfooter
    }
    return showMsg(obj)
  }
  //创建角色
  async getCreateRole(sign:string,areaId:number,error?:string){
    let obj={msg:'未知错误',url:'/users/login'}
    let userId=await this.checkSign(sign,areaId)
    if(userId){
      let html=`<p style="color:red;" id="error" style="display:none"></p>
      <p align='left'>
        <p>请输入角色信息：</p>		
        <form action="/users/createRole/${sign}/${areaId}" method="post">
            *名字(6个字内):<input name="username" type="text" value="" maxlength="6" /><br />
            *性别:<select name="gender" value="0">
                <option value="0" selected="selected">男</option>
                <option value="1">女</option>
            </select><br />
            <input name="submit" type="button" onclick="sendRequest('/users/createRole/${sign}/${areaId}')" title="创建" value="创建" />
        </form><br />
        <a href="/users/selectRole/${sign}/${areaId}">返回上级</a><br />
      </p>${defaultConfig.sendRequest}`
      return defaultConfig.htmlHeader+html+defaultConfig.htmlfooter
    }
    return showMsg(obj)
  }
 //进入游戏
  async getGcmdRole(sid:string){
    let roleInfo=await this.roleEntity.findOne({where:{sid:sid}})
    if(!roleInfo)return showMsg({msg:'角色不存在或已失效',url:'/users/login'})
    let oldsid=roleInfo.sid
    roleInfo.sid=CryptoJS.MD5(roleInfo.name+roleInfo.id+Date.now()).toString();
    let result=await this.roleEntity.save(roleInfo)
    if(result){
      await this.redisService.hset('user'+result.id,'routers',JSON.stringify({0:{name:'home',service:'gcmd',title:'游戏首页',params:{}}}))
      await this.redisService.hset('user'+result.id,'routerList',JSON.stringify([]))
      await this.redisService.hset('user'+result.id,'userInfo',JSON.stringify(result))
      await this.redisService.hset('sids',result.sid,String(result.id))
      await this.redisService.hdel('sids',oldsid)
      return {sid:result.sid}
    }else{ 
      return showMsg({msg:'其他错误',url:'/users/login'})
    }
  }


  async postLogin(loginUserDto: LoginUserDto) {
    const obj={msg:'账户或密码不正确',url:'/users/login'}
    let result=await this.userEntity.findOne({where:{userName:loginUserDto.username}})
    if(result&&result.password==CryptoJS.MD5(loginUserDto.password).toString()){
      obj.msg='登录成功'
      obj.url='/users/selectArea/'+result.sign
    }
    return obj
  }
  
  async postRegister(registerUserDto: RegisterUserDto) {
    if(!registerUserDto.username||!registerUserDto.password||!registerUserDto.confirmPassword){
      return {msg:'账户名或密码不能为空',url:'/users/register'}
    }
    if(registerUserDto.password!=registerUserDto.confirmPassword){
      return {msg:'两次密码不一致',url:'/users/register'}
    }
    let result=await this.userEntity.findOne({where:{userName:registerUserDto.username}})
    if(result){
      return {msg:'注册失败,账户名已存在',url:'/users/register'}
    }
    let user = new UserEntity();
    user.userName = registerUserDto.username;
    user.password = CryptoJS.MD5(registerUserDto.password).toString();
    user.sign=CryptoJS.MD5(registerUserDto.username+registerUserDto.password+Date.now()).toString();
    result=await this.userEntity.save(user)
    if(result){
      return {msg:'注册成功',url:'/users/login'}
    }else{
      return {msg:'注册失败',url:'/users/register'}
    }
  }
  
  async postUpdatePass(registerUserDto: RegisterUserDto) {
    if(registerUserDto.password!=registerUserDto.confirmPassword){
      return {msg:'两次密码不一致',url:'/users/updatePass'}
    }
    let msg={msg:'修改失败,账户名不存在或密码不正确',url:'/users/updatePass'}
    let result=await this.userEntity.findOne({where:{userName:registerUserDto.username}})
    if(result&&result.password==CryptoJS.MD5(registerUserDto.password).toString()){
      result.password = CryptoJS.MD5(registerUserDto.confirmPassword).toString();
      result.sign=CryptoJS.MD5(registerUserDto.username+registerUserDto.confirmPassword+Date.now()).toString();
      result=await this.userEntity.save(result)
      if(result){
        msg={msg:'修改成功,前往登录页面',url:'/users/login'}
      }
    }
    return msg
  }
  async postCreateRole(sign:string,areaId:number,createRoleDto: CreateRoleDto) {
    let obj={msg:'未知错误'}
    let userId=await this.checkSign(sign,areaId)
    if(userId){
      if(!createRoleDto.username||!createRoleDto.gender){
        return {msg:'角色名或性别不能为空'}
      }
      //敏感词过滤
      // 初始化时使用默认敏感词
      const sensitiveWordTool = new SensitiveWordTool({
        useDefaultWords: true
      })
      if(sensitiveWordTool.verify(createRoleDto.username)){
        return {msg:'角色名存在敏感词'}
      }
      if(createRoleDto.username.length>6){
        return {msg:'名字过长,最多6个字'}
      }
      let roleInfo=await this.roleEntity.findOne({where:{areaId,name:createRoleDto.username}})
      if(roleInfo){
        return {msg:'角色名已存在'}
      }
      if(await this.roleEntity.findOne({where:{areaId,userId}})){
        return {msg:'只能创建一个角色'}
      }
      let role = new RoleEntity();
      role.name = createRoleDto.username;
      role.gender = Number(createRoleDto.gender);
      role.areaId = areaId;
      role.userId = userId;
      role.sid=CryptoJS.MD5(createRoleDto.username+userId+Date.now()).toString();
      let result=await this.roleEntity.save(role)
      if(result){
        return {url:'/users/selectRole/'+sign+'/'+areaId}
      }else{
        return {msg:'创建角色失败'}
      }
    }
    return obj
  }

   //判断账号和分区是否存在 返回账号id
  async checkSign(sign:string,areaId:number){
    let result=await this.userEntity.findOne({where:{sign:sign}})
    if(result){
      let result1=await this.areaEntity.findOne({where:{id:areaId}})
      if(result1){
        return result.id
      }
    }
    return false
  }
}
