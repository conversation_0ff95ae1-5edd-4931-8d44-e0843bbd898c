import{defaultConfig} from './config';
function handelCode(code:string){
    return defaultConfig.htmlHeader+ code+defaultConfig.htmlfooter;
}
// const default=handelCode(``)
//登录页面
const htmlLogin =handelCode(`<form action="/users/login" method="post">
十年帝王,风云再起-WAP帝王<br />
----------<br /><br />
帐号:<input name="username" maxlength="16" size="10" /><br /><br />
密码:<input name="password" maxlength="16" size="10" /><br /><br />
<button type="submit">登录游戏</button>
</form>
<br />
----------<br />
<a href='/users/register'>免费注册</a>.<a
href='/users/updatePass'>重置密码</a>
<br />
----------<br />`);
//注册页面
const htmlRegister =handelCode(`<p align='left'>
十年帝王,风云再起-WAP帝王<br/>
----------<br/>
[注册账号]<br/>
<form action="/users/register" method="post">
    输入账号:<input  name="username" maxlength="16" size="10" />(4-16位数字、字母)<br/>
    输入密码:<input  name="password" maxlength="16" size="10" />(4-16位数字、字母)<br/>
    确认密码:<input  name="confirmPassword" maxlength="16" size="10" />(4-16位数字、字母)<br/>
    <br/>
    <button type="submit">注册账号</button><br/><br/>
    (恶意注册账号将查封IP)
</form>
<a href="/users/login">返回首页</a><br/>
    </p>`);
//修改密码
const htmlUpdatePass =handelCode(`<p align='left'>
十年帝王,风云再起-WAP帝王<br/>
----------<br/>
[修改密码]<br/><br/>
<form action="/users/updatePass" method="post">
    输入账号:<input  name="username" maxlength="16" size="10" />(4-16位数字、字母)<br/>
    旧 密 码 :<input  name="password" maxlength="16" size="10" />(4-16位数字、字母)<br/>
    新 密 码 :<input  name="confirmPassword" maxlength="16" size="10" />(4-16位数字、字母)<br/>
    <br/>
    <button type="submit">修改密码</button><br/><br/>
    (恶意注册账号将查封IP)
</form>
<a href="/users/login">返回首页</a><br/>
<br/>
    </p>`);
const htmlCreateRole =handelCode(``);
//提示页面
const showMsg = ({msg,url}: {msg: string, url: string}) => {
    let content=`<html>
    <head>
    <meta http-equiv="refresh" content="1;url=${url}">
    </head>
    <body>
    <p style="color:red">${msg}</p>
    <p><a href="${url}">拼命加载中...</a></p>    
    </body>
    </html>`
    return content;
}

export{
    htmlLogin,
    htmlRegister,
    htmlUpdatePass,
    showMsg
}