// 生成地图sql  万剑塔
function generateMapSQL(startId, startName) {
    let sql = "";

    // 定义一个映射，将数字层数转换为中文汉字
    const layerToChinese = {
        1: '一',
        2: '二',
        3: '三',
        4: '四',
        5: '五',
        6: '六',
        7: '七',
        8: '八',
        9: '九'
    };

    // 辅助函数：根据层、行、列计算ID
    const getId = (layer, row, col) => {
        // 每层6x6，总共9层
        // 计算当前层的基础ID
        const baseId = startId + (layer - 1) * 36; // 36个格子一层 (6*6)
        return baseId + (row - 1) * 6 + (col - 1);
    };

    for (let layer = 1; layer <= 9; layer++) {
        for (let row = 1; row <= 6; row++) {
            for (let col = 1; col <= 6; col++) {
                let id = getId(layer, row, col);
                // 根据层数获取对应的显示名称（中文或数字）
                let displayLayer = layerToChinese[layer] || layer;

                let title = `${startName}${displayLayer} (${row},${col})`;

                // 密室逻辑
                if (row === 3 && col === 3) {
                    title = `密室${displayLayer}`;
                }

                let topId = 'NULL';    // 上方格子
                let bottomId = 'NULL'; // 下方格子
                let leftId = 'NULL';   // 左侧格子
                let rightId = 'NULL';  // 右侧格子

                // 计算 topId, bottomId, leftId, rightId
                // 根据你的新规则：
                // 1.1 的右方应该是 2.1 (行号+1，列号不变)
                // 1.1 的上方应该是 1.2 (行号不变，列号+1)

                // 右侧连接 (行号+1，列号不变)
                if (row < 6) { // 如果当前行不是6 (即不是最右侧的行，因为右侧是行增加)
                    rightId = `'${getId(layer, row + 1, col)}'`;
                }

                // 左侧连接 (行号-1，列号不变)
                if (row > 1) { // 如果当前行不是1 (即不是最左侧的行)
                    leftId = `'${getId(layer, row - 1, col)}'`;
                }

                // 上方连接 (行号不变，列号+1)
                if (col < 6) { // 如果当前列不是6 (即不是最上方的列)
                    topId = `'${getId(layer, row, col + 1)}'`;
                }

                // 下方连接 (行号不变，列号-1)
                if (col > 1) { // 如果当前列不是1 (即不是最下方的列)
                    bottomId = `'${getId(layer, row, col - 1)}'`;
                }

                // 特殊跨层连接：每层6.6的上方是下一层的1.1
                // 这里的6.6指的是第6行第6列
                if (row === 6 && col === 6 && layer < 9) {
                    topId = `'${getId(layer + 1, 1, 1)}'`;
                }

                // 特殊跨层连接：第二层的1.1下方是第一层的6.6
                // 这里的1.1指的是第1行第1列
                if (row === 1 && col === 1 && layer > 1) {
                    bottomId = `'${getId(layer - 1, 6, 6)}'`;
                }

                // 第一层1.1的特殊处理：没有leftId和bottomId
                if (layer === 1 && row === 1 && col === 1) {
                    leftId = 'NULL';
                    bottomId = 'NULL';
                }

                // 第一层6.6的特殊处理
                if (layer === 1 && row === 6 && col === 6) {
                    rightId = 'NULL';
                }
                
                // 第一层 最上方（col=6）的格子，如果不是6.6，那么它的topId为NULL
                if (layer === 1 && col === 6 && row < 6) {
                    topId = 'NULL';
                }
                
                // 第一层 最右侧（row=6）的格子，如果不是6.6，那么它的rightId为NULL
                if (layer === 1 && row === 6 && col < 6) {
                    rightId = 'NULL';
                }

                sql += `INSERT INTO \`game\`.\`maps_entity\` (\`id\`, \`title\`, \`topId\`, \`bottomId\`, \`leftId\`, \`rightId\`) VALUES ('${id}', '${title}', ${topId}, ${bottomId}, ${leftId}, ${rightId});\n`;
            }
        }
    }
    return sql;
}

// 示例用法：
// const generatedSql = generateMapSQL(10001, '万剑塔');
// console.log(generatedSql);

function generateTeleportString(startId, startName) {
    let teleportStrings = [];
    const layerToChinese = {
        1: '一', 2: '二', 3: '三', 4: '四', 5: '五',
        6: '六', 7: '七', 8: '八', 9: '九'
    };

    // 辅助函数：根据层、行、列计算ID
    const getId = (layer, row, col) => {
        const baseId = startId + (layer - 1) * 36;
        return baseId + (row - 1) * 6 + (col - 1);
    };

    // 从第二层开始到第九层
    for (let layer = 2; layer <= 9; layer++) {
        const displayLayer = layerToChinese[layer] || layer;
        // 假设传送目标是每层的1.1格子，类型为4 (示例中的 '4')
        const targetId = getId(layer, 1, 1);
        teleportStrings.push(`传送至${startName}${displayLayer}层|4|${targetId}`);
    }

    return teleportStrings.join('&');
}

// 示例用法：
const generatedTeleportString = generateTeleportString(10001, '万剑塔');
console.log(generatedTeleportString);