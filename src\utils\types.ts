type paramsType = {
    name?: string;
    info?: Array<string>;
    updateRouter?:boolean//是否更新路由
};
type RouterType={
    title:string,//页面标题
    name:string,//页面名称
    service?:string,//service名称
    params?:paramsType//请求参数
}
type UserInfoType={
    food?:number,//粮草
    wood?:number,//木材
    stone?:number,//石材
    iron?:number,//生铁
    gold?:number,//银两
    taskId?:number,//任务id
}
type TaskReward = {
    gold?: number;
    potential?: number;
    food?: number;
    wood?: number;
    stone?: number;
    iron?: number;
};

type TaskStepCondition = {
    release: string;
    triggered?: number;
};

type TaskStepFinishCondition = {
    type: string;
    lvl?: number;
    count?: number;
    goodId?: number;
};

type TaskStep = {
    id: string;
    desc: string;
    condition: TaskStepCondition;
    finishcondition?: TaskStepFinishCondition;
    taskReward?: TaskReward;
};

type TaskCondition = {
    release: string;
};

type Task = {
    id: number;
    title: string;
    condition: TaskCondition;
    steps: TaskStep[];
};

type TasksType = {
    tasks: Task[];
};
enum NpcTaskStatus {
    NOT_STARTED = 'NOT_STARTED', // 未开始
    IN_PROGRESS = 'IN_PROGRESS', // 进行中
    COMPLETED = 'COMPLETED'      // 已完成
}
enum IsCaptain {
    NO = 'NO',
    YES = 'YES'
}
enum IsTrue {
    NO = 'NO',
    YES = 'YES'
}
enum DataType{
    role='role',
    general='general',
    soldier='soldier'
}
type RouterListType=RouterType[]
export { paramsType,RouterType,RouterListType,UserInfoType,NpcTaskStatus,TasksType,IsCaptain,IsTrue,DataType };