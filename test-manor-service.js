// 测试脚本：验证 ManorService 的 onModuleInit 是否正常执行
// 运行命令: node test-manor-service.js

const { spawn } = require('child_process');

console.log('🚀 开始测试 ManorService onModuleInit 执行情况...\n');

// 启动应用并监听输出
const child = spawn('npm', ['run', 'start:dev'], {
    stdio: 'pipe',
    shell: true
});

let hasError = false;
let manorServiceLogs = [];

// 监听标准输出
child.stdout.on('data', (data) => {
    const output = data.toString();
    console.log(output);
    
    // 检查 ManorService 相关日志
    if (output.includes('ManorService')) {
        manorServiceLogs.push(output.trim());
    }
    
    // 检查应用启动完成
    if (output.includes('Application is running on')) {
        setTimeout(() => {
            console.log('\n✅ 应用启动完成！');
            console.log('\n📊 ManorService 执行统计:');
            console.log(`- 检测到 ${manorServiceLogs.length} 条 ManorService 相关日志`);
            manorServiceLogs.forEach((msg, index) => {
                console.log(`  ${index + 1}. ${msg}`);
            });
            
            // 检查关键日志
            const hasConstructor = manorServiceLogs.some(log => log.includes('这里执行了吗1'));
            const hasOnModuleInit = manorServiceLogs.some(log => log.includes('ManorService onModuleInit'));
            const hasCommonService = manorServiceLogs.some(log => log.includes('成功获取 CommonService'));
            const hasCompleted = manorServiceLogs.some(log => log.includes('onModuleInit 执行完成'));
            
            console.log('\n🔍 关键步骤检查:');
            console.log(`  ✅ 构造函数执行: ${hasConstructor ? '是' : '否'}`);
            console.log(`  ✅ onModuleInit 开始: ${hasOnModuleInit ? '是' : '否'}`);
            console.log(`  ✅ CommonService 获取: ${hasCommonService ? '是' : '否'}`);
            console.log(`  ✅ onModuleInit 完成: ${hasCompleted ? '是' : '否'}`);
            
            if (hasConstructor && hasOnModuleInit && hasCommonService && hasCompleted) {
                console.log('\n🎉 ManorService 的 onModuleInit 已正常执行！循环依赖问题已解决！');
            } else {
                console.log('\n⚠️  ManorService 的 onModuleInit 可能未完全执行，请检查日志');
            }
            
            // 关闭应用
            child.kill('SIGTERM');
            process.exit(0);
        }, 3000);
    }
});

// 监听标准错误
child.stderr.on('data', (data) => {
    const error = data.toString();
    console.error('❌ 错误:', error);
    
    // 检查循环依赖错误
    if (error.includes('Circular dependency') || error.includes('circular dependency')) {
        hasError = true;
        console.error('🔄 检测到循环依赖错误！');
    }
});

// 监听进程退出
child.on('close', (code) => {
    if (hasError) {
        console.log('\n❌ 应用启动失败，存在循环依赖问题');
        process.exit(1);
    } else if (code !== 0) {
        console.log(`\n⚠️  应用退出，退出码: ${code}`);
        process.exit(code);
    }
});

// 设置超时
setTimeout(() => {
    console.log('\n⏰ 测试超时，强制退出');
    child.kill('SIGTERM');
    process.exit(1);
}, 30000); // 30秒超时