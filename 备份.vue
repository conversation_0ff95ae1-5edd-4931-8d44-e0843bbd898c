<template>
    <h1 style="text-align: center;">帝王练兵数据模拟器（QQ群：278353668）</h1>
    <div class="container">
        <n-card title="角色">
            <n-form label-placement="left" :label-width="80">
                <n-form-item label="等级:">
                    <n-input-number v-model:value="role.lvl" placeholder="请输入等级" :min="1" :max="308"
                        :update-value-on-input="false" clearable />
                </n-form-item>
                <n-form-item label="魄力:">
                    <n-input-number v-model:value="role.poli" placeholder="请输入魄力" :min="0" :max="role.lvl + 15" clearable
                        :update-value-on-input="false" />
                </n-form-item>
                <n-form-item label="官职:">
                    <n-select v-model:value="role.guanzhi" :options="guanzhiOptions" />
                </n-form-item>
                <n-form-item label="人口:">
                    <n-input disabled v-model:value="role.renkou" />
                </n-form-item>
                <n-divider dashed>
                    攻击/防御/血量相关
                </n-divider>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="飞天神马:">
                            <n-input-number v-model:value="role.飞天神马" placeholder="请输入个数" :min="0" :max="30"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='飞天神马'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="飞天神猴:">
                            <n-input-number v-model:value="role.飞天神猴" placeholder="请输入个数" :min="0" :max="30"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='飞天神猴'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="飞天金鸡:">
                            <n-input-number v-model:value="role.飞天金鸡" placeholder="请输入个数" :min="0" :max="30"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='飞天金鸡'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="神犬食日:">
                            <n-input-number v-model:value="role.神犬食日" placeholder="请输入个数" :min="0" :max="30"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='神犬食日'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="飞天金猪:">
                            <n-input-number v-model:value="role.飞天金猪" placeholder="请输入个数" :min="0" :max="30"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='飞天金猪'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="飞天锦鼠:">
                            <n-input-number v-model:value="role.飞天锦鼠" placeholder="请输入个数" :min="0" :max="30"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='飞天锦鼠'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="飞天神牛:">
                            <n-input-number v-model:value="role.飞天神牛" placeholder="请输入个数" :min="0" :max="30"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='飞天神牛'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="飞天寅虎:">
                            <n-input-number v-model:value="role.飞天寅虎" placeholder="请输入个数" :min="0" :max="30"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='飞天寅虎'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="飞天玉兔:">
                            <n-input-number v-model:value="role.飞天玉兔" placeholder="请输入个数" :min="0" :max="30"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='飞天玉兔'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="真爱纪念:">
                            <n-input-number v-model:value="role.真爱纪念1111" placeholder="请输入个数" :min="0" :max="12"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='真爱纪念'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="武神勋章:">
                            <n-input-number v-model:value="role.武神勋章" placeholder="请输入个数" :min="0" :max="2"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='武神勋章'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="转生勋章:">
                            <n-input-number v-model:value="role.转生勋章" placeholder="请输入个数" :min="0" :max="2"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='转生勋章'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="转生状态:">
                            <n-switch v-model:value="role.转生状态" />
                                <n-tag size="small" @click="showModal=true;jieshaoNow='转生状态'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6" v-if="role.转生状态">
                        <n-form-item label="储备丹:">
                            <n-input-number v-model:value="role.储备丹" placeholder="请输入个数" :min="0" :max="10"
                                :update-value-on-input="false" clearable />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <!-- 飞天神马[19].
                    飞天神猴[24].
                    飞天金鸡[18].
                    神犬食日[15].
                    飞天金猪[15].
                    飞天锦鼠[15].
                    飞天神牛[14].
                    飞天寅虎[14].
                    飞天玉兔[19].
                    [真爱纪念1111]
                    [大力神杯2018]
                    【武神勋章】
                    【转生勋章】

                    微信勋章
                    回馈VIP
                    义气VIP
                    超级消费VIP
                    神猴VIP
                    周年勋章
                    飞天法术
                    至尊钻戒
                    寅虎至尊
                    个人称号
                    大力神杯2018
                    法术勋章
                    飞天勋章
                    坐骑勋章
                    永久积分VIP
                    OK勋章
                    人口勋章
                    松鼠勋章
                    泡泡勋章
                -->
                <n-divider dashed>
                    其他
                </n-divider>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="微信勋章:">
                            <n-input-number v-model:value="role.微信勋章" placeholder="请输入个数" :min="0" :max="12"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='微信勋章'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="回馈VIP:">
                            <n-input-number v-model:value="role.回馈VIP" placeholder="请输入个数" :min="0" :max="10"
                                :update-value-on-input="false" clearable />
                            <n-tag size="large" @click="showModal=true;jieshaoNow='回馈VIP'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="义气VIP:">
                            <n-input-number v-model:value="role.义气VIP" placeholder="请输入个数" :min="0" :max="12"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='义气VIP'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="超级消费VIP:">
                            <n-input-number v-model:value="role.超级消费VIP" placeholder="请输入个数" :min="0" :max="10"
                                :update-value-on-input="false" clearable />
                            <n-tag size="large" @click="showModal=true;jieshaoNow='超级消费VIP'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="神猴VIP:">
                            <n-input-number v-model:value="role.神猴VIP" placeholder="请输入个数" :min="0" :max="12"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='神猴VIP'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="周年勋章:">
                            <n-input-number v-model:value="role.周年勋章" placeholder="请输入个数" :min="0" :max="10"
                                :update-value-on-input="false" clearable />
                            <n-tag size="large" @click="showModal=true;jieshaoNow='周年勋章'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="飞天法术:">
                            <n-input-number v-model:value="role.飞天法术" placeholder="请输入个数" :min="0" :max="12"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='飞天法术'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="至尊钻戒:">
                            <n-input-number v-model:value="role.至尊钻戒" placeholder="请输入个数" :min="0" :max="10"
                                :update-value-on-input="false" clearable />
                            <n-tag size="large" @click="showModal=true;jieshaoNow='至尊钻戒'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="寅虎至尊:">
                            <n-input-number v-model:value="role.寅虎至尊" placeholder="请输入个数" :min="0" :max="12"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='寅虎至尊'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="大力神杯:">
                            <n-input-number v-model:value="role.大力神杯" placeholder="请输入个数" :min="0" :max="10"
                                :update-value-on-input="false" clearable />
                            <n-tag size="large" @click="showModal=true;jieshaoNow='大力神杯'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="法术勋章:">
                            <n-input-number v-model:value="role.法术勋章" placeholder="请输入个数" :min="0" :max="12"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='法术勋章'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="飞天勋章:">
                            <n-input-number v-model:value="role.飞天勋章" placeholder="请输入个数" :min="0" :max="10"
                                :update-value-on-input="false" clearable />
                            <n-tag size="large" @click="showModal=true;jieshaoNow='飞天勋章'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="坐骑勋章:">
                            <n-input-number v-model:value="role.坐骑勋章" placeholder="请输入个数" :min="0" :max="12"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='坐骑勋章'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="永久积分VIP:">
                            <n-input-number v-model:value="role.永久积分VIP" placeholder="请输入个数" :min="0" :max="10"
                                :update-value-on-input="false" clearable />
                            <n-tag size="large" @click="showModal=true;jieshaoNow='永久积分VIP'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="OK勋章:">
                            <n-input-number v-model:value="role.OK勋章" placeholder="请输入个数" :min="0" :max="12"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='OK勋章'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="人口勋章:">
                            <n-input-number v-model:value="role.人口勋章" placeholder="请输入个数" :min="0" :max="10"
                                :update-value-on-input="false" clearable />
                            <n-tag size="large" @click="showModal=true;jieshaoNow='人口勋章'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="松鼠勋章:">
                            <n-input-number v-model:value="role.松鼠勋章" placeholder="请输入个数" :min="0" :max="2"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='松鼠勋章'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="泡泡勋章:">
                            <n-input-number v-model:value="role.泡泡勋章" placeholder="请输入个数" :min="0" :max="3"
                                :update-value-on-input="false" clearable />
                            <n-tag size="large" @click="showModal=true;jieshaoNow='泡泡勋章'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="个人称号:">
                            <n-input-number v-model:value="role.个人称号" placeholder="请输入个数" :min="0" :max="1"
                                :update-value-on-input="false" clearable />
                                <n-tag size="large" @click="showModal=true;jieshaoNow='个人称号'">?</n-tag>
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                    </n-gi>
                </n-grid>
                <n-divider dashed>
                    庄园科技
                </n-divider>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="武将攻击术:" label-width="auto">
                            <n-input-number v-model:value="role.武将攻击术" placeholder="请输入等级" :min="0" :max="30"
                                :update-value-on-input="false" clearable />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="武将防御术:" label-width="auto">
                            <n-input-number v-model:value="role.武将防御术" placeholder="请输入等级" :min="0" :max="30"
                                :update-value-on-input="false" clearable />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="士兵攻击术:" label-width="auto">
                            <n-input-number v-model:value="role.士兵攻击术" placeholder="请输入等级" :min="0" :max="30"
                                :update-value-on-input="false" clearable />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="士兵防御术:" label-width="auto">
                            <n-input-number v-model:value="role.士兵防御术" placeholder="请输入等级" :min="0" :max="30"
                                :update-value-on-input="false" clearable />
                        </n-form-item>
                    </n-gi>
                </n-grid>
        
            </n-form>
        </n-card>
        <n-card title="武将">
            <n-form label-placement="left" :label-width="80">
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="等级:">
                            <n-input-number v-model:value="general.lvl" placeholder="请输入等级" :min="1" :max="role.lvl"
                                clearable :update-value-on-input="false" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="魄力:">
                            <n-input-number v-model:value="general.poli" placeholder="请输入魄力" :min="0"
                                :max="general.lvl + 15" clearable :update-value-on-input="false" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="兵器:">
                            <n-select v-model:value="general.兵器"
                                :options="zhuangbei[0].option.filter(e => general.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="帽子:">
                            <n-select v-model:value="general.帽子"
                                :options="zhuangbei[1].option.filter(e => general.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="肩甲:">
                            <n-select v-model:value="general.肩甲"
                                :options="zhuangbei[2].option.filter(e => general.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="衣服:">
                            <n-select v-model:value="general.衣服"
                                :options="zhuangbei[3].option.filter(e => general.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="披风:">
                            <n-select v-model:value="general.披风"
                                :options="zhuangbei[4].option.filter(e => general.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="项链:">
                            <n-select v-model:value="general.项链"
                                :options="zhuangbei[5].option.filter(e => general.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="手套:">
                            <n-select v-model:value="general.手套"
                                :options="zhuangbei[6].option.filter(e => general.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="戒指:">
                            <n-select v-model:value="general.戒指"
                                :options="zhuangbei[7].option.filter(e => general.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="特殊戒指:">
                            <n-select v-model:value="general.特殊戒指"
                                :options="zhuangbei[8].option.filter(e => general.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="裤子:">
                            <n-select v-model:value="general.裤子"
                                :options="zhuangbei[9].option.filter(e => general.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="鞋子:">
                            <n-select v-model:value="general.鞋子"
                                :options="zhuangbei[10].option.filter(e => general.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="左手套:">
                            <n-select v-model:value="general.左手套"
                                :options="zhuangbei[11].option.filter(e => general.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="PK盾:">
                            <n-select v-model:value="general.PK盾"
                                :options="zhuangbei[12].option.filter(e => general.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="选择宝石:">
                            <n-select v-model:value="general.宝石" :options="baoshi.filter(e => general.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-form-item label="低级武神:">
                    <n-select v-model:value="general.低级武神种类" :options="wushenzhonglei" />
                    <n-input-number v-model:value="general.低级武神资质" placeholder="资质" :min="0" :max="13"
                                clearable :update-value-on-input="false" />
                    <n-input-number v-model:value="general.低级武神等级" placeholder="等级" :min="0" :max="role.武神勋章*10 + 100"
                                clearable :update-value-on-input="false" />
                </n-form-item>
                <n-form-item label="高级武神:">
                    <n-select v-model:value="general.高级武神种类" :options="wushenzhonglei" />
                    <n-input-number v-model:value="general.高级武神资质" placeholder="资质" :min="0" :max="13"
                                clearable :update-value-on-input="false" />
                    <n-input-number v-model:value="general.高级武神等级" placeholder="等级" :min="0" :max="role.武神勋章*10 + 100"
                                clearable :update-value-on-input="false" />
                </n-form-item>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="宠魂:">
                            <n-input-number v-model:value="general.宠魂" placeholder="请输入宠魂等级" :min="0" :max="65"
                                clearable :update-value-on-input="false" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="义气:">
                            <n-input-number v-model:value="general.义气" placeholder="请输入义气等级" :min="0" :max="general.lvl + 1"
                                clearable :update-value-on-input="false" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-form-item label="经脉:">
                    <n-checkbox-group v-model:value="general.经脉">
                        <n-space item-style="display: flex;">
                            <!-- 冲脉、带脉、阴维脉、阳维脉、阴跷脉、阳跷脉、任脉、督脉 -->
                            <n-checkbox value="冲脉" label="冲脉" />
                            <n-checkbox :disabled="!general.经脉.includes('冲脉')" value="带脉" label="带脉" />
                            <n-checkbox :disabled="!general.经脉.includes('带脉')" value="阴维脉" label="阴维脉" />
                            <n-checkbox :disabled="!general.经脉.includes('阴维脉')" value="阳维脉" label="阳维脉" />
                            <n-checkbox :disabled="!general.经脉.includes('阳维脉')" value="阴跷脉" label="阴跷脉" />
                            <n-checkbox :disabled="!general.经脉.includes('阴跷脉')" value="阳跷脉" label="阳跷脉" />
                            <n-checkbox :disabled="!general.经脉.includes('阳跷脉')" value="任脉" label="任脉" />
                            <n-checkbox :disabled="!general.经脉.includes('任脉')" value="督脉" label="督脉" />
                        </n-space>
                    </n-checkbox-group>
                </n-form-item>
                <n-form-item label="魂魄:">
                    <n-checkbox-group v-model:value="general.魂魄">
                        <n-space item-style="display: flex;">
                            <!-- 冲脉、带脉、阴维脉、阳维脉、阴跷脉、阳跷脉、任脉、督脉 -->
                            <n-checkbox value="金" label="金" />
                            <n-checkbox :disabled="!general.魂魄.includes('金')" value="木" label="木" />
                            <n-checkbox :disabled="!general.魂魄.includes('木')" value="水" label="水" />
                            <n-checkbox :disabled="!general.魂魄.includes('水')" value="火" label="火" />
                            <n-checkbox :disabled="!general.魂魄.includes('火')" value="土" label="土" />
                            <n-checkbox :disabled="!general.魂魄.includes('土')" value="高级魂珠*1" label="高级魂珠*1" />
                            <n-checkbox :disabled="!general.魂魄.includes('高级魂珠*1')" value="高级魂珠*2" label="高级魂珠*2" />
                            <n-checkbox :disabled="!general.魂魄.includes('高级魂珠*2')" value="高级魂珠*3" label="高级魂珠*3" />
                            <n-checkbox :disabled="!general.魂魄.includes('高级魂珠*3')" value="高级魂珠*4" label="高级魂珠*4" />
                            <n-checkbox :disabled="!general.魂魄.includes('高级魂珠*4')" value="高级魂珠*5" label="高级魂珠*5" />
                        </n-space>
                    </n-checkbox-group>
                </n-form-item>
                <n-form-item label="操作:">
                    <n-checkbox-group v-model:value="general.操作">
                        <n-space item-style="display: flex;">
                            <n-checkbox value="狂暴一*10" label="狂暴一*10" />
                            <n-checkbox :disabled="!general.操作.includes('狂暴一*10')" value="狂暴二*5" label="狂暴二*5" />
                            <n-checkbox :disabled="!general.操作.includes('狂暴二*5')" value="狂暴三*5" label="狂暴三*5" />
                            <n-checkbox :disabled="!general.操作.includes('狂暴三*5')" value="龙魄狂暴*5" label="龙魄狂暴*5" />
                            <n-checkbox value="低精" label="低精" />
                            <n-checkbox :disabled="!general.操作.includes('低精')" value="高精" label="高精" />
                            <n-checkbox value="开满孔" label="开满孔" />
                            <n-checkbox value="狂暴奇书*10" label="狂暴奇书*10" />
                            <n-checkbox :disabled="!general.操作.includes('狂暴奇书*10')" value="极度狂暴奇书*10" label="极度狂暴奇书*10" />
                            <n-checkbox value="统帅" label="统帅" />
                            <n-checkbox value="战神" label="战神" />
                            <n-checkbox value="高级战神" label="高级战神" />
                            <n-checkbox value="跨服战神" label="跨服战神" />
                        </n-space>
                    </n-checkbox-group>
                </n-form-item>
                <n-form-item label="祝福-待更新:">
                    <n-checkbox-group v-model:value="general.祝福">
                        <n-space item-style="display: flex;">
                            <n-checkbox value="奥运战将" label="奥运战将" />
                            <n-checkbox value="转生勋章" label="转生勋章" />
                            <n-checkbox value="天龙王" label="天龙王" />
                            <n-checkbox value="王者勋章" label="王者勋章" />
                            <n-checkbox value="元旦祝福" label="元旦祝福" />
                            <n-checkbox value="元旦祝福(高级)" label="元旦祝福(高级)" />
                            <n-checkbox value="元旦祝福(顶级)" label="元旦祝福(顶级)" />
                            <n-checkbox value="仙兵元旦祝福" label="仙兵元旦祝福" />
                            <n-checkbox value="河神祝福2023" label="河神祝福2023" />
                            <n-checkbox value="七夕红" label="七夕红" />
                            <n-checkbox value="七夕蓝" label="七夕蓝" />
                            <n-checkbox value="白色守护" label="白色守护" />
                            <n-checkbox value="元神冰" label="元神冰" />
                        </n-space>
                    </n-checkbox-group>
                </n-form-item>
                <n-form-item label="人口:">
                    <n-input-number  disabled :value="result.renkou" />
                </n-form-item>
                <n-form-item label="攻击:">
                    <n-input-number  disabled :value="result.initVal" />
                </n-form-item>
                <n-form-item label="防御:">
                    <n-input-number  disabled :value="result.initFY" />
                </n-form-item>
                <!-- <n-form-item label="血量:">
                    <n-input disabled v-model:value="general.xueliang"/>
                </n-form-item> -->
            </n-form>
        </n-card>
        <n-card title="兵种">
            <n-form label-placement="left" :label-width="80">
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="兵种:">
                            <n-select v-model:value="soldiers.兵种" :options="soldiersOptions" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="等级:">
                            <n-input-number v-model:value="soldiers.lvl" placeholder="请输入等级" :min="1" :max="general.lvl"
                                clearable :update-value-on-input="false" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="兵器:">
                            <n-select v-model:value="soldiers.兵器"
                                :options="zhuangbeiSoldiers[0].option.filter(e => soldiers.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="帽子:">
                            <n-select v-model:value="soldiers.帽子"
                                :options="zhuangbeiSoldiers[1].option.filter(e => soldiers.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="肩甲:">
                            <n-select v-model:value="soldiers.肩甲"
                                :options="zhuangbeiSoldiers[2].option.filter(e => soldiers.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="衣服:">
                            <n-select v-model:value="soldiers.衣服"
                                :options="zhuangbeiSoldiers[3].option.filter(e => soldiers.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="披风:">
                            <n-select v-model:value="soldiers.披风"
                                :options="zhuangbeiSoldiers[4].option.filter(e => soldiers.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="项链:">
                            <n-select v-model:value="soldiers.项链"
                                :options="zhuangbeiSoldiers[5].option.filter(e => soldiers.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="手套:">
                            <n-select v-model:value="soldiers.手套"
                                :options="zhuangbeiSoldiers[6].option.filter(e => soldiers.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="戒指:">
                            <n-select v-model:value="soldiers.戒指"
                                :options="zhuangbeiSoldiers[7].option.filter(e => soldiers.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="特殊戒指:">
                            <n-select v-model:value="soldiers.特殊戒指"
                                :options="zhuangbeiSoldiers[8].option.filter(e => soldiers.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="裤子:">
                            <n-select v-model:value="soldiers.裤子"
                                :options="zhuangbeiSoldiers[9].option.filter(e => soldiers.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="鞋子:">
                            <n-select v-model:value="soldiers.鞋子"
                                :options="zhuangbeiSoldiers[10].option.filter(e => soldiers.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="左手套:">
                            <n-select v-model:value="soldiers.左手套"
                                :options="zhuangbeiSoldiers[11].option.filter(e => soldiers.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="PK盾:">
                            <n-select v-model:value="soldiers.PK盾"
                                :options="zhuangbeiSoldiers[12].option.filter(e => soldiers.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="选择宝石:">
                            <n-select v-model:value="soldiers.宝石" :options="baoshi.filter(e => general.lvl >= e.lvl)" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-form-item label="低级武神:">
                    <n-select v-model:value="soldiers.低级武神种类" :options="wushenzhonglei" />
                    <n-input-number v-model:value="soldiers.低级武神资质" placeholder="资质" :min="0" :max="13"
                                clearable :update-value-on-input="false" />
                    <n-input-number v-model:value="soldiers.低级武神等级" placeholder="等级" :min="0" :max="role.武神勋章*10 + 100"
                                clearable :update-value-on-input="false" />
                </n-form-item>
                <n-form-item label="高级武神:">
                    <n-select v-model:value="soldiers.高级武神种类" :options="wushenzhonglei" />
                    <n-input-number v-model:value="soldiers.高级武神资质" placeholder="资质" :min="0" :max="13"
                                clearable :update-value-on-input="false" />
                    <n-input-number v-model:value="soldiers.高级武神等级" placeholder="等级" :min="0" :max="role.武神勋章*10 + 100"
                                clearable :update-value-on-input="false" />
                </n-form-item>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="宠魂:">
                            <n-input-number v-model:value="soldiers.宠魂" placeholder="请输入宠魂等级" :min="0" :max="65"
                                clearable :update-value-on-input="false" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="义气:">
                            <n-input-number v-model:value="soldiers.义气" placeholder="请输入义气等级" :min="0" :max="soldiers.lvl + 1"
                                clearable :update-value-on-input="false" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-grid :cols="12">
                    <n-gi span="6">
                        <n-form-item label="坐骑攻击:">
                            <n-input-number v-model:value="soldiers.mountgj" placeholder="请输入坐骑攻击" :min="0"
                                clearable :update-value-on-input="false" />
                        </n-form-item>
                    </n-gi>
                    <n-gi span="6">
                        <n-form-item label="坐骑防御:">
                            <n-input-number v-model:value="soldiers.mountfy" placeholder="请输入坐骑防御" :min="0"
                                clearable :update-value-on-input="false" />
                        </n-form-item>
                    </n-gi>
                </n-grid>
                <n-form-item label="经脉:">
                    <n-checkbox-group v-model:value="soldiers.经脉">
                        <n-space item-style="display: flex;">
                            <!-- 冲脉、带脉、阴维脉、阳维脉、阴跷脉、阳跷脉、任脉、督脉 -->
                            <n-checkbox value="冲脉" label="冲脉" />
                            <n-checkbox :disabled="!soldiers.经脉.includes('冲脉')" value="带脉" label="带脉" />
                            <n-checkbox :disabled="!soldiers.经脉.includes('带脉')" value="阴维脉" label="阴维脉" />
                            <n-checkbox :disabled="!soldiers.经脉.includes('阴维脉')" value="阳维脉" label="阳维脉" />
                            <n-checkbox :disabled="!soldiers.经脉.includes('阳维脉')" value="阴跷脉" label="阴跷脉" />
                            <n-checkbox :disabled="!soldiers.经脉.includes('阴跷脉')" value="阳跷脉" label="阳跷脉" />
                            <n-checkbox :disabled="!soldiers.经脉.includes('阳跷脉')" value="任脉" label="任脉" />
                            <n-checkbox :disabled="!soldiers.经脉.includes('任脉')" value="督脉" label="督脉" />
                        </n-space>
                    </n-checkbox-group>
                </n-form-item>
                <n-form-item label="魂魄:">
                    <n-checkbox-group v-model:value="soldiers.魂魄">
                        <n-space item-style="display: flex;">
                            <!-- 冲脉、带脉、阴维脉、阳维脉、阴跷脉、阳跷脉、任脉、督脉 -->
                            <n-checkbox value="金" label="金" />
                            <n-checkbox :disabled="!soldiers.魂魄.includes('金')" value="木" label="木" />
                            <n-checkbox :disabled="!soldiers.魂魄.includes('木')" value="水" label="水" />
                            <n-checkbox :disabled="!soldiers.魂魄.includes('水')" value="火" label="火" />
                            <n-checkbox :disabled="!soldiers.魂魄.includes('火')" value="土" label="土" />
                            <n-checkbox :disabled="!soldiers.魂魄.includes('土')" value="高级魂珠*1" label="高级魂珠*1" />
                            <n-checkbox :disabled="!soldiers.魂魄.includes('高级魂珠*1')" value="高级魂珠*2" label="高级魂珠*2" />
                            <n-checkbox :disabled="!soldiers.魂魄.includes('高级魂珠*2')" value="高级魂珠*3" label="高级魂珠*3" />
                            <n-checkbox :disabled="!soldiers.魂魄.includes('高级魂珠*3')" value="高级魂珠*4" label="高级魂珠*4" />
                            <n-checkbox :disabled="!soldiers.魂魄.includes('高级魂珠*4')" value="高级魂珠*5" label="高级魂珠*5" />
                        </n-space>
                    </n-checkbox-group>
                </n-form-item>
                <n-form-item label="操作:">
                    <n-checkbox-group v-model:value="soldiers.操作">
                        <n-space item-style="display: flex;">
                            <n-checkbox value="狂暴一*10" label="狂暴一*10" />
                            <n-checkbox :disabled="!soldiers.操作.includes('狂暴一*10')" value="狂暴二*5" label="狂暴二*5" />
                            <n-checkbox :disabled="!soldiers.操作.includes('狂暴二*5')" value="狂暴三*5" label="狂暴三*5" />
                            <n-checkbox :disabled="!soldiers.操作.includes('狂暴三*5')" value="龙魄狂暴*5" label="龙魄狂暴*5" />
                            <n-checkbox value="低精" label="低精" />
                            <n-checkbox :disabled="!soldiers.操作.includes('低精')" value="高精" label="高精" />
                            <n-checkbox value="开满孔" label="开满孔" />
                            <n-checkbox value="狂暴奇书*10" label="狂暴奇书*10" />
                            <n-checkbox :disabled="!soldiers.操作.includes('狂暴奇书*10')" value="极度狂暴奇书*10" label="极度狂暴奇书*10" />
                            <n-checkbox value="战神" label="战神" :disabled="soldiers.lvl<150"/>
                            <n-checkbox value="高级战神" label="高级战神" :disabled="soldiers.lvl<150"/>
                            <n-checkbox value="跨服战神" label="跨服战神*5"/>
                        </n-space>
                    </n-checkbox-group>
                </n-form-item>
                <n-form-item label="祝福-待更新:">
                    <n-checkbox-group v-model:value="soldiers.祝福">
                        <n-space item-style="display: flex;">
                            <n-checkbox value="奥运战将" label="奥运战将" />
                            <n-checkbox value="转生勋章" label="转生勋章" />
                            <n-checkbox value="天龙王" label="天龙王" />
                            <n-checkbox value="王者勋章" label="王者勋章" />
                            <n-checkbox value="元旦祝福" label="元旦祝福" />
                            <n-checkbox value="元旦祝福(高级)" label="元旦祝福(高级)" />
                            <n-checkbox value="元旦祝福(顶级)" label="元旦祝福(顶级)" />
                            <n-checkbox value="仙兵元旦祝福" label="仙兵元旦祝福" />
                            <n-checkbox value="河神祝福2023" label="河神祝福2023" />
                            <n-checkbox value="七夕红" label="七夕红" />
                            <n-checkbox value="七夕蓝" label="七夕蓝" />
                            <n-checkbox value="白色守护" label="白色守护" />
                            <n-checkbox value="元神冰" label="元神冰" />
                        </n-space>
                    </n-checkbox-group>
                </n-form-item>
                <n-form-item label="攻击:">
                    <n-input-number disabled v-model:value="resultSoldiers.gongji" />
                </n-form-item>
                <n-form-item label="防御:">
                    <n-input-number disabled v-model:value="resultSoldiers.fangyu" />
                </n-form-item>
                <n-form-item label="血量:">
                    <n-input-number disabled v-model:value="resultSoldiers.xueliang"/>
                </n-form-item>
            </n-form>
        </n-card>
    </div>
    <n-modal v-model:show="showModal">
        <n-card style="width: 600px" :title="jieshaoNow+'属性'" :bordered="false" size="huge" role="dialog" aria-modal="true">
            <div v-html="jieshao[jieshaoNow] "></div>
        </n-card>
    </n-modal>
</template>

<script setup lang="ts">
import { computed, reactive, watch,ref } from 'vue';
import { guanzhiOptions,soldiersOptions, zhuangbei, baoshi,wushenzhonglei,jieshao,zhuangbeiSoldiers } from "../../utils";
const message = window.$message
//兵种
const soldiers = reactive<GeneralType>({
    lvl: 1,
    mountgj:0,
    mountfy:0,
    poli: 0,
    兵种:'铁甲',
    renkou: '100',
    兵器: '',
    帽子: '',
    肩甲: '',
    衣服: '',
    披风: '',
    项链: '',
    手套: '',
    戒指: '',
    特殊戒指: '',
    裤子: '',
    鞋子: '',
    左手套: '',
    PK盾: '',
    操作: [],
    宝石: '',
    经脉: [],
    魂魄: [],
    宠魂: 0,
    祝福: [],
    低级武神种类: '',
    低级武神资质: 0,
    低级武神等级: 0,
    高级武神种类: '',
    高级武神资质: 0,
    高级武神等级: 0,
    义气: 0,
})
const resultSoldiers=computed(()=>{
    let gongji = 0
    let fangyu=0
    let xueliang=0
    //获取当前兵种参数
    let soldiersVal=soldiersOptions.filter(item=>item.label==soldiers.兵种)[0].option
    //武将等级、魄力、士兵等级+1 攻击 加兵种的成长 
    gongji += (general.lvl+general.poli+1+soldiers.lvl-1) * soldiersVal.gongji
    //士兵等级+1
    fangyu+=soldiers.lvl*soldiersVal.fangyu
    xueliang+=(soldiers.lvl-1)*soldiersVal.xueliangadd+soldiersVal.xueliang
    // 装备加成
    //装备
    let tem={
        gongji:0,
        fangyu:0,
        xueliang:0
    }
    let initVal1 = gongji
    let gongjiliinit=soldiers.lvl//初始攻击力  等级*10
    if(soldiers.兵器){
        tem=gongjiliSoldiers(soldiers.兵器, 0)
        gongjiliinit+=tem.gongji
    }
    if(soldiers.帽子){
        tem=gongjiliSoldiers(soldiers.帽子, 1)
        gongjiliinit+=tem.gongji
    }
    if(soldiers.肩甲){
        tem=gongjiliSoldiers(soldiers.肩甲, 2)
        gongjiliinit+=tem.gongji
    }
    if(soldiers.衣服){
        tem=gongjiliSoldiers(soldiers.衣服, 3)
        gongjiliinit+=tem.gongji
    }
    if(soldiers.披风){
        tem=gongjiliSoldiers(soldiers.披风, 4)
        gongjiliinit+=tem.gongji
    }
    if(soldiers.项链){
        tem=gongjiliSoldiers(soldiers.项链, 5)
        gongjiliinit+=tem.gongji
    }
    if(soldiers.手套){
        tem=gongjiliSoldiers(soldiers.手套, 6)
        gongjiliinit+=tem.gongji
    }
    if(soldiers.戒指){
        tem=gongjiliSoldiers(soldiers.戒指, 7)
        gongjiliinit+=tem.gongji
    }
    if(soldiers.特殊戒指){
        tem=gongjiliSoldiers(soldiers.特殊戒指, 8)
        gongjiliinit+=tem.gongji
    }
    if(soldiers.裤子){
        tem=gongjiliSoldiers(soldiers.裤子, 9)
        gongjiliinit+=tem.gongji
    }
    if(soldiers.鞋子){
        tem=gongjiliSoldiers(soldiers.鞋子, 10)
        gongjiliinit+=tem.gongji
    }
    if(soldiers.左手套){
        tem=gongjiliSoldiers(soldiers.左手套, 11)
        gongjiliinit+=tem.gongji
    }
    if(soldiers.PK盾){
        tem=gongjiliSoldiers(soldiers.PK盾, 12)
        gongjiliinit+=tem.gongji
    }
    if (soldiers.操作.includes('狂暴一*10')) {
        gongjiliinit = gongjiliinit * 1.5
    }
    if (soldiers.操作.includes('狂暴二*5')) {
        gongjiliinit = gongjiliinit * 1.25
    }
    if (soldiers.操作.includes('狂暴三*5')) {
        gongjiliinit = gongjiliinit * 1.25
    }
    if (soldiers.操作.includes('龙魄狂暴*5')) {
        gongjiliinit = gongjiliinit * 1.25
    }
    // 攻击力计算
    gongji+=(gongjiliinit-soldiers.lvl)* soldiersVal.gongji

    //吃书 攻击力加成
    let initVal2 = gongji
    let initFY2 = fangyu
    //角色称号 百分比加成
    if (soldiers.操作.includes('狂暴奇书*10')) {
        gongji=Math.floor(gongji)
        gongji += initVal2 * 0.3

    }
    if (soldiers.操作.includes('极度狂暴奇书*10')) {
        gongji=Math.floor(gongji)
        gongji += initVal2 * 0.3
    }
    // 庄园科技百分比加成
    gongji+=initVal2*0.02*role.士兵攻击术
    fangyu+=initFY2*0.02*role.士兵防御术
    // 魂珠
    gongji += hunpo(initVal2).gongji
    //开脉
    gongji += kaimai(initVal2).gongji
    //宠魂
    const obj2=chonghunjs(initVal2,initFY2)
    gongji+=obj2.gongji
    gongji+=obj2.fangyu
    //义气
    gongji += initVal2 * general.义气 * 0.005
    //战神
    if (soldiers.操作.includes('战神')) {
        gongji += initVal2 * 0.05
    }
    //高级战神
    if (soldiers.操作.includes('高级战神')) {
        gongji += initVal2 * 0.05
    }
    //跨服战神
    if (soldiers.操作.includes('跨服战神')) {
        gongji += initVal2 * 0.05
        fangyu += initFY2 * 0.05
    }
    //武神
    //角色称号
    const obj=userChenghao(initVal2,initFY2)
    gongji+=obj.gongji
    fangyu+=obj.fangyu
    //武神计算
    const obj1=wushen(initVal2,initFY2)
    gongji+=obj1.gongji
    fangyu+=obj1.fangyu
    //坐骑加成
    gongji=Math.floor(gongji)
    fangyu=Math.floor(fangyu)
    gongji+=gongji*soldiers.mountgj*0.01
    fangyu+=fangyu*soldiers.mountfy*0.01
    console.log(gongji)
    return {
        gongji:Math.floor(gongji),
        fangyu:Math.floor(fangyu),
        xueliang:Math.floor(xueliang)
    }
})

// 兵种结束
//角色
const showModal=ref(false)
const jieshaoNow=ref('')
const role = reactive({
    lvl: 1,
    poli: 0,
    guanzhi: 0,
    renkou: '100',
    武将攻击术: 0,
    武将防御术: 0,
    士兵攻击术: 0,
    士兵防御术: 0,
    飞天神马: 0,
    飞天神猴: 0,
    飞天金鸡: 0,
    飞天金猪: 0,
    飞天锦鼠: 0,
    飞天神牛: 0,
    飞天寅虎: 0,
    飞天玉兔: 0,
    神犬食日: 0,
    真爱纪念1111: 0,
    大力神杯2018: 0,
    武神勋章: 0,
    转生勋章: 0,
    转生状态: false,
    储备丹: 0,
    回馈VIP:0,
    微信勋章:0,
    义气VIP:0,
    超级消费VIP:0,
    神猴VIP:0,
    周年勋章:0,
    飞天法术:0,
    至尊钻戒:0,
    寅虎至尊:0,
    个人称号:0,
    大力神杯:0,
    法术勋章:0,
    飞天勋章:0,
    坐骑勋章:0,
    永久积分VIP:0,
    OK勋章:0,
    人口勋章:0,
    松鼠勋章:0,
    泡泡勋章:0,
})
type GeneralType={
    操作:string[],
    经脉:string[],
    魂魄:string[],
    祝福:string[],
    [propName: string]:any
}
//武将开始
const general = reactive<GeneralType>({
    lvl: 1,
    poli: 0,
    renkou: '100',
    兵器: '',
    帽子: '',
    肩甲: '',
    衣服: '',
    披风: '',
    项链: '',
    手套: '',
    戒指: '',
    特殊戒指: '',
    裤子: '',
    鞋子: '',
    左手套: '',
    PK盾: '',
    操作: [],
    宝石: '',
    经脉: [],
    魂魄: [],
    宠魂: 0,
    祝福: [],
    低级武神种类: '',
    低级武神资质: 0,
    低级武神等级: 0,
    高级武神种类: '',
    高级武神资质: 0,
    高级武神等级: 0,
    义气: 0,
})

const result=computed(()=>{
    let initVal = 0,initFY=0,renkou=100,xueliang=100;
    //角色等级 每级+10攻击
    initVal += role.lvl * 10
    //角色魄力 每级+10攻击
    initVal += role.poli * 10
    //武将等级 每级+10攻击
    initVal += general.lvl * 10
    initFY += general.lvl * 40
    //武将魄力 每级+10攻击
    initVal += general.poli * 10
    //装备
    initVal += gongjili(general.兵器, 0).gongji * 10
    initVal += gongjili(general.帽子, 1).gongji * 10
    initVal += gongjili(general.肩甲, 2).gongji * 10
    initVal += gongjili(general.衣服, 3).gongji * 10
    initVal += gongjili(general.披风, 4).gongji * 10
    initVal += gongjili(general.项链, 5).gongji * 10
    initVal += gongjili(general.手套, 6).gongji * 10
    initVal += gongjili(general.戒指, 7).gongji * 10
    initVal += gongjili(general.特殊戒指, 8).gongji * 10
    initVal += gongjili(general.裤子, 9).gongji * 10
    initVal += gongjili(general.鞋子, 10).gongji * 10
    initVal += gongjili(general.左手套, 11).gongji * 10
    initVal += gongjili(general.PK盾, 12).gongji * 10
    //吃书 攻击力加成
    let initVal1 = initVal
    if (general.操作.includes('狂暴一*10')) {
        initVal += initVal1 * 0.5
    }
    if (general.操作.includes('狂暴二*5')) {
        initVal += initVal1 * 0.25
    }
    if (general.操作.includes('狂暴三*5')) {
        initVal += initVal1 * 0.25
    }
    if (general.操作.includes('龙魄狂暴*5')) {
        initVal += initVal1 * 0.25
    }
    let initVal2 = initVal
    let initFY2 = initFY
    //角色称号 百分比加成
    if (general.操作.includes('狂暴奇书*10')) {
        initVal += initVal2 * 0.3

    }
    if (general.操作.includes('极度狂暴奇书*10')) {
        initVal += initVal2 * 0.3
    }
    // 魂珠
    initVal += hunpo(initVal2).gongji
    //开脉
    initVal += kaimai(initVal2).gongji
    //宠魂
    const obj2=chonghunjs(initVal2,initFY2)
    initVal+=obj2.gongji
    initFY+=obj2.fangyu
    //义气
    initVal += initVal2 * general.义气 * 0.005
    //统帅
    if (general.操作.includes('统帅')) {
        initVal += initVal2 * 0.4
    }
    //战神
    if (general.操作.includes('战神')) {
        initVal += initVal2 * 0.005
    }
    //高级战神
    if (general.操作.includes('高级战神')) {
        initVal += initVal2 * 0.005
    }
    //跨服战神
    if (general.操作.includes('跨服战神')) {
        initVal += initVal2 * 0.005
    }
    //武神
    // 庄园科技百分比加成
    initVal+=initVal2*0.02*role.武将攻击术
    initFY+=initFY2*0.02*role.武将防御术
    //角色称号
    const obj=userChenghao(initVal2,initFY2)
    initVal+=obj.gongji
    initFY+=obj.fangyu
    //武神计算
    const obj1=wushen(initVal2,initFY2)
    initVal+=obj1.gongji
    initFY+=obj1.fangyu
    return {
        initVal,
        initFY,
        renkou,
        xueliang
    }
})
//wushen
const wushen=(initVal2: number,initFY2:number)=>{
    let gongji = 0;
    let fangyu = 0;
    if(general.低级武神种类=='攻'){
        gongji+=initVal2*general.低级武神资质*general.低级武神等级*0.002
    }
    if(general.低级武神种类=='防'){
        fangyu+=initFY2*general.低级武神资质*general.低级武神等级*0.006
    }
    if(general.高级武神种类=='攻'){
        gongji+=initVal2*general.高级武神资质*general.高级武神等级*0.002
    }
    if(general.高级武神种类=='防'){
        fangyu+=initFY2*general.高级武神资质*general.高级武神等级*0.004
    }
    return {
        gongji,
        fangyu
    }
}
//计算攻击
// const generalGJ = computed(() => {
//     let initVal = 0
//     //角色等级 每级+10攻击
//     initVal += role.lvl * 10
//     //角色魄力 每级+10攻击
//     initVal += role.poli * 10
//     //武将等级 每级+10攻击
//     initVal += general.lvl * 10
//     //武将魄力 每级+10攻击
//     initVal += general.poli * 10
//     //装备
//     initVal += gongjili(general.兵器, 0).gongji * 10
//     initVal += gongjili(general.帽子, 1).gongji * 10
//     initVal += gongjili(general.肩甲, 2).gongji * 10
//     initVal += gongjili(general.衣服, 3).gongji * 10
//     initVal += gongjili(general.披风, 4).gongji * 10
//     initVal += gongjili(general.项链, 5).gongji * 10
//     initVal += gongjili(general.手套, 6).gongji * 10
//     initVal += gongjili(general.戒指, 7).gongji * 10
//     initVal += gongjili(general.特殊戒指, 8).gongji * 10
//     initVal += gongjili(general.裤子, 9).gongji * 10
//     initVal += gongjili(general.鞋子, 10).gongji * 10
//     initVal += gongjili(general.左手套, 11).gongji * 10
//     initVal += gongjili(general.PK盾, 12).gongji * 10
//     //吃书 攻击力加成
//     let initVal1 = initVal
//     if (general.操作.includes('狂暴一*10')) {
//         initVal += initVal1 * 0.5
//     }
//     if (general.操作.includes('狂暴二*5')) {
//         initVal += initVal1 * 0.25
//     }
//     if (general.操作.includes('狂暴三*5')) {
//         initVal += initVal1 * 0.25
//     }
//     if (general.操作.includes('龙魄狂暴*5')) {
//         initVal += initVal1 * 0.25
//     }
//     let initVal2 = initVal
//     //角色称号 百分比加成
//     if (general.操作.includes('狂暴奇书*10')) {
//         initVal += initVal2 * 0.3

//     }
//     if (general.操作.includes('极度狂暴奇书*10')) {
//         initVal += initVal2 * 0.3
//     }
//     // 魂珠
//     initVal += hunpo(initVal2).gongji
//     //开脉
//     initVal += kaimai(initVal2).gongji
//     //宠魂
//     initVal += chonghunjs(initVal2).gongji
//     //义气
//     initVal += initVal2 * general.义气 * 0.005
//     //统帅
//     if (general.操作.includes('统帅')) {
//         initVal += initVal2 * 0.4
//     }
//     //战神
//     if (general.操作.includes('战神')) {
//         initVal += initVal2 * 0.005
//     }
//     //高级战神
//     if (general.操作.includes('高级战神')) {
//         initVal += initVal2 * 0.005
//     }
//     //跨服战神
//     if (general.操作.includes('跨服战神')) {
//         initVal += initVal2 * 0.005
//     }
//     //武神
//     // 庄园科技百分比加成
//     initVal+=initVal2*0.02*role.武将攻击术
//     //角色称号
//     // initVal+=userChenghao(initVal2).gongji
//     return initVal + ''
// })
//角色称号属性计算
const userChenghao=(initVal2: number,initFY2:number)=>{
    let gongji = 0;
    let fangyu = 0;
    //飞天系列
    gongji+=initVal2*role.飞天神马*0.01
    fangyu+=initFY2*role.飞天神马*0.02
    gongji+=initVal2*role.飞天神猴*0.01
    fangyu+=initFY2*role.飞天神猴*0.02
    gongji+=initVal2*role.飞天金鸡*0.01
    fangyu+=initFY2*role.飞天金鸡*0.02
    gongji+=initVal2*role.飞天金猪*0.01
    fangyu+=initFY2*role.飞天金猪*0.02
    gongji+=initVal2*role.飞天锦鼠*0.01
    fangyu+=initFY2*role.飞天锦鼠*0.02
    gongji+=initVal2*role.飞天寅虎*0.01
    fangyu+=initFY2*role.飞天寅虎*0.02
    gongji+=initVal2*role.飞天玉兔*0.01
    fangyu+=initFY2*role.飞天玉兔*0.02
    gongji+=initVal2*role.神犬食日*0.01
    fangyu+=initFY2*role.神犬食日*0.02
    gongji+=initVal2*role.真爱纪念1111*0.01
    fangyu+=initFY2*role.真爱纪念1111*0.02
    gongji+=initVal2*role.飞天神牛*0.01
    fangyu+=initFY2*role.飞天神牛*0.02
    //武神勋章
    if(role.武神勋章>0){
        gongji+=initVal2*0.06
        fangyu+=initFY2*0.12
    }
    if(role.武神勋章>1){
        gongji+=initVal2*0.1
        fangyu+=initFY2*0.2
    }
    //转生勋章
    //转生储备丹
    if(role.转生状态){
        switch (role.转生勋章) {
            case 1:
                gongji+=initVal2*(0.15+0.0015*10+role.储备丹*0.01)
                fangyu+=initFY2*(0.3+0.003*10+role.储备丹*0.01)
                break;
            case 2:
                gongji+=initVal2*(0.15+0.0015*22+role.储备丹*0.01)
                fangyu+=initFY2*(0.3+0.003*20+role.储备丹*0.01)
                break;
            default:
                gongji+=initVal2*(0.15+role.储备丹*0.01)
                fangyu+=initFY2*(0.3+role.储备丹*0.01)
                break;
        }
        
    }
    return{
        gongji,
        fangyu
    }
}
//宠魂加成
const chonghunjs = (initVal2: number, initFangyu = 0) => {
    let gongji = 0;
    let fangyu=0
    let lvl = general.宠魂;
    if (general.宠魂 && lvl >= 30) {
        gongji += initVal2 * lvl * 0.015
    }
    if (general.宠魂 && lvl >= 20) {
        fangyu += initFangyu * lvl * 0.02
    }
    return {
        gongji,
        fangyu
    }
}
// 开脉计算 提升100%
const kaimai = (initVal2: number, initFangyu = 0) => {
    let gongji = 0;
    let fangyu = 0;
    if (general.经脉.includes('督脉')) {
        gongji += initVal2 * 100
        fangyu += initFangyu * 0.3
    }
    return {
        gongji,
        fangyu
    }
}
//魂魄计算 提升 96%
const hunpo = (initVal2: number, initFangyu = 0) => {
    let gongji = 0;
    let fangyu = 0;
    if (general.魂魄.includes('金')) {
        gongji += initVal2 * 0.3
        fangyu += initFangyu * 0.3
    }
    if (general.魂魄.includes('木')) {
        gongji += initVal2 * 0.3
        fangyu += initFangyu * 0.3
    }
    if (general.魂魄.includes('水')) {
        gongji += initVal2 * 0.6
        fangyu += initFangyu * 0.3
    }
    if (general.魂魄.includes('火')) {
        gongji += initVal2 * 1.2
        fangyu += initFangyu * 0.3
    }
    if (general.魂魄.includes('土')) {
        gongji += initVal2 * 2.4
        fangyu += initFangyu * 0.3
    }
    if (general.魂魄.includes('高级魂珠*1')) {
        gongji += initVal2 * 0.3
        fangyu += initFangyu * 0.3
    }
    if (general.魂魄.includes('高级魂珠*2')) {
        gongji += initVal2 * 0.3
        fangyu += initFangyu * 0.3
    }
    if (general.魂魄.includes('高级魂珠*3')) {
        gongji += initVal2 * 0.6
        fangyu += initFangyu * 0.3
    }
    if (general.魂魄.includes('高级魂珠*4')) {
        gongji += initVal2 * 1.2
        fangyu += initFangyu * 0.3
    }
    if (general.魂魄.includes('高级魂珠*5')) {
        gongji += initVal2 * 2.4
        fangyu += initFangyu * 0.3
    }
    return {
        gongji,
        fangyu
    }
}
// 装备计算攻击力  
const gongjili = (title: string, type: number) => {
    if (!title) {
        return {
            gongji: 0,
            fangyu: 0,
            xueliang: 0
        }
    }
    const obj = zhuangbei[type].option.filter(e => e.label == title)[0]
    let gongji = obj.initGj
    let fangyu = obj.initFY
    let xueliang = 0
    if (general.操作.includes('低精')) {
        gongji += obj.diGj
        fangyu += obj.dijingFY
    }
    if (general.操作.includes('高精')) {
        gongji += obj.gaoGj
        fangyu += obj.gaojingGj
    }
    let kong = general.操作.includes('开满孔') ? obj.initK + obj.kaiK : obj.initK
    if (general.宝石) {
        let obj1 = baoshi.filter(e => e.value == general.宝石)[0]
        gongji += kong * obj1.gongji
        fangyu += kong * obj1.fangyu
        xueliang += kong * obj1.xueliang
    }
    console.log(gongji, fangyu, xueliang)
    return {
        gongji,
        fangyu,
        xueliang
    }
}
// 装备计算攻击力  士兵
const gongjiliSoldiers = (title: string, type: number) => {
    if (!title) {
        return {
            gongji: 0,
            fangyu: 0,
            xueliang: 0
        }
    }
    const obj = zhuangbeiSoldiers[type].option.filter(e => e.label == title)[0]
    let gongji = obj.initGj
    let fangyu = obj.initFY
    let xueliang = 0
    if (soldiers.操作.includes('低精')) {
        gongji += obj.diGj
        fangyu += obj.dijingFY
    }
    if (soldiers.操作.includes('高精')) {
        gongji += obj.gaoGj
        fangyu += obj.gaojingGj
    }
    let kong = soldiers.操作.includes('开满孔') ? obj.initK + obj.kaiK : obj.initK
    if (soldiers.宝石) {
        let obj1 = baoshi.filter(e => e.value == soldiers.宝石)[0]
        gongji += kong * obj1.gongji
        fangyu += kong * obj1.fangyu
        xueliang += kong * obj1.xueliang
    }
    console.log(gongji, fangyu, xueliang)
    return {
        gongji,
        fangyu,
        xueliang
    }
}
// const generalFY = computed(() => {
//     let initVal = 0
//     //武将等级 
//     initVal += general.lvl * 40
//     //武将魄力 
//     let initVal2 = initVal
//     //角色称号 百分比加成

//     // 庄园科技百分比加成
//     initVal+=initVal2*0.02*role.武将防御术
//     //角色称号
//     // initVal+=userChenghao(initVal2).fangyu
//     return initVal + ''
// })
//官职变化
watch(() => role.guanzhi, (newval, oldval) => {
    if (newval < oldval) return
    const initName = guanzhiOptions.filter(e => e.value == oldval)[0].label
    const toName = guanzhiOptions.filter(e => e.value == newval)[0].label
    const val = guanzhiOptions.reduce((pre, current) => {
        if (current.value > oldval && current.value <= newval) {
            return pre + current.value
        } else {
            return pre
        }
    }, 0)
    message.success(`从${initName}升级到${toName}需要${val}声望`, { duration: 5000 })
})
</script>

<style scoped>
.container {
    display: flex;
    justify-content: space-around;
}

.n-card {
    max-width: 30%;
}
</style>