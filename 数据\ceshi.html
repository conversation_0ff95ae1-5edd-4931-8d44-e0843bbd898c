<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>

<body>
    <form action="" method="get">
        <select name="count10">
            <option value="1">打开</option>
            <option value="2">关闭</option>
        </select>
        <input type="submit" value="提交">
    </form>
    <script>
        let npcIdArr=[],temNpcIdArr=[]
        let content = `"54"	"史家庄丁"	"1"
"55"	"史家庄童仆"	"1"
"56"	"史家家将"	"1"
"57"	"神厨.赵老三"	"1"
"58"	"史家丫鬟.小红"	"1"
"59"	"九纹龙.史进"	"1"
"60"	"龙老大"	"1"
"61"	"史家丫鬟.小紫"	"1"
"62"	"史老太公"	"1"
"63"	"史家家奴.史小参"	"1"
"64"	"亡命飞贼"	"1"
"65"	"亡命飞贼副将"	"1"
"66"	"亡命飞贼头领"	"1"
"67"	"养蜂人"	"1"
"68"	"杀人蜂"	"1"
"69"	"桃花寨小头目"	"1"
"70"	"桃花寨小喽罗"	"1"
"71"	"桃花寨巡山使"	"1"
"72"	"小霸王.周通"	"1"
"73"	"打虎将.李忠"	"1"
"74"	"钓叟"	"1"
"75"	"蜂王"	"1"
"76"	"摆渡人"	"1"
"77"	"巨大老龟"	"1"
"78"	"邪龙"	"1"
"79"	"猎户.李吉"	"1"
"80"	"少华山石匠"	"1"
"81"	"松鼠"	"1"
"82"	"白兔"	"1"
"83"	"恶狼"	"1"
"84"	"少华山小头领部"	"1"
"85"	"黑风岭砍柴老者"	"1"
"86"	"云游道长"	"1"
"87"	"巡山喽啰"	"1"
"88"	"守山喽啰"	"1"
"89"	"少华山领队"	"1"
"90"	"少华山老伙头"	"1"
"91"	"少华山护卫"	"1"
"92"	"白花蛇.杨春"	"1"
"93"	"跳涧虎.陈达"	"1"
"94"	"神机军师.朱武"	"1"
"114"	"桃花村牧童"	"1"
"115"	"食牛兽"	"1"
"116"	"大食牛兽"	"1"
"117"	"暮年老者"	"1"
"118"	"桃花村村民"	"1"
"119"	"桃花庄庄客"	"1"
"120"	"桃花庄管家"	"1"
"121"	"刘太公"	"1"
"122"	"桃花庄丫鬟"	"1"
"123"	"流花河渔民"	"1"
"124"	"渔翁"	"1"
"125"	"飞瀑盗贼"	"1"
"126"	"大飞瀑盗贼"	"1"
"127"	"飞瀑盗贼头领"	"1"
"128"	"巨蛇"	"1"
"129"	"流花兽"	"1"
"130"	"流花鱼"	"1"
"131"	"流花河鱼鹰"	"1"
"132"	"流花河巨兽"	"1"
"357"	"长须老者"	"1"
"358"	"游方和尚"	"1"
"359"	"活动大使"	"1"
"360"	"紫禁卫士"	"1"
"361"	"妙手回春"	"1"
"362"	"樊老板"	"1"
"363"	"酒保"	"1"
"364"	"守城军官"	"1"
"365"	"守城士兵"	"1"
"366"	"烧饼贩子"	"1"
"367"	"秀才"	"1"
"368"	"杂货贩"	"1"
"369"	"老鸨"	"1"
"370"	"销金楼打手"	"1"
"371"	"教书先生"	"1"
"372"	"三只手.陈老大"	"1"
"373"	"赌徒"	"1"
"374"	"屠三刀"	"1"
"375"	"官家小姐"	"1"
"376"	"上香客"	"1"
"377"	"送子娘娘"	"1"
"378"	"庙祝"	"1"
"379"	"小混混"	"1"
"380"	"妙曼姑娘"	"1"
"381"	"疯狗"	"1"
"382"	"张大头"	"1"
"383"	"李大富"	"1"
"384"	"混混头子"	"1"
"385"	"枣红马"	"1"
"386"	"侯府卫士"	"1"
"387"	"张大头"	"1"
"388"	"卖膏药的"	"1"
"389"	"太尉府卫士长"	"1"
"390"	"太尉府卫士"	"1"
"391"	"太尉府丫鬟"	"1"
"392"	"高俅"	"1"
"393"	"富安"	"1"
"394"	"高衙内"	"1"
"395"	"天字会守卫"	"1"
"396"	"天字会弟子"	"1"
"397"	"地字老大"	"1"
"398"	"天字老大"	"1"
"399"	"人字老大"	"1"
"400"	"和字老大"	"1"
"401"	"赵捕头"	"1"
"402"	"捕快"	"1"
"403"	"张教头"	"1"
"404"	"林冲娘子.张氏"	"1"
"405"	"女使.锦儿"	"1"
"406"	"老黄牛"	"1"
"407"	"红包大使"	"1"
"408"	"铁匠.张牛"	"1"
"409"	"兵器贩子"	"1"
"410"	"老裁缝"	"1"
"411"	"牛二"	"1"
"412"	"宝库守卫"	"1"
"413"	"柴家庄庄客"	"1"
"414"	"红角巨兽"	"1"
"415"	"成年红角巨兽"	"1"
"416"	"红树林飞贼头领"	"1"
"417"	"红树林飞贼"	"1"
"418"	"红角兽王"	"1"
"419"	"冰封老人"	"1"
"420"	"红尾巨兽"	"1"
"421"	"董超"	"1"
"422"	"薛霸"	"1"
"423"	"枣红马"	"1"
"424"	"柴家庄教头"	"1"
"425"	"柴家庄庄客"	"1"
"426"	"小旋风.柴进"	"1"
"427"	"柴府管家.李于定"	"1"
"428"	"柴家家奴"	"1"
"429"	"饿狼"	"1"
"430"	"巨大饿狼"	"1"
"431"	"水洞守卫"	"1"
"432"	"水灵怪兽"	"1"
"433"	"木洞守卫"	"1"
"434"	"木灵怪兽"	"1"
"435"	"冰洞守卫"	"1"
"436"	"冰灵怪兽"	"1"
"437"	"黑金使者"	"1"
"438"	"紫土使者"	"1"
"439"	"铁木使者"	"1"
"440"	"寒水使者"	"1"
"441"	"烈焰使者"	"1"
"442"	"冰魄异兽"	"1"
"443"	"守城军官"	"4652"
"444"	"守城士兵"	"4652"
"445"	"守城士兵"	"4652"
"446"	"老黄牛"	"4552"
"447"	"红包大使"	"4552"
"448"	"铁匠.张牛"	"6974"
"449"	"头大"	"4452"
"452"	"头大"	"1"
"453"	"烈焰使者"	"6975"
"454"	"牛二"	"6975"
"455"	"兵器贩子"	"6975"
"456"	"老裁缝"	"6976"
"457"	"长须老者"	"4352"
"458"	"游方和尚"	"6977"
"459"	"活动大使"	"6977"
"460"	"守城军官"	"6978"
"461"	"守城士兵"	"6978"
"462"	"守城士兵"	"6978"
"463"	"樊老板"	"6980"
"464"	"酒保"	"6980"
"465"	"红角巨兽"	"6989"
"466"	"红角巨兽"	"6989"
"467"	"红角巨兽"	"6989"
"468"	"红角巨兽"	"6991"
"469"	"红角巨兽"	"6991"
"470"	"红角巨兽"	"6991"
"471"	"红角巨兽"	"6994"
"472"	"红角巨兽"	"6994"
"473"	"红角巨兽"	"6994"
"474"	"红角巨兽"	"6995"
"475"	"红角巨兽"	"6995"
"476"	"红角巨兽"	"6995"
"477"	"红角巨兽"	"6995"
"478"	"红树林飞贼头领"	"6997"
"479"	"红树林飞贼"	"6996"
"480"	"红角巨兽"	"6992"
"481"	"红角巨兽"	"6992"
"482"	"红角巨兽"	"6992"
"483"	"成年红角巨兽"	"6992"
"484"	"红角巨兽"	"6999"
"485"	"红角巨兽"	"6999"
"486"	"成年红角巨兽"	"6999"
"487"	"红角巨兽"	"7000"
"488"	"红角巨兽"	"7000"
"489"	"成年红角巨兽"	"7000"
"490"	"成年红角巨兽"	"7001"
"491"	"红角巨兽"	"7001"
"492"	"红角巨兽"	"7001"
"493"	"红角巨兽"	"7001"
"494"	"红角巨兽"	"6998"
"495"	"红角巨兽"	"6998"
"496"	"成年红角巨兽"	"6998"
"497"	"成年红角巨兽"	"6996"
"498"	"红角巨兽"	"6996"
"499"	"红角巨兽"	"7004"
"500"	"红角巨兽"	"7004"
"501"	"成年红角巨兽"	"7004"
"502"	"成年红角巨兽"	"7005"
"503"	"红角巨兽"	"7005"
"504"	"红角巨兽"	"7005"
"505"	"红角巨兽"	"7003"
"506"	"红角巨兽"	"7003"
"507"	"成年红角巨兽"	"7003"
"509"	"董超"	"6990"
"510"	"薛霸"	"6990"
"511"	"枣红马"	"7008"
"512"	"柴家庄庄客"	"7010"
"513"	"柴家庄庄客"	"7010"
"514"	"柴家庄教头"	"7010"
"515"	"小旋风.柴进"	"7011"
"516"	"柴府管家.李于定"	"7012"
"517"	"柴家家奴"	"7013"
"518"	"饿狼"	"7015"
"519"	"巨大饿狼"	"7021"
"520"	"烧饼贩子"	"7022"
"521"	"秀才"	"7022"
"522"	"老鸨"	"7023"
"523"	"销金楼打手"	"7023"
"524"	"销金楼打手"	"7023"
"525"	"杂货贩"	"7024"
"526"	"教书先生"	"7025"
"527"	"屠三刀"	"7026"
"528"	"三只手.陈老大"	"7027"
"529"	"官家小姐"	"7030"
"530"	"上香客"	"7030"
"531"	"上香客"	"7031"
"532"	"送子娘娘"	"7031"
"533"	"庙祝"	"7031"
"534"	"红角巨兽"	"6993"
"535"	"红角巨兽"	"6993"
"536"	"红角巨兽"	"6993"
"537"	"成年红角巨兽"	"7002"
"538"	"红角巨兽"	"7002"
"539"	"红角巨兽"	"7002"
"540"	"红角巨兽"	"7002"
"541"	"冰封老人"	"7006"
"542"	"养蜂人"	"6151"
"543"	"打虎将.李忠"	"7055"
"544"	"小霸王.周通"	"7054"
"545"	"桃花寨小喽罗"	"7052"
"546"	"桃花寨小喽罗"	"7052"
"547"	"桃花寨小喽罗"	"7052"
"548"	"桃花寨小头目"	"7053"
"549"	"桃花寨巡山使"	"7053"
"550"	"桃花寨小头目"	"7049"
"551"	"少华山小头领部"	"7063"
"552"	"松鼠"	"5950"
"553"	"白兔"	"5950"
"554"	"恶狼"	"5950"
"555"	"恶狼"	"7056"
"556"	"白兔"	"7056"
"557"	"松鼠"	"7056"
"558"	"松鼠"	"7057"
"559"	"白兔"	"7057"
"560"	"恶狼"	"7057"
"561"	"恶狼"	"7058"
"562"	"白兔"	"7058"
"563"	"松鼠"	"7058"
"564"	"松鼠"	"7059"
"565"	"白兔"	"7059"
"566"	"恶狼"	"7059"
"567"	"恶狼"	"7060"
"568"	"白兔"	"7060"
"569"	"松鼠"	"7060"
"570"	"松鼠"	"7061"
"571"	"白兔"	"7061"
"572"	"恶狼"	"7061"
"573"	"黑风岭砍柴老者"	"7062"
"574"	"巡山喽啰"	"7064"
"575"	"守山喽啰"	"7067"
"576"	"守山喽啰"	"7071"
"577"	"守山喽啰"	"7071"
"578"	"少华山领队"	"7071"
"579"	"少华山老伙头"	"7072"
"580"	"少华山护卫"	"7073"
"581"	"白花蛇.杨春"	"7073"
"582"	"跳涧虎.陈达"	"7074"
"583"	"神机军师.朱武"	"7075"
"584"	"杀人蜂"	"6150"
"585"	"杀人蜂"	"6150"
"586"	"杀人蜂"	"6150"
"587"	"杀人蜂"	"7033"
"588"	"杀人蜂"	"7033"
"589"	"杀人蜂"	"7033"
"590"	"杀人蜂"	"7034"
"591"	"杀人蜂"	"7034"
"592"	"杀人蜂"	"7034"
"593"	"杀人蜂"	"7035"
"594"	"杀人蜂"	"7035"
"595"	"杀人蜂"	"7035"
"596"	"杀人蜂"	"7036"
"597"	"杀人蜂"	"7036"
"598"	"杀人蜂"	"7036"
"599"	"杀人蜂"	"7037"
"600"	"杀人蜂"	"7037"
"601"	"杀人蜂"	"7037"
"602"	"杀人蜂"	"7038"
"603"	"杀人蜂"	"7038"
"604"	"杀人蜂"	"7038"
"605"	"杀人蜂"	"7038"
"606"	"杀人蜂"	"7039"
"607"	"杀人蜂"	"7039"
"608"	"杀人蜂"	"7039"
"609"	"杀人蜂"	"7040"
"610"	"杀人蜂"	"7040"
"611"	"杀人蜂"	"7040"
"612"	"钓叟"	"7041"
"613"	"摆渡人"	"7042"
"614"	"巨大老龟"	"7043"
"615"	"邪龙"	"7046"
"616"	"桃花村牧童"	"6153"
"617"	"流花河鱼鹰"	"7091"
"618"	"流花河巨兽"	"7092"
"619"	"飞瀑盗贼头领"	"7128"
"620"	"大飞瀑盗贼"	"7127"
"621"	"飞瀑盗贼"	"7127"
"622"	"飞瀑盗贼"	"7127"
"623"	"飞瀑盗贼"	"7085"
"624"	"飞瀑盗贼"	"7085"
"625"	"渔翁"	"7086"
"626"	"暮年老者"	"7087"
"627"	"桃花村村民"	"7121"
"628"	"桃花庄庄客"	"7122"
"629"	"桃花庄庄客"	"7122"
"630"	"桃花庄管家"	"7123"
"631"	"刘太公"	"7124"
"632"	"桃花庄丫鬟"	"7125"
"633"	"食牛兽"	"6253"
"634"	"食牛兽"	"7078"
"635"	"大食牛兽"	"7078"
"636"	"食牛兽"	"7079"
"637"	"大食牛兽"	"7079"
"638"	"食牛兽"	"7076"
"639"	"大食牛兽"	"7076"
"640"	"食牛兽"	"7077"
"641"	"大食牛兽"	"7077"
"642"	"食牛兽"	"7080"
"643"	"大食牛兽"	"7080"
"644"	"食牛兽"	"7081"
"645"	"大食牛兽"	"7081"
"646"	"食牛兽"	"7082"
"647"	"大食牛兽"	"7082"
"648"	"食牛兽"	"7083"
"649"	"大食牛兽"	"7083"
"650"	"食牛兽"	"7084"
"651"	"大食牛兽"	"7084"
"652"	"巨蛇"	"7089"
"653"	"流花河渔民"	"7088"
"654"	"流花鱼"	"7090"
"655"	"流花鱼"	"7090"
"656"	"流花河鱼鹰"	"7090"
"657"	"流花兽"	"7090"
"658"	"流花兽"	"7090"
"659"	"流花鱼"	"7094"
"660"	"流花鱼"	"7094"
"661"	"流花兽"	"7094"
"662"	"流花兽"	"7094"
"663"	"流花鱼"	"7095"
"664"	"流花鱼"	"7095"
"665"	"流花兽"	"7095"
"666"	"流花兽"	"7095"
"667"	"流花鱼"	"7096"
"668"	"流花鱼"	"7096"
"669"	"流花兽"	"7096"
"670"	"流花兽"	"7096"
"671"	"流花鱼"	"7098"
"672"	"流花鱼"	"7098"
"673"	"流花兽"	"7098"
"674"	"流花兽"	"7098"
"675"	"流花鱼"	"7099"
"676"	"流花鱼"	"7099"
"677"	"流花兽"	"7099"
"678"	"流花兽"	"7099"
"679"	"流花鱼"	"7100"
"680"	"流花鱼"	"7100"
"681"	"流花兽"	"7100"
"682"	"流花兽"	"7100"
"683"	"流花鱼"	"7101"
"684"	"流花鱼"	"7101"
"685"	"流花兽"	"7101"
"686"	"流花兽"	"7101"
"687"	"流花鱼"	"7102"
"688"	"流花鱼"	"7102"
"689"	"流花兽"	"7102"
"690"	"流花兽"	"7102"
"691"	"流花鱼"	"7103"
"692"	"流花鱼"	"7103"
"693"	"流花兽"	"7103"
"694"	"流花兽"	"7103"
"695"	"流花鱼"	"7104"
"696"	"流花鱼"	"7104"
"697"	"流花兽"	"7104"
"698"	"流花兽"	"7104"
"699"	"流花鱼"	"7105"
"700"	"流花鱼"	"7105"
"701"	"流花兽"	"7105"
"702"	"流花兽"	"7105"
"703"	"流花鱼"	"7106"
"704"	"流花鱼"	"7106"
"705"	"流花兽"	"7106"
"706"	"流花兽"	"7106"
"707"	"流花鱼"	"7107"
"708"	"流花鱼"	"7107"
"709"	"流花兽"	"7107"
"710"	"流花兽"	"7107"
"711"	"流花鱼"	"7108"
"712"	"流花鱼"	"7108"
"713"	"流花兽"	"7108"
"714"	"流花兽"	"7108"
"715"	"流花鱼"	"7109"
"716"	"流花鱼"	"7109"
"717"	"流花兽"	"7109"
"718"	"流花兽"	"7109"
"719"	"流花鱼"	"7110"
"720"	"流花鱼"	"7110"
"721"	"流花兽"	"7110"
"722"	"流花兽"	"7110"
"723"	"流花鱼"	"7112"
"724"	"流花鱼"	"7112"
"725"	"流花兽"	"7112"
"726"	"流花兽"	"7112"
"727"	"流花鱼"	"7113"
"728"	"流花鱼"	"7113"
"729"	"流花兽"	"7113"
"730"	"流花兽"	"7113"
"731"	"流花鱼"	"7114"
"732"	"流花鱼"	"7114"
"733"	"流花兽"	"7114"
"734"	"流花兽"	"7114"
"735"	"红角兽王"	"7003"
"736"	"史家庄丁"	"5853"
"737"	"史家庄丁"	"5854"
"738"	"史家庄丁"	"5854"
"739"	"史家庄丁"	"5754"
"740"	"史家庄丁"	"5754"
"741"	"史家家将"	"5754"
"742"	"史家丫鬟.小红"	"5855"
"743"	"禁军教头.王进"	"5755"
"744"	"王老太"	"1"
"745"	"王老太"	"5955"
"746"	"九纹龙.史进"	"5856"
"747"	"龙老大"	"5856"
"748"	"史家丫鬟.小紫"	"5857"
"749"	"史老太公"	"5757"
"750"	"史家家奴.史小参"	"5858"
"751"	"亡命飞贼"	"5860"
"752"	"亡命飞贼头领"	"5861"
"753"	"亡命飞贼副将"	"5760"
"754"	"禁军教头.王进"	"1"
"755"	"都尉.王生"	"1"
"756"	"王生妻子.佩蓉"	"1"
"757"	"都尉.王生"	"7153"
"758"	"王生妻子.佩蓉"	"7153"
"759"	"酒馆主人"	"1"
"760"	"酒馆主人"	"7147"
"761"	"老军"	"1"
"762"	"老军"	"7148"
"763"	"豹子头.林冲"	"1"
"764"	"豹子头.林冲"	"7149"
"765"	"守谷小喽罗"	"1"
"766"	"大吼猴"	"1"
"767"	"剧毒奇寒蛇"	"1"
"768"	"食人谷守卫"	"1"
"769"	"食人谷力士"	"1"
"770"	"食人怪鱼"	"1"
"771"	"食人谷护法"	"1"
"772"	"食人谷果农"	"1"
"773"	"食人谷五当家"	"1"
"774"	"食人谷四当家"	"1"
"775"	"食人谷三当家"	"1"
"776"	"食人谷二当家"	"1"
"777"	"食人谷大当家"	"1"
"778"	"食人谷长老"	"1"
"779"	"食人谷农夫"	"1"
"780"	"九头巨龙"	"1"
"781"	"食人谷农夫"	"7154"
"782"	"食人谷长老"	"7155"
"783"	"九头巨龙"	"7093"
"784"	"守谷小喽罗"	"7161"
"785"	"大吼猴"	"7162"
"786"	"剧毒奇寒蛇"	"7163"
"787"	"食人谷守卫"	"7164"
"788"	"食人谷守卫"	"7164"
"789"	"食人谷力士"	"7166"
"790"	"食人怪鱼"	"7167"
"791"	"食人谷护法"	"7168"
"792"	"食人谷果农"	"7169"
"793"	"食人谷五当家"	"7170"
"794"	"食人谷四当家"	"7171"
"795"	"食人谷三当家"	"7172"
"796"	"食人谷二当家"	"7173"
"797"	"食人谷大当家"	"7174"
`
        function convertTextToJsonArray(fileContent) {
           
            // Split the content into lines
            const lines = fileContent.trim().split('\n');

            // Extract the header line and remove the "" prefix
            const headerLineWithSource = lines[0];
            const headerLine = headerLineWithSource.replace("", "").trim(); //

            // Split the header line by multiple spaces to get the keys
            const keys = headerLine.split(/\s+/); // e.g., ['id', 'name', 'gpsId']

            const jsonArray = [];

            // Process the data lines (starting from the second line)
            for (let i = 1; i < lines.length; i++) {
                const line = lines[i].trim();
                if (line === "") {
                    continue; // Skip empty lines
                }

                // Split the data line by tabs
                const valuesWithQuotes = line.split('\t'); //

                if (valuesWithQuotes.length === keys.length) {
                    const entry = {};
                    for (let j = 0; j < keys.length; j++) {
                        // Remove the surrounding double quotes from each value
                        let value = valuesWithQuotes[j];
                        if (value.startsWith('"') && value.endsWith('"')) {
                            value = value.slice(1, -1);
                        }
                        let name='id'
                        if(j==1)name='name'
                        if(j==2)name='gpsId'
                        entry[name] = value;
                    }
                    jsonArray.push(entry);

                } else {
                    console.warn(`Skipping malformed line (expected ${keys.length} values, got ${valuesWithQuotes.length}): ${line}`);
                }
            }

            return jsonArray;
        }
        let jsonArray = convertTextToJsonArray(content)
        npcIdArr=jsonArray.filter(item=>item.gpsId!=1)
        temNpcIdArr=jsonArray.filter(item=>item.gpsId==1)
        npcIdArr.map(item=>{
            let tem=temNpcIdArr.find(tem=>tem.name==item.name)
            if(tem){
                item.temId=tem.id
            }
        })
        console.log(npcIdArr)
    </script>
</body>

</html>

<!-- gpsId!=1
id>36   导出名字  npcId
根据名字查找 gpsId=1的 npcId

查找物品表 -->