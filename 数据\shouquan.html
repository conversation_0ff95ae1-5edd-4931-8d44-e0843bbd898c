<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车辆授权</title>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .plate-number {
            font-size: 24px;
            color: #333;
            margin-bottom: 20px;
        }

        .auth-button {
            background-color: #07C160;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .auth-button:hover {
            background-color: #06AD56;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="plate-number" id="plate-number">车牌号：鲁P981HK</div>
        <button class="auth-button" onclick="handleAuth()">我要授权</button>
        <div id="result1"></div>
        <div id="result2"></div>
        <div id="result3"></div>
    </div>

    <script>
        // // 微信配置
        // wx.config({
        //     debug: false,
        //     appId: 'wxc2da6be3627920ad', // 必填，公众号的唯一标识
        //     timestamp: '', // 必填，生成签名的时间戳
        //     nonceStr: '', // 必填，生成签名的随机串
        //     signature: '',// 必填，签名
        //     jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'] // 必填，需要使用的JS接口列表
        // });

        // 处理授权
        function handleAuth() {
            const appId = 'wxc2da6be3627920ad'; // 替换为您的公众号APPID
            const redirectUri = encodeURIComponent(window.location.href);
            const scope = 'snsapi_userinfo';
            const state = 'STATE';

            const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`;
            window.location.href = authUrl;
        }

        // 页面加载完成后检查URL中是否包含code参数
        window.onload = async function () {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const carId = urlParams.get('carId')
            const carCode = urlParams.get('carCode')
            if (carId && carCode) {
                document.getElementById('plate-number').innerHTML = '车牌号：' + carCode
            }
            if (code) {
                let url = "https://xl.wht56.com/jeecgboot/wechat/callback"
                document.getElementById('result1').innerHTML = '请求参数:' + JSON.stringify({ code: code, carId: carId })
                document.getElementById('result2').innerHTML = '微信返回的code:' + code

                //发送post请求
                let res = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json' // Correct header
                    },
                    body: JSON.stringify({ code: code, carId: carId })
                })
                let data = await res.json()
                document.getElementById('result3').innerHTML = JSON.stringify(data)
            }
        }

        // // 微信配置成功回调
        // wx.ready(function() {
        //     console.log('微信JS-SDK配置成功');
        // });

        // // 微信配置失败回调
        // wx.error(function(res) {
        //     console.error('微信JS-SDK配置失败:', res);
        // });
    </script>
</body>

</html>