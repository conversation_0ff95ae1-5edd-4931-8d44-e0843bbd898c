<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>

<body>
    <script>
        let goMapString = ``
function extractGoMapData(goCodeString) {
    let mapContent = "";

    // 1. Extract the content within the main map literal: map[string]Ditu{ ... }
    let match = goCodeString.match(/(?:map\[string\]Ditu\s*=\s*)?{\s*([\s\S]*?)\s*}\s*$/m);
    if (match && match[1]) {
        mapContent = match[1].trim();
    } else {
        console.error("无法找到地图数据内容。请确保输入是 Go 地图字面量字符串，例如：`var ditu = map[string]Ditu{...}` 或仅仅是 `{...}`。");
        return "";
    }

    // Remove a potential trailing comma from the entire map content
    if (mapContent.endsWith(',')) {
        mapContent = mapContent.substring(0, mapContent.length - 1);
    }

    const results = [];
    let currentParsePos = 0;

    // Auxiliary function: unescape strings like \" and \\
    const unescapeStr = (str) => str ? str.replace(/\\"/g, '"').replace(/\\\\/g, '\\') : "";
    // Auxiliary function: format coordinate string to ID
    const formatCoordinateToId = (coordStr) => coordStr ? coordStr.replace(/[^0-9]/g, "") : "";


    while (currentParsePos < mapContent.length) {
        // 2. Find the next map key (e.g., "6,3":)
        const keyMatch = mapContent.substring(currentParsePos).match(/^"([^"]+)"\s*:\s*{/);
        if (!keyMatch) {
            const remainingTrimmed = mapContent.substring(currentParsePos).trim();
            if (remainingTrimmed !== "" && remainingTrimmed !== ",") {
                // console.warn("解析提前结束，末尾有未处理字符:", remainingTrimmed.substring(0, 50));
            }
            break; // No more entries
        }

        const coordinateKey = keyMatch[1]; // e.g., "6,3"
        const id = formatCoordinateToId(coordinateKey); // Generate ID, e.g., "63"

        // Advance to the start of the struct content
        let structStartIndexInSubstring = currentParsePos + keyMatch[0].length;

        // 3. Find the matching closing brace '}' for the current Ditu struct
        let openBraces = 1;
        let structEndIndexInSubstring = -1;
        for (let i = structStartIndexInSubstring; i < mapContent.length; i++) {
            if (mapContent[i] === '{') {
                openBraces++;
            } else if (mapContent[i] === '}') {
                openBraces--;
                if (openBraces === 0) {
                    structEndIndexInSubstring = i;
                    break;
                }
            }
        }

        if (structEndIndexInSubstring === -1) {
            console.error("地图条目结构错误 (key:", coordinateKey, ")。未找到匹配的 '}'.");
            break;
        }

        const structStr = mapContent.substring(structStartIndexInSubstring, structEndIndexInSubstring);

        // 4. Extract fields from the struct string
        const nameMatch = structStr.match(/N\s*:\s*"((?:[^"\\]|\\.)*)"/);
        const descMatch = structStr.match(/M\s*:\s*"((?:[^"\\]|\\.)*)"/);

        const name = nameMatch ? unescapeStr(nameMatch[1]) : "";
        const desc = descMatch ? unescapeStr(descMatch[1]) : "";

        // Extract directional info (Name N and ID I)
        // Left (Z)
        const z_N_Match = structStr.match(/Z\s*:\s*Mapfx\s*{[\s\S]*?N\s*:\s*"((?:[^"\\]|\\.)*)"[\s\S]*?}/);
        const z_I_Match = structStr.match(/Z\s*:\s*Mapfx\s*{[\s\S]*?I\s*:\s*"((?:[^"\\]|\\.)*)"[\s\S]*?}/);
        const z_N = z_N_Match && z_N_Match[1] !== undefined ? unescapeStr(z_N_Match[1]) : "";
        const z_I_raw = z_I_Match && z_I_Match[1] !== undefined ? unescapeStr(z_I_Match[1]) : "";
        const z_I = formatCoordinateToId(z_I_raw);

        // Right (Y)
        const y_N_Match = structStr.match(/Y\s*:\s*Mapfx\s*{[\s\S]*?N\s*:\s*"((?:[^"\\]|\\.)*)"[\s\S]*?}/);
        const y_I_Match = structStr.match(/Y\s*:\s*Mapfx\s*{[\s\S]*?I\s*:\s*"((?:[^"\\]|\\.)*)"[\s\S]*?}/);
        const y_N = y_N_Match && y_N_Match[1] !== undefined ? unescapeStr(y_N_Match[1]) : "";
        const y_I_raw = y_I_Match && y_I_Match[1] !== undefined ? unescapeStr(y_I_Match[1]) : "";
        const y_I = formatCoordinateToId(y_I_raw);

        // Up (S)
        const s_N_Match = structStr.match(/S\s*:\s*Mapfx\s*{[\s\S]*?N\s*:\s*"((?:[^"\\]|\\.)*)"[\s\S]*?}/);
        const s_I_Match = structStr.match(/S\s*:\s*Mapfx\s*{[\s\S]*?I\s*:\s*"((?:[^"\\]|\\.)*)"[\s\S]*?}/);
        const s_N = s_N_Match && s_N_Match[1] !== undefined ? unescapeStr(s_N_Match[1]) : "";
        const s_I_raw = s_I_Match && s_I_Match[1] !== undefined ? unescapeStr(s_I_Match[1]) : "";
        const s_I = formatCoordinateToId(s_I_raw);

        // Down (X)
        const x_N_Match = structStr.match(/X\s*:\s*Mapfx\s*{[\s\S]*?N\s*:\s*"((?:[^"\\]|\\.)*)"[\s\S]*?}/);
        const x_I_Match = structStr.match(/X\s*:\s*Mapfx\s*{[\s\S]*?I\s*:\s*"((?:[^"\\]|\\.)*)"[\s\S]*?}/);
        const x_N = x_N_Match && x_N_Match[1] !== undefined ? unescapeStr(x_N_Match[1]) : "";
        const x_I_raw = x_I_Match && x_I_Match[1] !== undefined ? unescapeStr(x_I_Match[1]) : "";
        const x_I = formatCoordinateToId(x_I_raw);

        results.push(`${id}|${name}|${desc}|${z_N}|${z_I}|${y_N}|${y_I}|${s_N}|${s_I}|${x_N}|${x_I}`);

        // Move to the start of the next entry
        currentParsePos = structEndIndexInSubstring + 1;
        const endOfEntryMatch = mapContent.substring(currentParsePos).match(/^\s*,\s*/);
        if (endOfEntryMatch) {
            currentParsePos += endOfEntryMatch[0].length;
        }
    }

    return results.join("\n");
}

// --- Example Usage ---
// const goMapDataString = `
// var ditu = map[string]Ditu{
// "6,3":{L:"",F:"",I:"",N:"起点",M:"冒险开始的地方",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N:"北部屋", I:"6,2",X:0,Y:0},Z:Mapfx{F:"",N:"西部森林", I:"5,3",X:0,Y:0},Y:Mapfx{F:"",N:"东部小径", I:"7,3",X:0,Y:0},X:Mapfx{F:"",N:"南部沼泽", I:"6,4",X:0,Y:0},Npc:[]Npc{},Lsnpc:[]Lsnpc{},Fx:[]Mapfx{},Jq:map[int]bool{},},
// "40,38":{L:"",F:"",I:"",N:"小路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N:"小路",I:"40,37",X:0,Y:0},Z:Mapfx{F:"",N:"农田",I:"39,38",X:0,Y:0},Y:Mapfx{F:"",N:"伐木场",I:"41,38",X:0,Y:0},X:Mapfx{F:"",N:"柴家农庄",I:"40,39",X:0,Y:0},Npc:[]Npc{},Lsnpc:[]Lsnpc{},Fx:[]Mapfx{},Jq:map[int]bool{},},
// }
// `;

const extractedData = extractGoMapData(goMapString);
console.log(extractedData);

/*
Expected output for the example:
63|起点|冒险开始的地方|西部森林|53|东部小径|73|北部屋|62|南部沼泽|64
4038|小路||农田|3938|伐木场|4138|小路|4037|柴家农庄|4039
*/
    </script>
</body>

</html>
