package main
var fbditu = map[string]map[string]Ditu{
//柳树林
"liushulin": {
"8,3":{L:"",F:"",I:"",N:"柳树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "柳树林", I: "8,4",X:0,Y:0},Npc:[]Npc{{I:"317",S:2,Cz:false},{I:"318",S:1,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,4":{L:"",F:"",I:"",N:"柳树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "柳树林", I: "8,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "柳树林", I: "8,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,4":{L:"",F:"",I:"",N:"柳树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "柳树林", I: "11,4",X:0,Y:0},X:Mapfx{F:"",N: "柳树林", I: "10,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,4":{L:"",F:"",I:"",N:"柳树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "柳树林", I: "10,4",X:0,Y:0},Y:Mapfx{F:"",N: "大柳树", I: "12,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,4":{L:"",F:"",I:"",N:"大柳树",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "柳树林", I: "11,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{{F: "liushudong", I: "9,5",N:"进入树洞",X:0,Y:0},},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,5":{L:"out",F:"",I:"58,53",N:"史家庄大门",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "柳树林", I: "8,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,5":{L:"",F:"",I:"",N:"柳树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "柳树林", I: "8,4",X:0,Y:0},Z:Mapfx{F:"",N: "史家庄大门", I: "7,5",X:0,Y:0},Y:Mapfx{F:"",N: "柳树林", I: "9,5",X:0,Y:0},X:Mapfx{F:"",N: "柳树林", I: "8,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,5":{L:"",F:"",I:"",N:"柳树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "柳树林", I: "8,5",X:0,Y:0},Y:Mapfx{F:"",N: "柳树林", I: "10,5",X:0,Y:0},X:Mapfx{F:"",N: "柳树林", I: "9,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,5":{L:"",F:"",I:"",N:"柳树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "柳树林", I: "10,4",X:0,Y:0},Z:Mapfx{F:"",N: "柳树林", I: "9,5",X:0,Y:0},Y:Mapfx{F:"",N: "柳树林", I: "11,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,5":{L:"",F:"",I:"",N:"柳树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "柳树林", I: "10,5",X:0,Y:0},Y:Mapfx{F:"",N: "柳树林", I: "12,5",X:0,Y:0},X:Mapfx{F:"",N: "柳树林", I: "11,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,5":{L:"",F:"",I:"",N:"柳树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "柳树林", I: "11,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,6":{L:"",F:"",I:"",N:"柳树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "柳树林", I: "8,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "柳树林", I: "8,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,6":{L:"",F:"",I:"",N:"柳树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "柳树林", I: "9,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,6":{L:"",F:"",I:"",N:"柳树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "柳树林", I: "11,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,7":{L:"",F:"",I:"",N:"柳树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "柳树林", I: "8,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//柳树洞
"liushudong": {
"8,5":{L:"in",F:"liushulin",I:"12,4",N:"大柳树",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "树洞", I: "9,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,5":{L:"",F:"",I:"",N:"树洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "大柳树", I: "8,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{{I:"20013",S:2},}},
},
//资源山
"ziyuanshan": {
"8,3":{L:"",F:"",I:"",N:"资源山(10,1)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(10,2)", I: "9,3",X:0,Y:0},X:Mapfx{F:"",N: "资源山(9,1)", I: "8,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,3":{L:"",F:"",I:"",N:"资源山(10,2)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(10,1)", I: "8,3",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(10,3)", I: "10,3",X:0,Y:0},X:Mapfx{F:"",N: "资源山(9,2)", I: "9,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,3":{L:"",F:"",I:"",N:"资源山(10,3)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(10,2)", I: "9,3",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(10,4)", I: "11,3",X:0,Y:0},X:Mapfx{F:"",N: "资源山(9,3)", I: "10,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,3":{L:"",F:"",I:"",N:"资源山(10,4)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(10,3)", I: "10,3",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(10,5)", I: "12,3",X:0,Y:0},X:Mapfx{F:"",N: "资源山(9,4)", I: "11,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,3":{L:"",F:"",I:"",N:"资源山(10,5)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(10,4)", I: "11,3",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(10,6)", I: "13,3",X:0,Y:0},X:Mapfx{F:"",N: "资源山(9,5)", I: "12,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,3":{L:"",F:"",I:"",N:"资源山(10,6)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(10,5)", I: "12,3",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(10,7)", I: "14,3",X:0,Y:0},X:Mapfx{F:"",N: "资源山(9,6)", I: "13,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,3":{L:"",F:"",I:"",N:"资源山(10,7)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(10,6)", I: "13,3",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(10,8)", I: "15,3",X:0,Y:0},X:Mapfx{F:"",N: "资源山(9,7)", I: "14,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,3":{L:"",F:"",I:"",N:"资源山(10,8)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(10,7)", I: "14,3",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(10,9)", I: "16,3",X:0,Y:0},X:Mapfx{F:"",N: "资源山(9,8)", I: "15,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,3":{L:"",F:"",I:"",N:"资源山(10,9)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(10,8)", I: "15,3",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(10,10)", I: "17,3",X:0,Y:0},X:Mapfx{F:"",N: "资源山(9,9)", I: "16,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,3":{L:"",F:"",I:"",N:"资源山(10,10)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(10,9)", I: "16,3",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "资源山(9,10)", I: "17,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,4":{L:"",F:"",I:"",N:"资源山(9,1)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(10,1)", I: "8,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(9,2)", I: "9,4",X:0,Y:0},X:Mapfx{F:"",N: "资源山(8,1)", I: "8,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,4":{L:"",F:"",I:"",N:"资源山(9,2)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(10,2)", I: "9,3",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(9,1)", I: "8,4",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(9,3)", I: "10,4",X:0,Y:0},X:Mapfx{F:"",N: "资源山(8,2)", I: "9,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,4":{L:"",F:"",I:"",N:"资源山(9,3)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(10,3)", I: "10,3",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(9,2)", I: "9,4",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(9,4)", I: "11,4",X:0,Y:0},X:Mapfx{F:"",N: "资源山(8,3)", I: "10,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,4":{L:"",F:"",I:"",N:"资源山(9,4)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(10,4)", I: "11,3",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(9,3)", I: "10,4",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(9,5)", I: "12,4",X:0,Y:0},X:Mapfx{F:"",N: "资源山(8,4)", I: "11,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,4":{L:"",F:"",I:"",N:"资源山(9,5)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(10,5)", I: "12,3",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(9,4)", I: "11,4",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(9,6)", I: "13,4",X:0,Y:0},X:Mapfx{F:"",N: "资源山(8,5)", I: "12,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,4":{L:"",F:"",I:"",N:"资源山(9,6)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(10,6)", I: "13,3",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(9,5)", I: "12,4",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(9,7)", I: "14,4",X:0,Y:0},X:Mapfx{F:"",N: "资源山(8,6)", I: "13,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,4":{L:"",F:"",I:"",N:"资源山(9,7)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(10,7)", I: "14,3",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(9,6)", I: "13,4",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(9,8)", I: "15,4",X:0,Y:0},X:Mapfx{F:"",N: "资源山(8,7)", I: "14,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,4":{L:"",F:"",I:"",N:"资源山(9,8)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(10,8)", I: "15,3",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(9,7)", I: "14,4",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(9,9)", I: "16,4",X:0,Y:0},X:Mapfx{F:"",N: "资源山(8,8)", I: "15,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,4":{L:"",F:"",I:"",N:"资源山(9,9)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(10,9)", I: "16,3",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(9,8)", I: "15,4",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(9,10)", I: "17,4",X:0,Y:0},X:Mapfx{F:"",N: "资源山(8,9)", I: "16,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,4":{L:"",F:"",I:"",N:"资源山(9,10)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(10,10)", I: "17,3",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(9,9)", I: "16,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "资源山(8,10)", I: "17,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,5":{L:"",F:"",I:"",N:"资源山(8,1)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(9,1)", I: "8,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(8,2)", I: "9,5",X:0,Y:0},X:Mapfx{F:"",N: "资源山(7,1)", I: "8,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,5":{L:"",F:"",I:"",N:"资源山(8,2)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(9,2)", I: "9,4",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(8,1)", I: "8,5",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(8,3)", I: "10,5",X:0,Y:0},X:Mapfx{F:"",N: "资源山(7,2)", I: "9,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,5":{L:"",F:"",I:"",N:"资源山(8,3)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(9,3)", I: "10,4",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(8,2)", I: "9,5",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(8,4)", I: "11,5",X:0,Y:0},X:Mapfx{F:"",N: "资源山(7,3)", I: "10,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,5":{L:"",F:"",I:"",N:"资源山(8,4)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(9,4)", I: "11,4",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(8,3)", I: "10,5",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(8,5)", I: "12,5",X:0,Y:0},X:Mapfx{F:"",N: "资源山(7,4)", I: "11,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,5":{L:"",F:"",I:"",N:"资源山(8,5)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(9,5)", I: "12,4",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(8,4)", I: "11,5",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(8,6)", I: "13,5",X:0,Y:0},X:Mapfx{F:"",N: "资源山(7,5)", I: "12,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,5":{L:"",F:"",I:"",N:"资源山(8,6)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(9,6)", I: "13,4",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(8,5)", I: "12,5",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(8,7)", I: "14,5",X:0,Y:0},X:Mapfx{F:"",N: "资源山(7,6)", I: "13,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,5":{L:"",F:"",I:"",N:"资源山(8,7)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(9,7)", I: "14,4",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(8,6)", I: "13,5",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(8,8)", I: "15,5",X:0,Y:0},X:Mapfx{F:"",N: "资源山(7,7)", I: "14,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,5":{L:"",F:"",I:"",N:"资源山(8,8)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(9,8)", I: "15,4",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(8,7)", I: "14,5",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(8,9)", I: "16,5",X:0,Y:0},X:Mapfx{F:"",N: "资源山(7,8)", I: "15,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,5":{L:"",F:"",I:"",N:"资源山(8,9)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(9,9)", I: "16,4",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(8,8)", I: "15,5",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(8,10)", I: "17,5",X:0,Y:0},X:Mapfx{F:"",N: "资源山(7,9)", I: "16,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,5":{L:"",F:"",I:"",N:"资源山(8,10)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(9,10)", I: "17,4",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(8,9)", I: "16,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "资源山(7,10)", I: "17,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,6":{L:"",F:"",I:"",N:"资源山(7,1)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(8,1)", I: "8,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(7,2)", I: "9,6",X:0,Y:0},X:Mapfx{F:"",N: "资源山(6,1)", I: "8,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,6":{L:"",F:"",I:"",N:"资源山(7,2)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(8,2)", I: "9,5",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(7,1)", I: "8,6",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(7,3)", I: "10,6",X:0,Y:0},X:Mapfx{F:"",N: "资源山(6,2)", I: "9,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,6":{L:"",F:"",I:"",N:"资源山(7,3)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(8,3)", I: "10,5",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(7,2)", I: "9,6",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(7,4)", I: "11,6",X:0,Y:0},X:Mapfx{F:"",N: "资源山(6,3)", I: "10,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,6":{L:"",F:"",I:"",N:"资源山(7,4)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(8,4)", I: "11,5",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(7,3)", I: "10,6",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(7,5)", I: "12,6",X:0,Y:0},X:Mapfx{F:"",N: "资源山(6,4)", I: "11,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,6":{L:"",F:"",I:"",N:"资源山(7,5)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(8,5)", I: "12,5",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(7,4)", I: "11,6",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(7,6)", I: "13,6",X:0,Y:0},X:Mapfx{F:"",N: "资源山(6,5)", I: "12,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,6":{L:"",F:"",I:"",N:"资源山(7,6)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(8,6)", I: "13,5",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(7,5)", I: "12,6",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(7,7)", I: "14,6",X:0,Y:0},X:Mapfx{F:"",N: "资源山(6,6)", I: "13,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,6":{L:"",F:"",I:"",N:"资源山(7,7)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(8,7)", I: "14,5",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(7,6)", I: "13,6",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(7,8)", I: "15,6",X:0,Y:0},X:Mapfx{F:"",N: "资源山(6,7)", I: "14,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,6":{L:"",F:"",I:"",N:"资源山(7,8)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(8,8)", I: "15,5",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(7,7)", I: "14,6",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(7,9)", I: "16,6",X:0,Y:0},X:Mapfx{F:"",N: "资源山(6,8)", I: "15,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,6":{L:"",F:"",I:"",N:"资源山(7,9)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(8,9)", I: "16,5",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(7,8)", I: "15,6",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(7,10)", I: "17,6",X:0,Y:0},X:Mapfx{F:"",N: "资源山(6,9)", I: "16,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,6":{L:"",F:"",I:"",N:"资源山(7,10)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(8,10)", I: "17,5",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(7,9)", I: "16,6",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "资源山(6,10)", I: "17,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,7":{L:"",F:"",I:"",N:"资源山(6,1)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(7,1)", I: "8,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(6,2)", I: "9,7",X:0,Y:0},X:Mapfx{F:"",N: "资源山(5,1)", I: "8,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,7":{L:"",F:"",I:"",N:"资源山(6,2)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(7,2)", I: "9,6",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(6,1)", I: "8,7",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(6,3)", I: "10,7",X:0,Y:0},X:Mapfx{F:"",N: "资源山(5,2)", I: "9,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,7":{L:"",F:"",I:"",N:"资源山(6,3)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(7,3)", I: "10,6",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(6,2)", I: "9,7",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(6,4)", I: "11,7",X:0,Y:0},X:Mapfx{F:"",N: "资源山(5,3)", I: "10,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,7":{L:"",F:"",I:"",N:"资源山(6,4)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(7,4)", I: "11,6",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(6,3)", I: "10,7",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(6,5)", I: "12,7",X:0,Y:0},X:Mapfx{F:"",N: "资源山(5,4)", I: "11,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,7":{L:"",F:"",I:"",N:"资源山(6,5)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(7,5)", I: "12,6",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(6,4)", I: "11,7",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(6,6)", I: "13,7",X:0,Y:0},X:Mapfx{F:"",N: "资源山(5,5)", I: "12,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,7":{L:"",F:"",I:"",N:"资源山(6,6)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(7,6)", I: "13,6",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(6,5)", I: "12,7",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(6,7)", I: "14,7",X:0,Y:0},X:Mapfx{F:"",N: "资源山(5,6)", I: "13,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,7":{L:"",F:"",I:"",N:"资源山(6,7)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(7,7)", I: "14,6",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(6,6)", I: "13,7",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(6,8)", I: "15,7",X:0,Y:0},X:Mapfx{F:"",N: "资源山(5,7)", I: "14,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,7":{L:"",F:"",I:"",N:"资源山(6,8)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(7,8)", I: "15,6",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(6,7)", I: "14,7",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(6,9)", I: "16,7",X:0,Y:0},X:Mapfx{F:"",N: "资源山(5,8)", I: "15,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,7":{L:"",F:"",I:"",N:"资源山(6,9)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(7,9)", I: "16,6",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(6,8)", I: "15,7",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(6,10)", I: "17,7",X:0,Y:0},X:Mapfx{F:"",N: "资源山(5,9)", I: "16,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,7":{L:"",F:"",I:"",N:"资源山(6,10)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(7,10)", I: "17,6",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(6,9)", I: "16,7",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "资源山(5,10)", I: "17,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,8":{L:"",F:"",I:"",N:"资源山(5,1)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(6,1)", I: "8,7",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(5,2)", I: "9,8",X:0,Y:0},X:Mapfx{F:"",N: "资源山(4,1)", I: "8,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,8":{L:"",F:"",I:"",N:"资源山(5,2)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(6,2)", I: "9,7",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(5,1)", I: "8,8",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(5,3)", I: "10,8",X:0,Y:0},X:Mapfx{F:"",N: "资源山(4,2)", I: "9,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,8":{L:"",F:"",I:"",N:"资源山(5,3)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(6,3)", I: "10,7",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(5,2)", I: "9,8",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(5,4)", I: "11,8",X:0,Y:0},X:Mapfx{F:"",N: "资源山(4,3)", I: "10,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,8":{L:"",F:"",I:"",N:"资源山(5,4)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(6,4)", I: "11,7",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(5,3)", I: "10,8",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(5,5)", I: "12,8",X:0,Y:0},X:Mapfx{F:"",N: "资源山(4,4)", I: "11,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,8":{L:"",F:"",I:"",N:"资源山(5,5)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(6,5)", I: "12,7",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(5,4)", I: "11,8",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(5,6)", I: "13,8",X:0,Y:0},X:Mapfx{F:"",N: "资源山(4,5)", I: "12,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,8":{L:"",F:"",I:"",N:"资源山(5,6)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(6,6)", I: "13,7",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(5,5)", I: "12,8",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(5,7)", I: "14,8",X:0,Y:0},X:Mapfx{F:"",N: "资源山(4,6)", I: "13,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,8":{L:"",F:"",I:"",N:"资源山(5,7)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(6,7)", I: "14,7",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(5,6)", I: "13,8",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(5,8)", I: "15,8",X:0,Y:0},X:Mapfx{F:"",N: "资源山(4,7)", I: "14,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,8":{L:"",F:"",I:"",N:"资源山(5,8)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(6,8)", I: "15,7",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(5,7)", I: "14,8",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(5,9)", I: "16,8",X:0,Y:0},X:Mapfx{F:"",N: "资源山(4,8)", I: "15,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,8":{L:"",F:"",I:"",N:"资源山(5,9)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(6,9)", I: "16,7",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(5,8)", I: "15,8",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(5,10)", I: "17,8",X:0,Y:0},X:Mapfx{F:"",N: "资源山(4,9)", I: "16,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,8":{L:"",F:"",I:"",N:"资源山(5,10)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(6,10)", I: "17,7",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(5,9)", I: "16,8",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "资源山(4,10)", I: "17,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,9":{L:"",F:"",I:"",N:"资源山(4,1)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(5,1)", I: "8,8",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(4,2)", I: "9,9",X:0,Y:0},X:Mapfx{F:"",N: "资源山(3,1)", I: "8,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,9":{L:"",F:"",I:"",N:"资源山(4,2)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(5,2)", I: "9,8",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(4,1)", I: "8,9",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(4,3)", I: "10,9",X:0,Y:0},X:Mapfx{F:"",N: "资源山(3,2)", I: "9,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,9":{L:"",F:"",I:"",N:"资源山(4,3)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(5,3)", I: "10,8",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(4,2)", I: "9,9",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(4,4)", I: "11,9",X:0,Y:0},X:Mapfx{F:"",N: "资源山(3,3)", I: "10,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,9":{L:"",F:"",I:"",N:"资源山(4,4)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(5,4)", I: "11,8",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(4,3)", I: "10,9",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(4,5)", I: "12,9",X:0,Y:0},X:Mapfx{F:"",N: "资源山(3,4)", I: "11,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,9":{L:"",F:"",I:"",N:"资源山(4,5)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(5,5)", I: "12,8",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(4,4)", I: "11,9",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(4,6)", I: "13,9",X:0,Y:0},X:Mapfx{F:"",N: "资源山(3,5)", I: "12,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,9":{L:"",F:"",I:"",N:"资源山(4,6)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(5,6)", I: "13,8",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(4,5)", I: "12,9",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(4,7)", I: "14,9",X:0,Y:0},X:Mapfx{F:"",N: "资源山(3,6)", I: "13,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,9":{L:"",F:"",I:"",N:"资源山(4,7)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(5,7)", I: "14,8",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(4,6)", I: "13,9",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(4,8)", I: "15,9",X:0,Y:0},X:Mapfx{F:"",N: "资源山(3,7)", I: "14,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,9":{L:"",F:"",I:"",N:"资源山(4,8)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(5,8)", I: "15,8",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(4,7)", I: "14,9",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(4,9)", I: "16,9",X:0,Y:0},X:Mapfx{F:"",N: "资源山(3,8)", I: "15,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,9":{L:"",F:"",I:"",N:"资源山(4,9)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(5,9)", I: "16,8",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(4,8)", I: "15,9",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(4,10)", I: "17,9",X:0,Y:0},X:Mapfx{F:"",N: "资源山(3,9)", I: "16,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,9":{L:"",F:"",I:"",N:"资源山(4,10)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(5,10)", I: "17,8",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(4,9)", I: "16,9",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "资源山(3,10)", I: "17,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,10":{L:"",F:"",I:"",N:"资源山(3,1)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(4,1)", I: "8,9",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(3,2)", I: "9,10",X:0,Y:0},X:Mapfx{F:"",N: "资源山(2,1)", I: "8,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,10":{L:"",F:"",I:"",N:"资源山(3,2)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(4,2)", I: "9,9",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(3,1)", I: "8,10",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(3,3)", I: "10,10",X:0,Y:0},X:Mapfx{F:"",N: "资源山(2,2)", I: "9,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,10":{L:"",F:"",I:"",N:"资源山(3,3)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(4,3)", I: "10,9",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(3,2)", I: "9,10",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(3,4)", I: "11,10",X:0,Y:0},X:Mapfx{F:"",N: "资源山(2,3)", I: "10,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,10":{L:"",F:"",I:"",N:"资源山(3,4)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(4,4)", I: "11,9",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(3,3)", I: "10,10",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(3,5)", I: "12,10",X:0,Y:0},X:Mapfx{F:"",N: "资源山(2,4)", I: "11,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,10":{L:"",F:"",I:"",N:"资源山(3,5)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(4,5)", I: "12,9",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(3,4)", I: "11,10",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(3,6)", I: "13,10",X:0,Y:0},X:Mapfx{F:"",N: "资源山(2,5)", I: "12,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,10":{L:"",F:"",I:"",N:"资源山(3,6)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(4,6)", I: "13,9",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(3,5)", I: "12,10",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(3,7)", I: "14,10",X:0,Y:0},X:Mapfx{F:"",N: "资源山(2,6)", I: "13,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,10":{L:"",F:"",I:"",N:"资源山(3,7)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(4,7)", I: "14,9",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(3,6)", I: "13,10",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(3,8)", I: "15,10",X:0,Y:0},X:Mapfx{F:"",N: "资源山(2,7)", I: "14,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,10":{L:"",F:"",I:"",N:"资源山(3,8)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(4,8)", I: "15,9",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(3,7)", I: "14,10",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(3,9)", I: "16,10",X:0,Y:0},X:Mapfx{F:"",N: "资源山(2,8)", I: "15,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,10":{L:"",F:"",I:"",N:"资源山(3,9)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(4,9)", I: "16,9",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(3,8)", I: "15,10",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(3,10)", I: "17,10",X:0,Y:0},X:Mapfx{F:"",N: "资源山(2,9)", I: "16,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,10":{L:"",F:"",I:"",N:"资源山(3,10)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(4,10)", I: "17,9",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(3,9)", I: "16,10",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "资源山(2,10)", I: "17,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,11":{L:"",F:"",I:"",N:"资源山(2,1)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(3,1)", I: "8,10",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(2,2)", I: "9,11",X:0,Y:0},X:Mapfx{F:"",N: "资源山(1,1)", I: "8,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,11":{L:"",F:"",I:"",N:"资源山(2,2)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(3,2)", I: "9,10",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(2,1)", I: "8,11",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(2,3)", I: "10,11",X:0,Y:0},X:Mapfx{F:"",N: "资源山(1,2)", I: "9,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,11":{L:"",F:"",I:"",N:"资源山(2,3)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(3,3)", I: "10,10",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(2,2)", I: "9,11",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(2,4)", I: "11,11",X:0,Y:0},X:Mapfx{F:"",N: "资源山(1,3)", I: "10,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,11":{L:"",F:"",I:"",N:"资源山(2,4)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(3,4)", I: "11,10",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(2,3)", I: "10,11",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(2,5)", I: "12,11",X:0,Y:0},X:Mapfx{F:"",N: "资源山(1,4)", I: "11,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,11":{L:"",F:"",I:"",N:"资源山(2,5)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(3,5)", I: "12,10",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(2,4)", I: "11,11",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(2,6)", I: "13,11",X:0,Y:0},X:Mapfx{F:"",N: "资源山(1,5)", I: "12,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,11":{L:"",F:"",I:"",N:"资源山(2,6)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(3,6)", I: "13,10",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(2,5)", I: "12,11",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(2,7)", I: "14,11",X:0,Y:0},X:Mapfx{F:"",N: "资源山(1,6)", I: "13,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,11":{L:"",F:"",I:"",N:"资源山(2,7)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(3,7)", I: "14,10",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(2,6)", I: "13,11",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(2,8)", I: "15,11",X:0,Y:0},X:Mapfx{F:"",N: "资源山(1,7)", I: "14,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,11":{L:"",F:"",I:"",N:"资源山(2,8)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(3,8)", I: "15,10",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(2,7)", I: "14,11",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(2,9)", I: "16,11",X:0,Y:0},X:Mapfx{F:"",N: "资源山(1,8)", I: "15,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,11":{L:"",F:"",I:"",N:"资源山(2,9)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(3,9)", I: "16,10",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(2,8)", I: "15,11",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(2,10)", I: "17,11",X:0,Y:0},X:Mapfx{F:"",N: "资源山(1,9)", I: "16,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,11":{L:"",F:"",I:"",N:"资源山(2,10)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(3,10)", I: "17,10",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(2,9)", I: "16,11",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "资源山(1,10)", I: "17,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,12":{L:"",F:"",I:"",N:"资源山(1,1)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(2,1)", I: "8,11",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(1,2)", I: "9,12",X:0,Y:0},X:Mapfx{F:"",N: "官道", I: "8,13",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,12":{L:"",F:"",I:"",N:"资源山(1,2)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(2,2)", I: "9,11",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(1,1)", I: "8,12",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,12":{L:"",F:"",I:"",N:"资源山(1,3)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(2,3)", I: "10,11",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(1,4)", I: "11,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,12":{L:"",F:"",I:"",N:"资源山(1,4)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(2,4)", I: "11,11",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(1,3)", I: "10,12",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(1,5)", I: "12,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,12":{L:"",F:"",I:"",N:"资源山(1,5)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(2,5)", I: "12,11",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(1,4)", I: "11,12",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(1,6)", I: "13,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,12":{L:"",F:"",I:"",N:"资源山(1,6)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(2,6)", I: "13,11",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(1,5)", I: "12,12",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(1,7)", I: "14,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,12":{L:"",F:"",I:"",N:"资源山(1,7)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(2,7)", I: "14,11",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(1,6)", I: "13,12",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(1,8)", I: "15,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,12":{L:"",F:"",I:"",N:"资源山(1,8)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(2,8)", I: "15,11",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(1,7)", I: "14,12",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(1,9)", I: "16,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,12":{L:"",F:"",I:"",N:"资源山(1,9)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(2,9)", I: "16,11",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(1,8)", I: "15,12",X:0,Y:0},Y:Mapfx{F:"",N: "资源山(1,10)", I: "17,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,12":{L:"",F:"",I:"",N:"资源山(1,10)",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(2,10)", I: "17,11",X:0,Y:0},Z:Mapfx{F:"",N: "资源山(1,9)", I: "16,12",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,13":{L:"out",F:"",I:"58,52",N:"官道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "资源山(1,1)", I: "8,12",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//松树林
"songshulin": {
"11,5":{L:"out",F:"",I:"59,48",N:"少华狭道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "松树林", I: "11,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,6":{L:"",F:"",I:"",N:"松树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "松树林", I: "10,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,6":{L:"",F:"",I:"",N:"松树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "少华狭道", I: "11,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "黑风坳", I: "12,6",X:0,Y:0},X:Mapfx{F:"",N: "松树林", I: "11,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,6":{L:"",F:"",I:"",N:"黑风坳",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "松树林", I: "11,6",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,7":{L:"",F:"",I:"",N:"松树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "松树林", I: "10,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "松树林", I: "11,7",X:0,Y:0},X:Mapfx{F:"",N: "松树林", I: "10,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,7":{L:"",F:"",I:"",N:"松树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "松树林", I: "11,6",X:0,Y:0},Z:Mapfx{F:"",N: "松树林", I: "10,7",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,8":{L:"",F:"",I:"",N:"松树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "松树林", I: "10,8",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,8":{L:"",F:"",I:"",N:"松树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "松树林", I: "10,7",X:0,Y:0},Z:Mapfx{F:"",N: "松树林", I: "9,8",X:0,Y:0},Y:Mapfx{F:"",N: "松树林", I: "11,8",X:0,Y:0},X:Mapfx{F:"",N: "少华山脚", I: "10,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,8":{L:"",F:"",I:"",N:"松树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "松树林", I: "10,8",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,9":{L:"out",F:"",I:"59,51",N:"少华山脚",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "松树林", I: "10,8",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//桃树林
"taoshulin": {
"10,4":{L:"",F:"",I:"",N:"桃树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "食人谷悬崖", I: "11,4",X:0,Y:0},X:Mapfx{F:"",N: "桃树林", I: "10,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,4":{L:"",F:"",I:"",N:"食人谷悬崖",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "桃树林", I: "10,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{{F: "shirengu", I: "5,3",N:"进入食人谷",X:0,Y:0},},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,5":{L:"",F:"",I:"",N:"桃树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃树林", I: "10,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "桃树林", I: "11,5",X:0,Y:0},X:Mapfx{F:"",N: "桃树林", I: "10,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,5":{L:"",F:"",I:"",N:"桃树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "桃树林", I: "10,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "桃树林", I: "11,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,6":{L:"out",F:"",I:"61,53",N:"桃花村村口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "桃树林", I: "10,6",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,6":{L:"",F:"",I:"",N:"桃树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃树林", I: "10,5",X:0,Y:0},Z:Mapfx{F:"",N: "桃花村村口", I: "9,6",X:0,Y:0},Y:Mapfx{F:"",N: "桃树林", I: "11,6",X:0,Y:0},X:Mapfx{F:"",N: "桃树林", I: "10,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,6":{L:"",F:"",I:"",N:"桃树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃树林", I: "11,5",X:0,Y:0},Z:Mapfx{F:"",N: "桃树林", I: "10,6",X:0,Y:0},Y:Mapfx{F:"",N: "桃花村小路", I: "12,6",X:0,Y:0},X:Mapfx{F:"",N: "桃树林", I: "11,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,6":{L:"out",F:"",I:"64,53",N:"桃花村小路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "桃树林", I: "11,6",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,7":{L:"",F:"",I:"",N:"桃树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃树林", I: "10,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "桃树林", I: "11,7",X:0,Y:0},X:Mapfx{F:"",N: "桃树林", I: "10,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,7":{L:"",F:"",I:"",N:"桃树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃树林", I: "11,6",X:0,Y:0},Z:Mapfx{F:"",N: "桃树林", I: "10,7",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "桃树林", I: "11,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,8":{L:"",F:"",I:"",N:"桃树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃树林", I: "10,7",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "桃树林", I: "11,8",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,8":{L:"",F:"",I:"",N:"桃树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃树林", I: "11,7",X:0,Y:0},Z:Mapfx{F:"",N: "桃树林", I: "10,8",X:0,Y:0},Y:Mapfx{F:"",N: "小溪边", I: "12,8",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,8":{L:"",F:"",I:"",N:"小溪边",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "桃树林", I: "11,8",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,9":{L:"",F:"",I:"",N:"桃花瀑布",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//桃花阵
"taohuazhen": {
"9,3":{L:"out",F:"",I:"65,42",N:"桃花山山腰",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "桃花阵", I: "9,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,3":{L:"",F:"",I:"",N:"桃花阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃花山山腰", I: "9,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "桃花阵", I: "10,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,4":{L:"",F:"",I:"",N:"桃花阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃花山山腰", I: "9,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "桃花阵", I: "9,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,4":{L:"",F:"",I:"",N:"桃花阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃花阵", I: "10,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "桃花阵", I: "10,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,5":{L:"",F:"",I:"",N:"桃花阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "桃花阵", I: "8,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,5":{L:"",F:"",I:"",N:"桃花阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃花阵", I: "9,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "桃花阵", I: "10,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,5":{L:"",F:"",I:"",N:"桃花阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃花阵", I: "10,4",X:0,Y:0},Z:Mapfx{F:"",N: "桃花阵", I: "9,5",X:0,Y:0},Y:Mapfx{F:"",N: "桃花阵", I: "11,5",X:0,Y:0},X:Mapfx{F:"",N: "桃花阵", I: "10,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,5":{L:"",F:"",I:"",N:"桃花阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃花阵", I: "10,5",X:0,Y:0},Z:Mapfx{F:"",N: "桃花阵", I: "10,5",X:0,Y:0},Y:Mapfx{F:"",N: "桃花涧", I: "12,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,5":{L:"",F:"",I:"",N:"桃花涧",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "桃花阵", I: "11,5",X:0,Y:0},Y:Mapfx{F:"",N: "桃花涧中央", I: "13,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,5":{L:"",F:"",I:"",N:"桃花涧中央",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "桃花涧", I: "12,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "涧底", I: "13,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,6":{L:"",F:"",I:"",N:"桃花阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃花阵", I: "8,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "桃花阵", I: "9,6",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,6":{L:"",F:"",I:"",N:"桃花阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "桃花阵", I: "8,6",X:0,Y:0},Y:Mapfx{F:"",N: "桃花阵", I: "10,6",X:0,Y:0},X:Mapfx{F:"",N: "桃花山脚", I: "9,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,6":{L:"",F:"",I:"",N:"桃花阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃花阵", I: "10,5",X:0,Y:0},Z:Mapfx{F:"",N: "桃花阵", I: "9,6",X:0,Y:0},Y:Mapfx{F:"",N: "桃花阵", I: "11,6",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,6":{L:"",F:"",I:"",N:"桃花阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "桃花阵", I: "10,6",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,6":{L:"",F:"",I:"",N:"涧底",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃花涧中央", I: "13,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "水云洞", I: "14,6",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,6":{L:"",F:"",I:"",N:"水云洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "涧底", I: "13,6",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "洞中小路", I: "14,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,7":{L:"out",F:"",I:"61,51",N:"桃花山脚",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "桃花阵", I: "9,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,7":{L:"",F:"",I:"",N:"洞中小路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "水云洞", I: "14,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "水云洞深处", I: "14,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,8":{L:"",F:"",I:"",N:"水云洞深处",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "洞中小路", I: "14,7",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//黑风岭
"heifengling": {
"12,0":{L:"",F:"",I:"",N:"巨型洞穴",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "潮湿小路", I: "12,1",X:0,Y:0},Npc:[]Npc{{I:"22",S:1,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,1":{L:"",F:"",I:"",N:"内洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "潮湿小路", I: "12,1",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{{I:"21",S:1,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,1":{L:"",F:"",I:"",N:"潮湿小路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "巨型洞穴", I: "12,0",X:0,Y:0},Z:Mapfx{F:"",N: "内洞", I: "11,1",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "窄路", I: "12,2",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,2":{L:"",F:"",I:"",N:"窄路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "潮湿小路", I: "12,1",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "大洞穴", I: "13,2",X:0,Y:0},X:Mapfx{F:"",N: "洞穴", I: "12,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,2":{L:"",F:"",I:"",N:"大洞穴",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "窄路", I: "12,2",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{{I:"19",S:1,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,3":{L:"",F:"",I:"",N:"黑松林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "黑松林", I: "6,4",X:0,Y:0},Npc:[]Npc{{I:"9",S:3,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,3":{L:"",F:"",I:"",N:"黑松林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "破庙", I: "8,3",X:0,Y:0},X:Mapfx{F:"",N: "黑松林", I: "7,4",X:0,Y:0},Npc:[]Npc{{I:"9",S:3,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,3":{L:"",F:"",I:"",N:"破庙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "黑松林", I: "7,3",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{{I:"10",S:1,Cz:true},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,3":{L:"",F:"",I:"",N:"洞穴",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "窄路", I: "12,2",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "干草洞穴", I: "13,3",X:0,Y:0},X:Mapfx{F:"",N: "猛虎洞", I: "12,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,3":{L:"",F:"",I:"",N:"干草洞穴",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "洞穴", I: "12,3",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{{I:"17",S:1,Cz:false},{I:"17",S:1,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,4":{L:"in",F:"songshulin",I:"12,6",N:"黑风坳",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "黑松林", I: "5,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,4":{L:"",F:"",I:"",N:"黑松林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "黑风坳", I: "4,4",X:0,Y:0},Y:Mapfx{F:"",N: "黑松林", I: "6,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{{I:"9",S:3,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{{I:"20006",S:2},}},
"6,4":{L:"",F:"",I:"",N:"黑松林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "黑松林", I: "6,3",X:0,Y:0},Z:Mapfx{F:"",N: "黑松林", I: "5,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{{I:"9",S:3,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,4":{L:"",F:"",I:"",N:"黑松林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "黑松林", I: "7,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "黑松林", I: "8,4",X:0,Y:0},X:Mapfx{F:"",N: "黑松林", I: "7,5",X:0,Y:0},Npc:[]Npc{{I:"9",S:3,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,4":{L:"",F:"",I:"",N:"黑松林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "黑松林", I: "7,4",X:0,Y:0},Y:Mapfx{F:"",N: "乱石河边", I: "9,4",X:0,Y:0},X:Mapfx{F:"",N: "黑松林", I: "8,5",X:0,Y:0},Npc:[]Npc{{I:"9",S:3,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,4":{L:"",F:"",I:"",N:"乱石河边",M:"",Ds:0,Dz:0,Dy:1,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "黑松林", I: "8,4",X:0,Y:0},Y:Mapfx{F:"",N: "乱石河", I: "10,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{{I:"12",S:1,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,4":{L:"",F:"",I:"",N:"乱石河",M:"",Ds:0,Dz:0,Dy:1,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "乱石河边", I: "9,4",X:0,Y:0},Y:Mapfx{F:"",N: "猛虎坡", I: "11,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{{I:"13",S:1,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,4":{L:"",F:"",I:"",N:"猛虎坡",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "乱石河", I: "10,4",X:0,Y:0},Y:Mapfx{F:"",N: "猛虎洞", I: "12,4",X:0,Y:0},X:Mapfx{F:"",N: "狩猎茅屋", I: "11,5",X:0,Y:0},Npc:[]Npc{{I:"15",S:1,Cz:false},{I:"15",S:1,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,4":{L:"",F:"",I:"",N:"猛虎洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "洞穴", I: "12,3",X:0,Y:0},Z:Mapfx{F:"",N: "猛虎坡", I: "11,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{{I:"18",S:1,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,5":{L:"",F:"",I:"",N:"黑松林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "黑松林", I: "7,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{{I:"9",S:3,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,5":{L:"",F:"",I:"",N:"黑松林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "黑松林", I: "8,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{{I:"9",S:3,Cz:false},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,5":{L:"",F:"",I:"",N:"狩猎茅屋",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "猛虎坡", I: "11,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{{I:"14",S:1,Cz:true},},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//食人谷
"shirengu": {
"5,2":{L:"in",F:"taoshulin",I:"11,4",N:"悬崖大树",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "食人谷口", I: "5,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,2":{L:"",F:"",I:"",N:"奇异果园",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "巨型峡谷", I: "9,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,2":{L:"",F:"",I:"",N:"北偏厅",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "大堂", I: "11,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,3":{L:"",F:"",I:"",N:"食人谷口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "悬崖大树", I: "5,2",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "一线天", I: "6,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,3":{L:"",F:"",I:"",N:"一线天",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "食人谷口", I: "5,3",X:0,Y:0},Y:Mapfx{F:"",N: "狭小石门", I: "7,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,3":{L:"",F:"",I:"",N:"狭小石门",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "一线天", I: "6,3",X:0,Y:0},Y:Mapfx{F:"",N: "小路", I: "8,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,3":{L:"",F:"",I:"",N:"小路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "狭小石门", I: "7,3",X:0,Y:0},Y:Mapfx{F:"",N: "巨型峡谷", I: "9,3",X:0,Y:0},X:Mapfx{F:"",N: "小河边", I: "8,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,3":{L:"",F:"",I:"",N:"巨型峡谷",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "奇异果园", I: "9,2",X:0,Y:0},Z:Mapfx{F:"",N: "小路", I: "8,3",X:0,Y:0},Y:Mapfx{F:"",N: "谷内小屋", I: "10,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,3":{L:"",F:"",I:"",N:"谷内小屋",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "巨型峡谷", I: "9,3",X:0,Y:0},Y:Mapfx{F:"",N: "大堂", I: "11,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,3":{L:"",F:"",I:"",N:"大堂",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "北偏厅", I: "11,2",X:0,Y:0},Z:Mapfx{F:"",N: "谷内小屋", I: "10,3",X:0,Y:0},Y:Mapfx{F:"",N: "暗室", I: "12,3",X:0,Y:0},X:Mapfx{F:"",N: "南偏厅", I: "11,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,3":{L:"",F:"",I:"",N:"暗室",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "大堂", I: "11,3",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,4":{L:"",F:"",I:"",N:"小河边",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "小路", I: "8,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "河底", I: "9,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,4":{L:"",F:"",I:"",N:"河底",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "小河边", I: "8,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,4":{L:"",F:"",I:"",N:"南偏厅",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "大堂", I: "11,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//冰魄洞
"bingpodong": {
"8,1":{L:"",F:"",I:"",N:"木洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "木洞", I: "9,1",X:0,Y:0},X:Mapfx{F:"",N: "木洞", I: "8,2",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,1":{L:"",F:"",I:"",N:"木洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "木洞", I: "8,1",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "木洞", I: "9,2",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,1":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "冰洞", I: "12,1",X:0,Y:0},X:Mapfx{F:"",N: "冰洞", I: "11,2",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,1":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "冰洞", I: "11,1",X:0,Y:0},Y:Mapfx{F:"",N: "冰洞", I: "13,1",X:0,Y:0},X:Mapfx{F:"",N: "冰洞", I: "12,2",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,1":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "冰洞", I: "12,1",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "冰洞", I: "13,2",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,2":{L:"",F:"",I:"",N:"水洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "水洞", I: "6,2",X:0,Y:0},X:Mapfx{F:"",N: "水洞", I: "5,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,2":{L:"",F:"",I:"",N:"水洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "水洞", I: "5,2",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "水洞", I: "6,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,2":{L:"",F:"",I:"",N:"木洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "木洞", I: "8,1",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "木洞", I: "8,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,2":{L:"",F:"",I:"",N:"木洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "木洞", I: "9,1",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "木洞", I: "9,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,2":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "冰洞", I: "11,1",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "冰洞", I: "12,2",X:0,Y:0},X:Mapfx{F:"",N: "冰洞", I: "11,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,2":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "冰洞", I: "12,1",X:0,Y:0},Z:Mapfx{F:"",N: "冰洞", I: "11,2",X:0,Y:0},Y:Mapfx{F:"",N: "冰洞", I: "13,2",X:0,Y:0},X:Mapfx{F:"",N: "冰洞", I: "12,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,2":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "冰洞", I: "13,1",X:0,Y:0},Z:Mapfx{F:"",N: "冰洞", I: "12,2",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "冰洞", I: "13,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,3":{L:"out",F:"",I:"44,44",N:"红树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "水洞", I: "5,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,3":{L:"",F:"",I:"",N:"水洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "水洞", I: "5,2",X:0,Y:0},Z:Mapfx{F:"",N: "红树林", I: "4,3",X:0,Y:0},Y:Mapfx{F:"",N: "水洞", I: "6,3",X:0,Y:0},X:Mapfx{F:"",N: "水洞", I: "5,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,3":{L:"",F:"",I:"",N:"水洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "水洞", I: "6,2",X:0,Y:0},Z:Mapfx{F:"",N: "水洞", I: "5,3",X:0,Y:0},Y:Mapfx{F:"",N: "水洞", I: "7,3",X:0,Y:0},X:Mapfx{F:"",N: "水洞", I: "6,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,3":{L:"",F:"",I:"",N:"水洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "水洞", I: "6,3",X:0,Y:0},Y:Mapfx{F:"",N: "木洞", I: "8,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,3":{L:"",F:"",I:"",N:"木洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "木洞", I: "8,2",X:0,Y:0},Z:Mapfx{F:"",N: "水洞", I: "7,3",X:0,Y:0},Y:Mapfx{F:"",N: "木洞", I: "9,3",X:0,Y:0},X:Mapfx{F:"",N: "木洞", I: "8,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,3":{L:"",F:"",I:"",N:"木洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "木洞", I: "9,2",X:0,Y:0},Z:Mapfx{F:"",N: "木洞", I: "8,3",X:0,Y:0},Y:Mapfx{F:"",N: "木洞", I: "10,3",X:0,Y:0},X:Mapfx{F:"",N: "木洞", I: "9,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,3":{L:"",F:"",I:"",N:"木洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "木洞", I: "9,3",X:0,Y:0},Y:Mapfx{F:"",N: "冰洞", I: "11,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,3":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "冰洞", I: "11,2",X:0,Y:0},Z:Mapfx{F:"",N: "木洞", I: "10,3",X:0,Y:0},Y:Mapfx{F:"",N: "冰洞", I: "12,3",X:0,Y:0},X:Mapfx{F:"",N: "冰洞", I: "11,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,3":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "冰洞", I: "12,2",X:0,Y:0},Z:Mapfx{F:"",N: "冰洞", I: "11,3",X:0,Y:0},Y:Mapfx{F:"",N: "冰洞", I: "13,3",X:0,Y:0},X:Mapfx{F:"",N: "冰洞", I: "12,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,3":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "冰洞", I: "13,2",X:0,Y:0},Z:Mapfx{F:"",N: "冰洞", I: "12,3",X:0,Y:0},Y:Mapfx{F:"",N: "冰洞", I: "14,3",X:0,Y:0},X:Mapfx{F:"",N: "冰洞", I: "13,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,3":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "冰洞", I: "13,3",X:0,Y:0},Y:Mapfx{F:"",N: "黑金桥", I: "15,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,3":{L:"",F:"",I:"",N:"黑金桥",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "冰洞", I: "14,3",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "紫土桥", I: "15,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,4":{L:"",F:"",I:"",N:"水洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "水洞", I: "5,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "水洞", I: "6,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,4":{L:"",F:"",I:"",N:"水洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "水洞", I: "6,3",X:0,Y:0},Z:Mapfx{F:"",N: "水洞", I: "5,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,4":{L:"",F:"",I:"",N:"木洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "木洞", I: "8,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "木洞", I: "9,4",X:0,Y:0},X:Mapfx{F:"",N: "木洞", I: "8,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,4":{L:"",F:"",I:"",N:"木洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "木洞", I: "9,3",X:0,Y:0},Z:Mapfx{F:"",N: "木洞", I: "8,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "木洞", I: "9,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,4":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "冰洞", I: "11,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "冰洞", I: "12,4",X:0,Y:0},X:Mapfx{F:"",N: "冰洞", I: "11,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,4":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "冰洞", I: "12,3",X:0,Y:0},Z:Mapfx{F:"",N: "冰洞", I: "11,4",X:0,Y:0},Y:Mapfx{F:"",N: "冰洞", I: "13,4",X:0,Y:0},X:Mapfx{F:"",N: "冰洞", I: "12,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,4":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "冰洞", I: "13,3",X:0,Y:0},Z:Mapfx{F:"",N: "冰洞", I: "12,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "冰洞", I: "13,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,4":{L:"",F:"",I:"",N:"紫土桥",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "黑金桥", I: "15,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "铁木桥", I: "15,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,5":{L:"",F:"",I:"",N:"木洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "木洞", I: "8,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "木洞", I: "9,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,5":{L:"",F:"",I:"",N:"木洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "木洞", I: "9,4",X:0,Y:0},Z:Mapfx{F:"",N: "木洞", I: "8,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,5":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "冰洞", I: "11,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "冰洞", I: "12,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,5":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "冰洞", I: "12,4",X:0,Y:0},Z:Mapfx{F:"",N: "冰洞", I: "11,5",X:0,Y:0},Y:Mapfx{F:"",N: "冰洞", I: "13,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,5":{L:"",F:"",I:"",N:"冰洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "冰洞", I: "13,4",X:0,Y:0},Z:Mapfx{F:"",N: "冰洞", I: "12,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,5":{L:"",F:"",I:"",N:"铁木桥",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "紫土桥", I: "15,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,6":{L:"",F:"",I:"",N:"寒水桥",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "烈焰桥", I: "15,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,7":{L:"",F:"",I:"",N:"烈焰桥",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "寒水桥", I: "15,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "冰魄洞", I: "15,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,8":{L:"",F:"",I:"",N:"冰魄洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "烈焰桥", I: "15,7",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//太尉宝库
"taiweibaoku": {
"4,2":{L:"",F:"",I:"",N:"石室",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "石阶", I: "4,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,2":{L:"",F:"",I:"",N:"铁屋",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "铁屋", I: "6,2",X:0,Y:0},X:Mapfx{F:"",N: "过道", I: "5,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,2":{L:"",F:"",I:"",N:"铁屋",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "铁屋", I: "5,2",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,3":{L:"",F:"",I:"",N:"石阶",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "石阶", I: "4,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,3":{L:"",F:"",I:"",N:"石阶",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "石室", I: "4,2",X:0,Y:0},Z:Mapfx{F:"",N: "石阶", I: "3,3",X:0,Y:0},Y:Mapfx{F:"",N: "过道", I: "5,3",X:0,Y:0},X:Mapfx{F:"",N: "石室", I: "4,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,3":{L:"",F:"",I:"",N:"过道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "铁屋", I: "5,2",X:0,Y:0},Z:Mapfx{F:"",N: "石阶", I: "4,3",X:0,Y:0},Y:Mapfx{F:"",N: "秘道", I: "6,3",X:0,Y:0},X:Mapfx{F:"",N: "铁屋", I: "5,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,3":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "过道", I: "5,3",X:0,Y:0},Y:Mapfx{F:"",N: "秘道", I: "7,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,3":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "秘道", I: "6,3",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "秘道", I: "7,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,4":{L:"",F:"",I:"",N:"石室",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "石阶", I: "4,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,4":{L:"",F:"",I:"",N:"铁屋",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "过道", I: "5,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "铁屋", I: "6,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,4":{L:"",F:"",I:"",N:"铁屋",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "铁屋", I: "5,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,4":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "秘道", I: "7,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "秘道", I: "7,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,5":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "秘道", I: "6,5",X:0,Y:0},X:Mapfx{F:"",N: "秘道", I: "5,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,5":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "秘道", I: "5,5",X:0,Y:0},Y:Mapfx{F:"",N: "秘道", I: "7,5",X:0,Y:0},X:Mapfx{F:"",N: "秘道", I: "6,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,5":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "秘道", I: "7,4",X:0,Y:0},Z:Mapfx{F:"",N: "秘道", I: "6,5",X:0,Y:0},Y:Mapfx{F:"",N: "秘道", I: "8,5",X:0,Y:0},X:Mapfx{F:"",N: "秘道", I: "7,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,5":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "秘道", I: "7,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "秘道", I: "8,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,6":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "秘道", I: "5,6",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,6":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "秘道", I: "5,5",X:0,Y:0},Z:Mapfx{F:"",N: "秘道", I: "4,6",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "秘道", I: "5,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,6":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "秘道", I: "6,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,6":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "秘道", I: "7,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,6":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "秘道", I: "8,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,7":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "秘道", I: "5,7",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,7":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "秘道", I: "5,6",X:0,Y:0},Z:Mapfx{F:"",N: "秘道", I: "4,7",X:0,Y:0},Y:Mapfx{F:"",N: "秘道", I: "6,7",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,7":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "秘道", I: "5,7",X:0,Y:0},Y:Mapfx{F:"",N: "秘道", I: "7,7",X:0,Y:0},X:Mapfx{F:"",N: "秘道", I: "6,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,7":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "秘道", I: "6,7",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,8":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "秘道", I: "6,8",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,8":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "秘道", I: "6,7",X:0,Y:0},Z:Mapfx{F:"",N: "秘道", I: "5,8",X:0,Y:0},Y:Mapfx{F:"",N: "秘道", I: "7,8",X:0,Y:0},X:Mapfx{F:"",N: "秘道", I: "6,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,8":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "秘道", I: "6,8",X:0,Y:0},Y:Mapfx{F:"",N: "藏宝库巨门", I: "8,8",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,8":{L:"",F:"",I:"",N:"藏宝库巨门",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "秘道", I: "7,8",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "太尉宝库", I: "8,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,9":{L:"",F:"",I:"",N:"秘道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "秘道", I: "6,8",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,9":{L:"",F:"",I:"",N:"太尉宝库",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "藏宝库巨门", I: "8,8",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//云落山
"yunluoshan": {
"3,0":{L:"",F:"",I:"",N:"小草屋",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "云树山顶", I: "3,1",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,1":{L:"",F:"",I:"",N:"云树山顶",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "小草屋", I: "3,0",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "山坡", I: "3,2",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,2":{L:"",F:"",I:"",N:"山坡",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "云树山顶", I: "3,1",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "云树山脚", I: "3,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,3":{L:"",F:"",I:"",N:"云树山脚",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "山坡", I: "3,2",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "石子路", I: "3,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,4":{L:"",F:"",I:"",N:"石子路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "云树山脚", I: "3,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "土路", I: "3,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,5":{L:"",F:"",I:"",N:"土路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "石子路", I: "3,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "土路", I: "3,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,6":{L:"",F:"",I:"",N:"土路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "土路", I: "3,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "小河", I: "3,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"2,7":{L:"",F:"",I:"",N:"云树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "云树林", I: "2,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,7":{L:"",F:"",I:"",N:"小河",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "土路", I: "3,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "云树林", I: "3,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,7":{L:"",F:"",I:"",N:"云树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "云树林", I: "4,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"2,8":{L:"",F:"",I:"",N:"云树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "云树林", I: "2,7",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,8":{L:"",F:"",I:"",N:"云树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "小河", I: "3,7",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "云树林", I: "4,8",X:0,Y:0},X:Mapfx{F:"",N: "云树林边", I: "3,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,8":{L:"",F:"",I:"",N:"云树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "云树林", I: "4,7",X:0,Y:0},Z:Mapfx{F:"",N: "云树林", I: "3,8",X:0,Y:0},Y:Mapfx{F:"",N: "云树林", I: "5,8",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,8":{L:"",F:"",I:"",N:"云树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "云树林", I: "4,8",X:0,Y:0},Y:Mapfx{F:"",N: "云树林", I: "6,8",X:0,Y:0},X:Mapfx{F:"",N: "云树林", I: "5,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,8":{L:"",F:"",I:"",N:"云树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "云树林", I: "5,8",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,9":{L:"",F:"",I:"",N:"云树林边",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "云树林", I: "3,8",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,9":{L:"",F:"",I:"",N:"云树林",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "云树林", I: "5,8",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//九头巨龙
"tulong": {
"5,3":{L:"",F:"",I:"",N:"流花河乱流",M:"每天中午12:00及晚上20:00刷新出九头巨龙。注：不在规定时间进来也没有怪。",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,4":{L:"",F:"",I:"",N:"流花河底",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,5":{L:"",F:"",I:"",N:"河中巨石",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//九霄洞
"jiuxiao": {
"4,4":{L:"",F:"",I:"",N:"沙漠",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "沙漠", I: "5,4",X:0,Y:0},X:Mapfx{F:"",N: "沙漠", I: "4,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,4":{L:"",F:"",I:"",N:"沙漠",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "沙漠", I: "4,4",X:0,Y:0},Y:Mapfx{F:"",N: "沙漠", I: "6,4",X:0,Y:0},X:Mapfx{F:"",N: "沙漠", I: "5,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,4":{L:"",F:"",I:"",N:"沙漠",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "沙漠", I: "5,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "沙漠", I: "6,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,5":{L:"",F:"",I:"",N:"沙漠",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "沙漠", I: "4,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "沙漠", I: "5,5",X:0,Y:0},X:Mapfx{F:"",N: "沙漠", I: "4,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,5":{L:"",F:"",I:"",N:"沙漠",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "沙漠", I: "5,4",X:0,Y:0},Z:Mapfx{F:"",N: "沙漠", I: "4,5",X:0,Y:0},Y:Mapfx{F:"",N: "沙漠", I: "6,5",X:0,Y:0},X:Mapfx{F:"",N: "沙漠", I: "5,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,5":{L:"",F:"",I:"",N:"沙漠",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "沙漠", I: "6,4",X:0,Y:0},Z:Mapfx{F:"",N: "沙漠", I: "5,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "沙漠", I: "6,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,6":{L:"",F:"",I:"",N:"沙漠",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "沙漠", I: "4,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "沙漠", I: "5,6",X:0,Y:0},X:Mapfx{F:"",N: "沙漠", I: "4,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,6":{L:"",F:"",I:"",N:"沙漠",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "沙漠", I: "5,5",X:0,Y:0},Z:Mapfx{F:"",N: "沙漠", I: "4,6",X:0,Y:0},Y:Mapfx{F:"",N: "沙漠", I: "6,6",X:0,Y:0},X:Mapfx{F:"",N: "沙漠", I: "5,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,6":{L:"",F:"",I:"",N:"沙漠",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "沙漠", I: "6,5",X:0,Y:0},Z:Mapfx{F:"",N: "沙漠", I: "5,6",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "沙漠", I: "6,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,7":{L:"",F:"",I:"",N:"沙漠",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "沙漠", I: "4,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "沙漠", I: "5,7",X:0,Y:0},X:Mapfx{F:"",N: "沙漠", I: "4,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,7":{L:"",F:"",I:"",N:"沙漠",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "沙漠", I: "5,6",X:0,Y:0},Z:Mapfx{F:"",N: "沙漠", I: "4,7",X:0,Y:0},Y:Mapfx{F:"",N: "沙漠", I: "6,7",X:0,Y:0},X:Mapfx{F:"",N: "沙漠", I: "5,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,7":{L:"",F:"",I:"",N:"沙漠",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "沙漠", I: "6,6",X:0,Y:0},Z:Mapfx{F:"",N: "沙漠", I: "5,7",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "沙漠", I: "6,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,8":{L:"",F:"",I:"",N:"沙漠",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "沙漠", I: "4,7",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "沙漠", I: "5,8",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,8":{L:"",F:"",I:"",N:"沙漠",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "沙漠", I: "5,7",X:0,Y:0},Z:Mapfx{F:"",N: "沙漠", I: "4,8",X:0,Y:0},Y:Mapfx{F:"",N: "沙漠", I: "6,8",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,8":{L:"",F:"",I:"",N:"沙漠",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "沙漠", I: "6,7",X:0,Y:0},Z:Mapfx{F:"",N: "沙漠", I: "5,8",X:0,Y:0},Y:Mapfx{F:"",N: "峭壁", I: "7,8",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,8":{L:"",F:"",I:"",N:"峭壁",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "沙漠", I: "6,8",X:0,Y:0},Y:Mapfx{F:"",N: "九霄洞", I: "8,8",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,8":{L:"",F:"",I:"",N:"九霄洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "峭壁", I: "7,8",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//落雪山
"luoxueshan": {
"4,0":{L:"",F:"",I:"",N:"寒冰坡",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "白雪路", I: "5,0",X:0,Y:0},X:Mapfx{F:"",N: "白雪河", I: "4,1",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,0":{L:"",F:"",I:"",N:"白雪路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "寒冰坡", I: "4,0",X:0,Y:0},Y:Mapfx{F:"",N: "落雪山顶", I: "6,0",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,0":{L:"",F:"",I:"",N:"落雪山顶",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "白雪路", I: "5,0",X:0,Y:0},Y:Mapfx{F:"",N: "落雪洞", I: "7,0",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,0":{L:"",F:"",I:"",N:"落雪洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "落雪山顶", I: "6,0",X:0,Y:0},Y:Mapfx{F:"",N: "落雪洞深处", I: "8,0",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,0":{L:"",F:"",I:"",N:"落雪洞深处",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "落雪洞", I: "7,0",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,1":{L:"",F:"",I:"",N:"白雪河",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "寒冰坡", I: "4,0",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "雪原", I: "4,2",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,2":{L:"",F:"",I:"",N:"雪原",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "白雪河", I: "4,1",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "冰原", I: "4,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,3":{L:"",F:"",I:"",N:"冰原",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "雪原", I: "4,2",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "白雪地", I: "4,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"1,4":{L:"",F:"",I:"",N:"雪地机关二",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "雪地", I: "2,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"2,4":{L:"",F:"",I:"",N:"雪地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "雪地机关二", I: "1,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "雪地", I: "2,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,4":{L:"",F:"",I:"",N:"雪地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "雪地", I: "3,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,4":{L:"",F:"",I:"",N:"白雪地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "冰原", I: "4,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "雪地", I: "4,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,4":{L:"",F:"",I:"",N:"雪地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "冰河", I: "6,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,4":{L:"",F:"",I:"",N:"冰河",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "雪地", I: "5,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"1,5":{L:"",F:"",I:"",N:"雪地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "雪地", I: "2,5",X:0,Y:0},X:Mapfx{F:"",N: "冰窟", I: "1,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"2,5":{L:"",F:"",I:"",N:"雪地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "雪地", I: "2,4",X:0,Y:0},Z:Mapfx{F:"",N: "雪地", I: "1,5",X:0,Y:0},Y:Mapfx{F:"",N: "雪地", I: "3,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,5":{L:"",F:"",I:"",N:"雪地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "雪地", I: "3,4",X:0,Y:0},Z:Mapfx{F:"",N: "雪地", I: "2,5",X:0,Y:0},Y:Mapfx{F:"",N: "雪地", I: "4,5",X:0,Y:0},X:Mapfx{F:"",N: "落雪山脚", I: "3,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,5":{L:"",F:"",I:"",N:"雪地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "白雪地", I: "4,4",X:0,Y:0},Z:Mapfx{F:"",N: "雪地", I: "3,5",X:0,Y:0},Y:Mapfx{F:"",N: "雪地", I: "5,5",X:0,Y:0},X:Mapfx{F:"",N: "雪地", I: "4,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,5":{L:"",F:"",I:"",N:"雪地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "雪地", I: "4,5",X:0,Y:0},Y:Mapfx{F:"",N: "雪地", I: "6,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,5":{L:"",F:"",I:"",N:"雪地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "雪地", I: "5,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"1,6":{L:"",F:"",I:"",N:"冰窟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "雪地", I: "1,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,6":{L:"",F:"",I:"",N:"落雪山脚",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "雪地", I: "3,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,6":{L:"",F:"",I:"",N:"雪地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "雪地", I: "4,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "雪地", I: "5,6",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,6":{L:"",F:"",I:"",N:"雪地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "雪地", I: "4,6",X:0,Y:0},Y:Mapfx{F:"",N: "雪地机关一", I: "6,6",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,6":{L:"",F:"",I:"",N:"雪地机关一",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "雪地", I: "5,6",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//入梦
"rumeng": {
"3,3":{L:"",F:"",I:"",N:"阴暗",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "阴暗", I: "3,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,3":{L:"",F:"",I:"",N:"阴暗",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "阴暗", I: "5,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,4":{L:"",F:"",I:"",N:"阴暗",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "阴暗", I: "3,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "阴暗", I: "4,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,4":{L:"",F:"",I:"",N:"阴暗",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "阴暗", I: "3,4",X:0,Y:0},Y:Mapfx{F:"",N: "阴暗", I: "5,4",X:0,Y:0},X:Mapfx{F:"",N: "阴暗", I: "4,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,4":{L:"",F:"",I:"",N:"阴暗",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "阴暗", I: "5,3",X:0,Y:0},Z:Mapfx{F:"",N: "阴暗", I: "4,4",X:0,Y:0},Y:Mapfx{F:"",N: "阴暗", I: "6,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,4":{L:"",F:"",I:"",N:"阴暗",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "阴暗", I: "5,4",X:0,Y:0},Y:Mapfx{F:"",N: "阴暗深处", I: "7,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,4":{L:"",F:"",I:"",N:"阴暗深处",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "阴暗", I: "6,4",X:0,Y:0},Y:Mapfx{F:"",N: "剑冢", I: "8,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,4":{L:"",F:"",I:"",N:"剑冢",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "阴暗深处", I: "7,4",X:0,Y:0},Y:Mapfx{F:"",N: "心魔之所", I: "9,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,4":{L:"",F:"",I:"",N:"心魔之所",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "剑冢", I: "8,4",X:0,Y:0},Y:Mapfx{F:"",N: "噩梦之所", I: "10,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,4":{L:"",F:"",I:"",N:"噩梦之所",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "心魔之所", I: "9,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,5":{L:"",F:"",I:"",N:"阴暗",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "阴暗", I: "4,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//地下古城
"dixiagucheng": {
"13,15":{L:"",F:"",I:"",N:"西北城角",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "城墙", I: "14,15",X:0,Y:0},X:Mapfx{F:"",N: "城墙", I: "13,16",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,15":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "西北城角", I: "13,15",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "15,15",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,15":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "城墙", I: "14,15",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "16,15",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,15":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "15,15",X:0,Y:0},Y:Mapfx{F:"",N: "玄武", I: "17,15",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,15":{L:"",F:"",I:"",N:"玄武",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "16,15",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"18,15":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "城墙", I: "19,15",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"19,15":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "城墙", I: "18,15",X:0,Y:0},Y:Mapfx{F:"",N: "城墙", I: "20,15",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"20,15":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "城墙", I: "19,15",X:0,Y:0},Y:Mapfx{F:"",N: "东北城角", I: "21,15",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"21,15":{L:"",F:"",I:"",N:"东北城角",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "城墙", I: "20,15",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "城墙", I: "21,16",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,16":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "西北城角", I: "13,15",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "将军府", I: "14,16",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,16":{L:"",F:"",I:"",N:"将军府",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "城墙", I: "13,16",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "15,16",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,16":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "将军府", I: "14,16",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "16,16",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,16":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "15,16",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "废墟", I: "16,17",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,16":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "废墟", I: "17,17",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"18,16":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "19,16",X:0,Y:0},X:Mapfx{F:"",N: "废墟", I: "18,17",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"19,16":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "18,16",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "20,16",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"20,16":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "19,16",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "太守府", I: "20,17",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"21,16":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "东北城角", I: "21,15",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "城墙", I: "21,17",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,17":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "14,17",X:0,Y:0},X:Mapfx{F:"",N: "城墙", I: "13,18",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,17":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "城墙", I: "13,17",X:0,Y:0},Y:Mapfx{F:"",N: "西北阁", I: "15,17",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,17":{L:"",F:"",I:"",N:"西北阁",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "14,17",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,17":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "16,16",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "废墟", I: "16,18",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,17":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "17,16",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "古城北街", I: "17,18",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"18,17":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "18,16",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "废墟", I: "18,18",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"19,17":{L:"",F:"",I:"",N:"东北阁",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "太守府", I: "20,17",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"20,17":{L:"",F:"",I:"",N:"太守府",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "20,16",X:0,Y:0},Z:Mapfx{F:"",N: "东北阁", I: "19,17",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"21,17":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "城墙", I: "21,16",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "城墙", I: "21,18",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,18":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "城墙", I: "13,17",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "14,18",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,18":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "城墙", I: "13,18",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "15,18",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,18":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "14,18",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "16,18",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,18":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "16,17",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "15,18",X:0,Y:0},Y:Mapfx{F:"",N: "古城北街", I: "17,18",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,18":{L:"",F:"",I:"",N:"古城北街",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "17,17",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "16,18",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "古城中心", I: "17,19",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"18,18":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "18,17",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "古城东街", I: "18,19",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"19,18":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "20,18",X:0,Y:0},X:Mapfx{F:"",N: "废墟", I: "19,19",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"20,18":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "19,18",X:0,Y:0},Y:Mapfx{F:"",N: "城墙", I: "21,18",X:0,Y:0},X:Mapfx{F:"",N: "废墟", I: "20,19",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"21,18":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "城墙", I: "21,17",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "20,18",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "青龙", I: "21,19",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,19":{L:"",F:"",I:"",N:"白虎",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "城墙", I: "13,20",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,19":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "15,19",X:0,Y:0},X:Mapfx{F:"",N: "废墟", I: "14,20",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,19":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "14,19",X:0,Y:0},Y:Mapfx{F:"",N: "古城西街", I: "16,19",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,19":{L:"",F:"",I:"",N:"古城西街",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "15,19",X:0,Y:0},Y:Mapfx{F:"",N: "古城中心", I: "17,19",X:0,Y:0},X:Mapfx{F:"",N: "废墟", I: "16,20",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,19":{L:"",F:"",I:"",N:"古城中心",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "古城北街", I: "17,18",X:0,Y:0},Z:Mapfx{F:"",N: "古城西街", I: "16,19",X:0,Y:0},Y:Mapfx{F:"",N: "古城东街", I: "18,19",X:0,Y:0},X:Mapfx{F:"",N: "古城南街", I: "17,20",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"18,19":{L:"",F:"",I:"",N:"古城东街",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "18,18",X:0,Y:0},Z:Mapfx{F:"",N: "古城中心", I: "17,19",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "19,19",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"19,19":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "19,18",X:0,Y:0},Z:Mapfx{F:"",N: "古城东街", I: "18,19",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "20,19",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"20,19":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "20,18",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "19,19",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"21,19":{L:"",F:"",I:"",N:"青龙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "城墙", I: "21,18",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,20":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "白虎", I: "13,19",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "城墙", I: "13,21",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,20":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "14,19",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "废墟", I: "14,21",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,20":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "16,20",X:0,Y:0},X:Mapfx{F:"",N: "西南阁", I: "15,21",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,20":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "古城西街", I: "16,19",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "15,20",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,20":{L:"",F:"",I:"",N:"古城南街",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "古城中心", I: "17,19",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "18,20",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"18,20":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "古城南街", I: "17,20",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "19,20",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"19,20":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "18,20",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "20,20",X:0,Y:0},X:Mapfx{F:"",N: "东南阁", I: "19,21",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"20,20":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "19,20",X:0,Y:0},Y:Mapfx{F:"",N: "城墙", I: "21,20",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"21,20":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "20,20",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "城墙", I: "21,21",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,21":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "城墙", I: "13,20",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "城墙", I: "13,22",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,21":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "14,20",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "废墟", I: "14,22",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,21":{L:"",F:"",I:"",N:"西南阁",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "15,20",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "16,21",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,21":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "西南阁", I: "15,21",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "废墟", I: "16,22",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,21":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "18,21",X:0,Y:0},X:Mapfx{F:"",N: "废墟", I: "17,22",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"18,21":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "17,21",X:0,Y:0},Y:Mapfx{F:"",N: "东南阁", I: "19,21",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"19,21":{L:"",F:"",I:"",N:"东南阁",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "19,20",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "18,21",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "废墟", I: "19,22",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"20,21":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "废墟", I: "20,22",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"21,21":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "城墙", I: "21,20",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "城墙", I: "21,22",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,22":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "城墙", I: "13,21",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "西南城角", I: "13,23",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,22":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "14,21",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "15,22",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,22":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "14,22",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "城墙", I: "15,23",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,22":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "16,21",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "城墙", I: "16,23",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,22":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "17,21",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "废墟", I: "18,22",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"18,22":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "废墟", I: "17,22",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "城墙", I: "18,23",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"19,22":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "东南阁", I: "19,21",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "城墙", I: "19,23",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"20,22":{L:"",F:"",I:"",N:"废墟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "20,21",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "城墙", I: "20,23",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"21,22":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "城墙", I: "21,21",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "东南城角", I: "21,23",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,23":{L:"",F:"",I:"",N:"西南城角",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "城墙", I: "13,22",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "城墙", I: "14,23",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,23":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "西南城角", I: "13,23",X:0,Y:0},Y:Mapfx{F:"",N: "城墙", I: "15,23",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,23":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "15,22",X:0,Y:0},Z:Mapfx{F:"",N: "城墙", I: "14,23",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,23":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "16,22",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,23":{L:"",F:"",I:"",N:"朱雀",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "城墙", I: "18,23",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"18,23":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "18,22",X:0,Y:0},Z:Mapfx{F:"",N: "朱雀", I: "17,23",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"19,23":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "19,22",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "城墙", I: "20,23",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"20,23":{L:"",F:"",I:"",N:"城墙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "废墟", I: "20,22",X:0,Y:0},Z:Mapfx{F:"",N: "城墙", I: "19,23",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"21,23":{L:"",F:"",I:"",N:"东南城角",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "城墙", I: "21,22",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//祝家庄
"zhujaizhuang": {
"11,10":{L:"",F:"",I:"",N:"天字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "和字号", I: "12,10",X:0,Y:0},X:Mapfx{F:"",N: "天字号", I: "11,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,10":{L:"",F:"",I:"",N:"和字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "天字号", I: "11,10",X:0,Y:0},Y:Mapfx{F:"",N: "和字号", I: "13,10",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,10":{L:"",F:"",I:"",N:"和字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "和字号", I: "12,10",X:0,Y:0},Y:Mapfx{F:"",N: "和字号", I: "14,10",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,10":{L:"",F:"",I:"",N:"和字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "和字号", I: "13,10",X:0,Y:0},Y:Mapfx{F:"",N: "和字号", I: "15,10",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,10":{L:"",F:"",I:"",N:"和字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "和字号", I: "14,10",X:0,Y:0},Y:Mapfx{F:"",N: "和字号", I: "16,10",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,10":{L:"",F:"",I:"",N:"和字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "和字号", I: "15,10",X:0,Y:0},Y:Mapfx{F:"",N: "人字号", I: "17,10",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,10":{L:"",F:"",I:"",N:"人字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "和字号", I: "16,10",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "人字号", I: "17,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,11":{L:"",F:"",I:"",N:"天字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "天字号", I: "11,10",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "和字号", I: "12,11",X:0,Y:0},X:Mapfx{F:"",N: "天字号", I: "11,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,11":{L:"",F:"",I:"",N:"和字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "天字号", I: "11,11",X:0,Y:0},Y:Mapfx{F:"",N: "和字号", I: "13,11",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,11":{L:"",F:"",I:"",N:"和字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "和字号", I: "12,11",X:0,Y:0},Y:Mapfx{F:"",N: "和字号", I: "14,11",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,11":{L:"",F:"",I:"",N:"和字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "和字号", I: "13,11",X:0,Y:0},Y:Mapfx{F:"",N: "和字号", I: "15,11",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,11":{L:"",F:"",I:"",N:"和字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "和字号", I: "14,11",X:0,Y:0},Y:Mapfx{F:"",N: "和字号", I: "16,11",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,11":{L:"",F:"",I:"",N:"和字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "和字号", I: "15,11",X:0,Y:0},Y:Mapfx{F:"",N: "人字号", I: "17,11",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,11":{L:"",F:"",I:"",N:"人字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "人字号", I: "17,10",X:0,Y:0},Z:Mapfx{F:"",N: "和字号", I: "16,11",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "人字号", I: "17,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,12":{L:"",F:"",I:"",N:"天字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "天字号", I: "11,11",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "天字号", I: "11,13",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,12":{L:"",F:"",I:"",N:"人字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "人字号", I: "17,11",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "人字号", I: "17,13",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,13":{L:"",F:"",I:"",N:"天字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "天字号", I: "11,12",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "天字号", I: "11,14",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,13":{L:"",F:"",I:"",N:"人字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "人字号", I: "17,12",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "人字号", I: "17,14",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,14":{L:"",F:"",I:"",N:"天字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "天字号", I: "11,13",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "天字号", I: "11,15",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,14":{L:"",F:"",I:"",N:"人字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "人字号", I: "17,13",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "人字号", I: "17,15",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,15":{L:"",F:"",I:"",N:"天字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "天字号", I: "11,14",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "天字号", I: "11,16",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,15":{L:"",F:"",I:"",N:"人字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "人字号", I: "17,14",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "人字号", I: "17,16",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,16":{L:"",F:"",I:"",N:"天字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "天字号", I: "11,15",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "天字号", I: "11,17",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,16":{L:"",F:"",I:"",N:"人字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "人字号", I: "17,15",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "人字号", I: "17,17",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,17":{L:"",F:"",I:"",N:"天字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "天字号", I: "11,16",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "天字号", I: "11,18",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,17":{L:"",F:"",I:"",N:"人字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "人字号", I: "17,16",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "人字号", I: "17,18",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,18":{L:"",F:"",I:"",N:"天字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "天字号", I: "11,17",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "地字号", I: "12,18",X:0,Y:0},X:Mapfx{F:"",N: "天字号", I: "11,19",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,18":{L:"",F:"",I:"",N:"地字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "天字号", I: "11,18",X:0,Y:0},Y:Mapfx{F:"",N: "地字号", I: "13,18",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,18":{L:"",F:"",I:"",N:"地字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "地字号", I: "12,18",X:0,Y:0},Y:Mapfx{F:"",N: "地字号", I: "14,18",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,18":{L:"",F:"",I:"",N:"地字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "地字号", I: "13,18",X:0,Y:0},Y:Mapfx{F:"",N: "地字号", I: "15,18",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,18":{L:"",F:"",I:"",N:"地字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "地字号", I: "14,18",X:0,Y:0},Y:Mapfx{F:"",N: "地字号", I: "16,18",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,18":{L:"",F:"",I:"",N:"地字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "地字号", I: "15,18",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,18":{L:"",F:"",I:"",N:"人字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "人字号", I: "17,17",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "人字号", I: "17,19",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,19":{L:"",F:"",I:"",N:"天字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "天字号", I: "11,18",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "地字号", I: "12,19",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,19":{L:"",F:"",I:"",N:"地字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "天字号", I: "11,19",X:0,Y:0},Y:Mapfx{F:"",N: "地字号", I: "13,19",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,19":{L:"",F:"",I:"",N:"地字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "地字号", I: "12,19",X:0,Y:0},Y:Mapfx{F:"",N: "地字号", I: "14,19",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,19":{L:"",F:"",I:"",N:"地字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "地字号", I: "13,19",X:0,Y:0},Y:Mapfx{F:"",N: "地字号", I: "15,19",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,19":{L:"",F:"",I:"",N:"地字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "地字号", I: "14,19",X:0,Y:0},Y:Mapfx{F:"",N: "地字号", I: "16,19",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,19":{L:"",F:"",I:"",N:"地字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "地字号", I: "15,19",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"17,19":{L:"",F:"",I:"",N:"人字号",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "人字号", I: "17,18",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//五七星耀八卦阵
"wuxingqiyao": {
"4,4":{L:"",F:"",I:"",N:"斗",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "牛", I: "5,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,4":{L:"",F:"",I:"",N:"牛",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "斗", I: "4,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "女", I: "5,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,4":{L:"",F:"",I:"",N:"室",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "壁", I: "8,4",X:0,Y:0},X:Mapfx{F:"",N: "危", I: "7,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,4":{L:"",F:"",I:"",N:"壁",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "室", I: "7,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,5":{L:"",F:"",I:"",N:"女",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "牛", I: "5,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "虚", I: "6,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,5":{L:"",F:"",I:"",N:"虚",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "女", I: "5,5",X:0,Y:0},Y:Mapfx{F:"",N: "危", I: "7,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,5":{L:"",F:"",I:"",N:"危",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "室", I: "7,4",X:0,Y:0},Z:Mapfx{F:"",N: "虚", I: "6,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,6":{L:"",F:"",I:"",N:"北宫玄武",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "乾位", I: "6,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"1,7":{L:"",F:"",I:"",N:"奎",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "娄", I: "1,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,7":{L:"",F:"",I:"",N:"乾位",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "北宫玄武", I: "6,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "太阳", I: "6,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,7":{L:"",F:"",I:"",N:"角",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "亢", I: "11,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"1,8":{L:"",F:"",I:"",N:"娄",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "奎", I: "1,7",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "胃", I: "2,8",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"2,8":{L:"",F:"",I:"",N:"胃",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "娄", I: "1,8",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "昂", I: "2,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,8":{L:"",F:"",I:"",N:"兑位",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "太阳", I: "6,8",X:0,Y:0},X:Mapfx{F:"",N: "少阴", I: "5,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,8":{L:"",F:"",I:"",N:"太阳",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "乾位", I: "6,7",X:0,Y:0},Z:Mapfx{F:"",N: "兑位", I: "5,8",X:0,Y:0},Y:Mapfx{F:"",N: "坤位", I: "7,8",X:0,Y:0},X:Mapfx{F:"",N: "阵心", I: "6,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,8":{L:"",F:"",I:"",N:"坤位",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "太阳", I: "6,8",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,8":{L:"",F:"",I:"",N:"氏",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "亢", I: "11,8",X:0,Y:0},X:Mapfx{F:"",N: "房", I: "10,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,8":{L:"",F:"",I:"",N:"亢",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "角", I: "11,7",X:0,Y:0},Z:Mapfx{F:"",N: "氏", I: "10,8",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"2,9":{L:"",F:"",I:"",N:"昂",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "胃", I: "2,8",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "西宫白虎", I: "3,9",X:0,Y:0},X:Mapfx{F:"",N: "毕", I: "2,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,9":{L:"",F:"",I:"",N:"西宫白虎",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "昂", I: "2,9",X:0,Y:0},Y:Mapfx{F:"",N: "离位", I: "4,9",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,9":{L:"",F:"",I:"",N:"离位",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "西宫白虎", I: "3,9",X:0,Y:0},Y:Mapfx{F:"",N: "少阴", I: "5,9",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,9":{L:"",F:"",I:"",N:"少阴",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "兑位", I: "5,8",X:0,Y:0},Z:Mapfx{F:"",N: "离位", I: "4,9",X:0,Y:0},Y:Mapfx{F:"",N: "阵心", I: "6,9",X:0,Y:0},X:Mapfx{F:"",N: "震位", I: "5,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,9":{L:"",F:"",I:"",N:"阵心",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "太阳", I: "6,8",X:0,Y:0},Z:Mapfx{F:"",N: "少阴", I: "5,9",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "太阴", I: "6,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,9":{L:"",F:"",I:"",N:"少阳",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "艮位", I: "8,9",X:0,Y:0},X:Mapfx{F:"",N: "坎位", I: "7,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,9":{L:"",F:"",I:"",N:"艮位",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "少阳", I: "7,9",X:0,Y:0},Y:Mapfx{F:"",N: "东宫苍龙", I: "9,9",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,9":{L:"",F:"",I:"",N:"东宫苍龙",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "艮位", I: "8,9",X:0,Y:0},Y:Mapfx{F:"",N: "房", I: "10,9",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,9":{L:"",F:"",I:"",N:"房",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "氏", I: "10,8",X:0,Y:0},Z:Mapfx{F:"",N: "东宫苍龙", I: "9,9",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "心", I: "10,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"1,10":{L:"",F:"",I:"",N:"觜",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "毕", I: "2,10",X:0,Y:0},X:Mapfx{F:"",N: "参", I: "1,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"2,10":{L:"",F:"",I:"",N:"毕",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "昂", I: "2,9",X:0,Y:0},Z:Mapfx{F:"",N: "觜", I: "1,10",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,10":{L:"",F:"",I:"",N:"震位",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "少阴", I: "5,9",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "太阴", I: "6,10",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,10":{L:"",F:"",I:"",N:"太阴",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "阵心", I: "6,9",X:0,Y:0},Z:Mapfx{F:"",N: "震位", I: "5,10",X:0,Y:0},Y:Mapfx{F:"",N: "坎位", I: "7,10",X:0,Y:0},X:Mapfx{F:"",N: "巽位", I: "6,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,10":{L:"",F:"",I:"",N:"坎位",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "少阳", I: "7,9",X:0,Y:0},Z:Mapfx{F:"",N: "太阴", I: "6,10",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,10":{L:"",F:"",I:"",N:"心",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "房", I: "10,9",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "尾", I: "11,10",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,10":{L:"",F:"",I:"",N:"尾",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "心", I: "10,10",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "箕", I: "11,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"1,11":{L:"",F:"",I:"",N:"参",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "觜", I: "1,10",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,11":{L:"",F:"",I:"",N:"巽位",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "太阴", I: "6,10",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "南宫朱雀", I: "6,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,11":{L:"",F:"",I:"",N:"箕",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "尾", I: "11,10",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,12":{L:"",F:"",I:"",N:"南宫朱雀",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "巽位", I: "6,11",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "星", I: "6,13",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,13":{L:"",F:"",I:"",N:"柳",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "星", I: "6,13",X:0,Y:0},X:Mapfx{F:"",N: "鬼", I: "5,14",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,13":{L:"",F:"",I:"",N:"星",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "南宫朱雀", I: "6,12",X:0,Y:0},Z:Mapfx{F:"",N: "柳", I: "5,13",X:0,Y:0},Y:Mapfx{F:"",N: "张", I: "7,13",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,13":{L:"",F:"",I:"",N:"张",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "星", I: "6,13",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "巽", I: "7,14",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,14":{L:"",F:"",I:"",N:"井",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "鬼", I: "5,14",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,14":{L:"",F:"",I:"",N:"鬼",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "柳", I: "5,13",X:0,Y:0},Z:Mapfx{F:"",N: "井", I: "4,14",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,14":{L:"",F:"",I:"",N:"巽",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "张", I: "7,13",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "轸", I: "8,14",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,14":{L:"",F:"",I:"",N:"轸",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "巽", I: "7,14",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//至尊宝库
"zhizunbaoku": {
"2,3":{L:"",F:"",I:"",N:"至尊宝库",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "至尊宝库", I: "3,3",X:0,Y:0},X:Mapfx{F:"",N: "至尊宝库", I: "2,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,3":{L:"",F:"",I:"",N:"至尊宝库",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "至尊宝库", I: "2,3",X:0,Y:0},Y:Mapfx{F:"",N: "至尊宝库", I: "4,3",X:0,Y:0},X:Mapfx{F:"",N: "至尊宝库", I: "3,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,3":{L:"",F:"",I:"",N:"至尊宝库",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "至尊宝库", I: "3,3",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "至尊宝库", I: "4,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"2,4":{L:"",F:"",I:"",N:"至尊宝库",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "至尊宝库", I: "2,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "至尊宝库", I: "2,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,4":{L:"",F:"",I:"",N:"至尊宝库",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "至尊宝库", I: "3,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "至尊宝库", I: "3,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,4":{L:"",F:"",I:"",N:"至尊宝库",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "至尊宝库", I: "4,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "至尊宝库", I: "4,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"2,5":{L:"",F:"",I:"",N:"至尊宝库",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "至尊宝库", I: "2,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "至尊宝库", I: "3,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,5":{L:"",F:"",I:"",N:"至尊宝库",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "至尊宝库", I: "3,4",X:0,Y:0},Z:Mapfx{F:"",N: "至尊宝库", I: "2,5",X:0,Y:0},Y:Mapfx{F:"",N: "至尊宝库", I: "4,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,5":{L:"",F:"",I:"",N:"至尊宝库",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "至尊宝库", I: "4,4",X:0,Y:0},Z:Mapfx{F:"",N: "至尊宝库", I: "3,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//阮家宝库
"ruanjaibaoku": {
},
//守卫丛林禁地
"conglinjindi": {
"2,3":{L:"",F:"",I:"",N:"丛林禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "丛林禁地", I: "2,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,3":{L:"",F:"",I:"",N:"丛林禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "丛林禁地", I: "4,3",X:0,Y:0},X:Mapfx{F:"",N: "丛林禁地", I: "3,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,3":{L:"",F:"",I:"",N:"丛林禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "丛林禁地", I: "3,3",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"2,4":{L:"",F:"",I:"",N:"丛林禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "丛林禁地", I: "2,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "丛林禁地", I: "3,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,4":{L:"",F:"",I:"",N:"丛林禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "丛林禁地", I: "3,3",X:0,Y:0},Z:Mapfx{F:"",N: "丛林禁地", I: "2,4",X:0,Y:0},Y:Mapfx{F:"",N: "丛林禁地", I: "4,4",X:0,Y:0},X:Mapfx{F:"",N: "丛林禁地", I: "3,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,4":{L:"",F:"",I:"",N:"丛林禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "丛林禁地", I: "3,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "丛林禁地", I: "4,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"2,5":{L:"",F:"",I:"",N:"丛林禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "丛林禁地", I: "3,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,5":{L:"",F:"",I:"",N:"丛林禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "丛林禁地", I: "3,4",X:0,Y:0},Z:Mapfx{F:"",N: "丛林禁地", I: "2,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,5":{L:"",F:"",I:"",N:"丛林禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "丛林禁地", I: "4,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//守卫深海禁地
"shenhaijindi": {
"6,1":{L:"",F:"",I:"",N:"深海禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "深海禁地", I: "7,1",X:0,Y:0},X:Mapfx{F:"",N: "深海禁地", I: "6,2",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,1":{L:"",F:"",I:"",N:"深海禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "深海禁地", I: "6,1",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "深海禁地", I: "7,2",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,2":{L:"",F:"",I:"",N:"深海禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "深海禁地", I: "6,2",X:0,Y:0},X:Mapfx{F:"",N: "深海禁地", I: "5,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,2":{L:"",F:"",I:"",N:"深海禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "深海禁地", I: "6,1",X:0,Y:0},Z:Mapfx{F:"",N: "深海禁地", I: "5,2",X:0,Y:0},Y:Mapfx{F:"",N: "深海禁地", I: "7,2",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,2":{L:"",F:"",I:"",N:"深海禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "深海禁地", I: "7,1",X:0,Y:0},Z:Mapfx{F:"",N: "深海禁地", I: "6,2",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,3":{L:"",F:"",I:"",N:"深海禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "深海禁地", I: "5,3",X:0,Y:0},X:Mapfx{F:"",N: "深海禁地", I: "4,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,3":{L:"",F:"",I:"",N:"深海禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "深海禁地", I: "5,2",X:0,Y:0},Z:Mapfx{F:"",N: "深海禁地", I: "4,3",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,4":{L:"",F:"",I:"",N:"深海禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "深海禁地", I: "4,4",X:0,Y:0},X:Mapfx{F:"",N: "深海禁地", I: "3,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,4":{L:"",F:"",I:"",N:"深海禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "深海禁地", I: "4,3",X:0,Y:0},Z:Mapfx{F:"",N: "深海禁地", I: "3,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"2,5":{L:"",F:"",I:"",N:"深海禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "深海禁地", I: "3,5",X:0,Y:0},X:Mapfx{F:"",N: "深海禁地", I: "2,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,5":{L:"",F:"",I:"",N:"深海禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "深海禁地", I: "3,4",X:0,Y:0},Z:Mapfx{F:"",N: "深海禁地", I: "2,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "深海禁地", I: "3,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"2,6":{L:"",F:"",I:"",N:"深海禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "深海禁地", I: "2,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "深海禁地", I: "3,6",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,6":{L:"",F:"",I:"",N:"深海禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "深海禁地", I: "3,5",X:0,Y:0},Z:Mapfx{F:"",N: "深海禁地", I: "2,6",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//守护边界禁地
"bianjiejindi": {
"1,4":{L:"",F:"",I:"",N:"边界禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "边界禁地", I: "2,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"2,4":{L:"",F:"",I:"",N:"边界禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "边界禁地", I: "1,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "边界禁地", I: "2,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,4":{L:"",F:"",I:"",N:"边界禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "边界禁地", I: "6,4",X:0,Y:0},X:Mapfx{F:"",N: "边界禁地", I: "5,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,4":{L:"",F:"",I:"",N:"边界禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "边界禁地", I: "5,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"2,5":{L:"",F:"",I:"",N:"边界禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "边界禁地", I: "2,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "边界禁地", I: "3,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,5":{L:"",F:"",I:"",N:"边界禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "边界禁地", I: "2,5",X:0,Y:0},Y:Mapfx{F:"",N: "边界禁地", I: "4,5",X:0,Y:0},X:Mapfx{F:"",N: "边界禁地", I: "3,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,5":{L:"",F:"",I:"",N:"边界禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "边界禁地", I: "3,5",X:0,Y:0},Y:Mapfx{F:"",N: "边界禁地", I: "5,5",X:0,Y:0},X:Mapfx{F:"",N: "边界禁地", I: "4,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,5":{L:"",F:"",I:"",N:"边界禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "边界禁地", I: "5,4",X:0,Y:0},Z:Mapfx{F:"",N: "边界禁地", I: "4,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,6":{L:"",F:"",I:"",N:"边界禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "边界禁地", I: "3,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "边界禁地", I: "4,6",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,6":{L:"",F:"",I:"",N:"边界禁地",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "边界禁地", I: "4,5",X:0,Y:0},Z:Mapfx{F:"",N: "边界禁地", I: "3,6",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//水月镜花
"shuiyueqinghua": {
"6,9":{L:"",F:"",I:"",N:"华南水口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "丰善水口", I: "7,9",X:0,Y:0},X:Mapfx{F:"",N: "源煌月口", I: "6,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,9":{L:"",F:"",I:"",N:"丰善水口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "华南水口", I: "6,9",X:0,Y:0},Y:Mapfx{F:"",N: "祥江水口", I: "8,9",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,9":{L:"",F:"",I:"",N:"祥江水口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "丰善水口", I: "7,9",X:0,Y:0},Y:Mapfx{F:"",N: "山安水口", I: "9,9",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,9":{L:"",F:"",I:"",N:"山安水口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "祥江水口", I: "8,9",X:0,Y:0},Y:Mapfx{F:"",N: "天赣水口", I: "10,9",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,9":{L:"",F:"",I:"",N:"天赣水口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "山安水口", I: "9,9",X:0,Y:0},Y:Mapfx{F:"",N: "宁金水口", I: "11,9",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,9":{L:"",F:"",I:"",N:"宁金水口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "天赣水口", I: "10,9",X:0,Y:0},Y:Mapfx{F:"",N: "安江水口", I: "12,9",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,9":{L:"",F:"",I:"",N:"安江水口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "宁金水口", I: "11,9",X:0,Y:0},Y:Mapfx{F:"",N: "坪洲水口", I: "13,9",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,9":{L:"",F:"",I:"",N:"坪洲水口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "安江水口", I: "12,9",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,10":{L:"",F:"",I:"",N:"源煌月口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "华南水口", I: "6,9",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "政城月口", I: "7,10",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,10":{L:"",F:"",I:"",N:"政城月口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "源煌月口", I: "6,10",X:0,Y:0},Y:Mapfx{F:"",N: "高许月口", I: "8,10",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,10":{L:"",F:"",I:"",N:"高许月口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "政城月口", I: "7,10",X:0,Y:0},Y:Mapfx{F:"",N: "敦汾月口", I: "9,10",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,10":{L:"",F:"",I:"",N:"敦汾月口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "高许月口", I: "8,10",X:0,Y:0},Y:Mapfx{F:"",N: "安上月口", I: "10,10",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,10":{L:"",F:"",I:"",N:"安上月口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "敦汾月口", I: "9,10",X:0,Y:0},Y:Mapfx{F:"",N: "内连月口", I: "11,10",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,10":{L:"",F:"",I:"",N:"内连月口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "安上月口", I: "10,10",X:0,Y:0},Y:Mapfx{F:"",N: "长川月口", I: "12,10",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,10":{L:"",F:"",I:"",N:"长川月口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "内连月口", I: "11,10",X:0,Y:0},Y:Mapfx{F:"",N: "汤武月口", I: "13,10",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,10":{L:"",F:"",I:"",N:"汤武月口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "长川月口", I: "12,10",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "新宁镜口", I: "13,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,11":{L:"",F:"",I:"",N:"化嵩镜口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "库阴镜口", I: "7,11",X:0,Y:0},X:Mapfx{F:"",N: "同馨花口", I: "6,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,11":{L:"",F:"",I:"",N:"库阴镜口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "化嵩镜口", I: "6,11",X:0,Y:0},Y:Mapfx{F:"",N: "昔阳镜口", I: "8,11",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,11":{L:"",F:"",I:"",N:"昔阳镜口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "库阴镜口", I: "7,11",X:0,Y:0},Y:Mapfx{F:"",N: "泰羽镜口", I: "9,11",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,11":{L:"",F:"",I:"",N:"泰羽镜口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "昔阳镜口", I: "8,11",X:0,Y:0},Y:Mapfx{F:"",N: "谷清镜口", I: "10,11",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,11":{L:"",F:"",I:"",N:"谷清镜口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "泰羽镜口", I: "9,11",X:0,Y:0},Y:Mapfx{F:"",N: "南海镜口", I: "11,11",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,11":{L:"",F:"",I:"",N:"南海镜口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "谷清镜口", I: "10,11",X:0,Y:0},Y:Mapfx{F:"",N: "凤鸣镜口", I: "12,11",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,11":{L:"",F:"",I:"",N:"凤鸣镜口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "南海镜口", I: "11,11",X:0,Y:0},Y:Mapfx{F:"",N: "新宁镜口", I: "13,11",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,11":{L:"",F:"",I:"",N:"新宁镜口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "汤武月口", I: "13,10",X:0,Y:0},Z:Mapfx{F:"",N: "凤鸣镜口", I: "12,11",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,12":{L:"",F:"",I:"",N:"同馨花口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "化嵩镜口", I: "6,11",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "合欢花口", I: "7,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,12":{L:"",F:"",I:"",N:"合欢花口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "同馨花口", I: "6,12",X:0,Y:0},Y:Mapfx{F:"",N: "彼岸花口", I: "8,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,12":{L:"",F:"",I:"",N:"彼岸花口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "合欢花口", I: "7,12",X:0,Y:0},Y:Mapfx{F:"",N: "芍药花口", I: "9,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,12":{L:"",F:"",I:"",N:"芍药花口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "彼岸花口", I: "8,12",X:0,Y:0},Y:Mapfx{F:"",N: "南铁花口", I: "10,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,12":{L:"",F:"",I:"",N:"南铁花口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "芍药花口", I: "9,12",X:0,Y:0},Y:Mapfx{F:"",N: "襄元花口", I: "11,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,12":{L:"",F:"",I:"",N:"襄元花口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "南铁花口", I: "10,12",X:0,Y:0},Y:Mapfx{F:"",N: "云裳花口", I: "12,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,12":{L:"",F:"",I:"",N:"云裳花口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "襄元花口", I: "11,12",X:0,Y:0},Y:Mapfx{F:"",N: "凌霄花口", I: "13,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,12":{L:"",F:"",I:"",N:"凌霄花口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "云裳花口", I: "12,12",X:0,Y:0},Y:Mapfx{F:"",N: "水月镜花", I: "14,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,12":{L:"",F:"",I:"",N:"水月镜花",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "凌霄花口", I: "13,12",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,18":{L:"",F:"",I:"",N:"水月镜花",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//镜花水月
"jinghuahsuiyue": {
"5,1":{L:"",F:"",I:"",N:"镇弯水",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "通天水", I: "6,1",X:0,Y:0},X:Mapfx{F:"",N: "紫薇花", I: "5,2",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,1":{L:"",F:"",I:"",N:"通天水",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "镇弯水", I: "5,1",X:0,Y:0},Y:Mapfx{F:"",N: "虎跑水", I: "7,1",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,1":{L:"",F:"",I:"",N:"虎跑水",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "通天水", I: "6,1",X:0,Y:0},Y:Mapfx{F:"",N: "言武水", I: "8,1",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,1":{L:"",F:"",I:"",N:"言武水",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "虎跑水", I: "7,1",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "川渝水", I: "8,2",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,2":{L:"",F:"",I:"",N:"紫薇花",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "镇弯水", I: "5,1",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "曼陀花", I: "5,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,2":{L:"",F:"",I:"",N:"川渝水",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "言武水", I: "8,1",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "盘龙水", I: "8,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,2":{L:"",F:"",I:"",N:"周朔月",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "定华月", I: "13,2",X:0,Y:0},X:Mapfx{F:"",N: "新武月", I: "12,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,2":{L:"",F:"",I:"",N:"定华月",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "周朔月", I: "12,2",X:0,Y:0},Y:Mapfx{F:"",N: "百川月", I: "14,2",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,2":{L:"",F:"",I:"",N:"百川月",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "定华月", I: "13,2",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "狮天月", I: "14,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,3":{L:"",F:"",I:"",N:"曼陀花",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "紫薇花", I: "5,2",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "大蝶花", I: "5,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,3":{L:"",F:"",I:"",N:"盘龙水",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "川渝水", I: "8,2",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "潇湘水", I: "8,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,3":{L:"",F:"",I:"",N:"礼徽镜",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "兰庄镜", I: "11,3",X:0,Y:0},X:Mapfx{F:"",N: "常瑞镜", I: "10,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,3":{L:"",F:"",I:"",N:"兰庄镜",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "礼徽镜", I: "10,3",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "泰清镜", I: "11,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,3":{L:"",F:"",I:"",N:"新武月",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "周朔月", I: "12,2",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "寒都镜", I: "12,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,3":{L:"",F:"",I:"",N:"狮天月",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "百川月", I: "14,2",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "太一境", I: "14,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,4":{L:"",F:"",I:"",N:"大蝶花",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "曼陀花", I: "5,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "玉簪花", I: "5,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,4":{L:"",F:"",I:"",N:"潇湘水",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "盘龙水", I: "8,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "宁屏镜", I: "9,4",X:0,Y:0},X:Mapfx{F:"",N: "夏甲水", I: "8,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,4":{L:"",F:"",I:"",N:"宁屏镜",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "潇湘水", I: "8,4",X:0,Y:0},Y:Mapfx{F:"",N: "常瑞镜", I: "10,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,4":{L:"",F:"",I:"",N:"常瑞镜",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "礼徽镜", I: "10,3",X:0,Y:0},Z:Mapfx{F:"",N: "宁屏镜", I: "9,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "山光镜", I: "10,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,4":{L:"",F:"",I:"",N:"泰清镜",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "兰庄镜", I: "11,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "寒都镜", I: "12,4",X:0,Y:0},X:Mapfx{F:"",N: "龙竹镜", I: "11,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,4":{L:"",F:"",I:"",N:"寒都镜",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "新武月", I: "12,3",X:0,Y:0},Z:Mapfx{F:"",N: "泰清镜", I: "11,4",X:0,Y:0},Y:Mapfx{F:"",N: "定阳镜", I: "13,4",X:0,Y:0},X:Mapfx{F:"",N: "曲溪月", I: "12,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,4":{L:"",F:"",I:"",N:"定阳镜",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "寒都镜", I: "12,4",X:0,Y:0},Y:Mapfx{F:"",N: "太一境", I: "14,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,4":{L:"",F:"",I:"",N:"太一境",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "狮天月", I: "14,3",X:0,Y:0},Z:Mapfx{F:"",N: "定阳镜", I: "13,4",X:0,Y:0},Y:Mapfx{F:"",N: "万庆境", I: "15,4",X:0,Y:0},X:Mapfx{F:"",N: "新武月", I: "14,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,4":{L:"",F:"",I:"",N:"万庆境",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "太一境", I: "14,4",X:0,Y:0},Y:Mapfx{F:"",N: "镜花水月", I: "16,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"16,4":{L:"",F:"",I:"",N:"镜花水月",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "万庆境", I: "15,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,5":{L:"",F:"",I:"",N:"玉簪花",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "大蝶花", I: "5,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "丰谷花", I: "6,5",X:0,Y:0},X:Mapfx{F:"",N: "冬青花", I: "5,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,5":{L:"",F:"",I:"",N:"丰谷花",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "玉簪花", I: "5,5",X:0,Y:0},Y:Mapfx{F:"",N: "舒伶花", I: "7,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,5":{L:"",F:"",I:"",N:"舒伶花",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "丰谷花", I: "6,5",X:0,Y:0},Y:Mapfx{F:"",N: "夏甲水", I: "8,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,5":{L:"",F:"",I:"",N:"夏甲水",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "潇湘水", I: "8,4",X:0,Y:0},Z:Mapfx{F:"",N: "舒伶花", I: "7,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "乌方水", I: "8,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,5":{L:"",F:"",I:"",N:"山光镜",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "常瑞镜", I: "10,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "龙竹镜", I: "11,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,5":{L:"",F:"",I:"",N:"龙竹镜",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "泰清镜", I: "11,4",X:0,Y:0},Z:Mapfx{F:"",N: "山光镜", I: "10,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,5":{L:"",F:"",I:"",N:"曲溪月",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "寒都镜", I: "12,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "文露月", I: "12,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,5":{L:"",F:"",I:"",N:"新武月",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "太一境", I: "14,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "凝空月", I: "14,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,6":{L:"",F:"",I:"",N:"冬青花",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "玉簪花", I: "5,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "栀子花", I: "5,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,6":{L:"",F:"",I:"",N:"乌方水",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "夏甲水", I: "8,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "冰封水", I: "8,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,6":{L:"",F:"",I:"",N:"文露月",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "曲溪月", I: "12,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "至极月", I: "13,6",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,6":{L:"",F:"",I:"",N:"至极月",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "文露月", I: "12,6",X:0,Y:0},Y:Mapfx{F:"",N: "凝空月", I: "14,6",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,6":{L:"",F:"",I:"",N:"凝空月",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "新武月", I: "14,5",X:0,Y:0},Z:Mapfx{F:"",N: "至极月", I: "13,6",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,7":{L:"",F:"",I:"",N:"栀子花",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "冬青花", I: "5,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "海棠花", I: "6,7",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,7":{L:"",F:"",I:"",N:"海棠花",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "栀子花", I: "5,7",X:0,Y:0},Y:Mapfx{F:"",N: "夜熏花", I: "7,7",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,7":{L:"",F:"",I:"",N:"夜熏花",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "海棠花", I: "6,7",X:0,Y:0},Y:Mapfx{F:"",N: "冰封水", I: "8,7",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,7":{L:"",F:"",I:"",N:"冰封水",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "乌方水", I: "8,6",X:0,Y:0},Z:Mapfx{F:"",N: "夜熏花", I: "7,7",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//阮家水阵
"ruanjiashuizhen": {
"5,2":{L:"",F:"",I:"",N:"水阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "水阵深渊", I: "6,2",X:0,Y:0},X:Mapfx{F:"",N: "水阵中心", I: "5,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,2":{L:"",F:"",I:"",N:"水阵深渊",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "水阵", I: "5,2",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "水阵", I: "6,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,3":{L:"",F:"",I:"",N:"水阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "水阵中心", I: "5,3",X:0,Y:0},X:Mapfx{F:"",N: "水阵", I: "4,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,3":{L:"",F:"",I:"",N:"水阵中心",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "水阵", I: "5,2",X:0,Y:0},Z:Mapfx{F:"",N: "水阵", I: "4,3",X:0,Y:0},Y:Mapfx{F:"",N: "水阵", I: "6,3",X:0,Y:0},X:Mapfx{F:"",N: "水阵", I: "5,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,3":{L:"",F:"",I:"",N:"水阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "水阵深渊", I: "6,2",X:0,Y:0},Z:Mapfx{F:"",N: "水阵中心", I: "5,3",X:0,Y:0},Y:Mapfx{F:"",N: "水阵", I: "7,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,3":{L:"",F:"",I:"",N:"水阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "水阵", I: "6,3",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,4":{L:"",F:"",I:"",N:"水阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "水阵", I: "4,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "水阵", I: "5,4",X:0,Y:0},X:Mapfx{F:"",N: "水阵", I: "4,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,4":{L:"",F:"",I:"",N:"水阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "水阵中心", I: "5,3",X:0,Y:0},Z:Mapfx{F:"",N: "水阵", I: "4,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "水阵", I: "5,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,5":{L:"",F:"",I:"",N:"水阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "水阵", I: "4,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "水阵", I: "5,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,5":{L:"",F:"",I:"",N:"水阵",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "水阵", I: "5,4",X:0,Y:0},Z:Mapfx{F:"",N: "水阵", I: "4,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//天王路
"tainwanglu": {
},
//梁山三沟
"liangshansangou": {
"7,2":{L:"",F:"",I:"",N:"霸王沟北",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "霸王沟", I: "7,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,2":{L:"",F:"",I:"",N:"马沟北",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "马沟", I: "10,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,2":{L:"",F:"",I:"",N:"于沟北",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "于沟", I: "13,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,3":{L:"",F:"",I:"",N:"霸王沟西",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "霸王沟", I: "7,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,3":{L:"",F:"",I:"",N:"霸王沟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "霸王沟北", I: "7,2",X:0,Y:0},Z:Mapfx{F:"",N: "霸王沟西", I: "6,3",X:0,Y:0},Y:Mapfx{F:"",N: "霸王沟东", I: "8,3",X:0,Y:0},X:Mapfx{F:"",N: "霸王沟南", I: "7,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,3":{L:"",F:"",I:"",N:"霸王沟东",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "霸王沟", I: "7,3",X:0,Y:0},Y:Mapfx{F:"",N: "马沟西", I: "9,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,3":{L:"",F:"",I:"",N:"马沟西",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "霸王沟东", I: "8,3",X:0,Y:0},Y:Mapfx{F:"",N: "马沟", I: "10,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,3":{L:"",F:"",I:"",N:"马沟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "马沟北", I: "10,2",X:0,Y:0},Z:Mapfx{F:"",N: "马沟西", I: "9,3",X:0,Y:0},Y:Mapfx{F:"",N: "马沟东", I: "11,3",X:0,Y:0},X:Mapfx{F:"",N: "马沟南", I: "10,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"11,3":{L:"",F:"",I:"",N:"马沟东",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "马沟", I: "10,3",X:0,Y:0},Y:Mapfx{F:"",N: "于沟西", I: "12,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"12,3":{L:"",F:"",I:"",N:"于沟西",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "马沟东", I: "11,3",X:0,Y:0},Y:Mapfx{F:"",N: "于沟", I: "13,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,3":{L:"",F:"",I:"",N:"于沟",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "于沟北", I: "13,2",X:0,Y:0},Z:Mapfx{F:"",N: "于沟西", I: "12,3",X:0,Y:0},Y:Mapfx{F:"",N: "于沟东", I: "14,3",X:0,Y:0},X:Mapfx{F:"",N: "于沟南", I: "13,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"14,3":{L:"",F:"",I:"",N:"于沟东",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "于沟", I: "13,3",X:0,Y:0},Y:Mapfx{F:"",N: "王沟北", I: "15,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,3":{L:"",F:"",I:"",N:"王沟北",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "于沟东", I: "14,3",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "王沟南", I: "15,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,4":{L:"",F:"",I:"",N:"霸王沟南",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "霸王沟", I: "7,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,4":{L:"",F:"",I:"",N:"马沟南",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "马沟", I: "10,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"13,4":{L:"",F:"",I:"",N:"于沟南",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "于沟", I: "13,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"15,4":{L:"",F:"",I:"",N:"王沟南",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "王沟北", I: "15,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//梁山山庙
"liangshanshanmiao": {
"4,2":{L:"",F:"",I:"",N:"朱庙九层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "朱庙八层", I: "4,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,3":{L:"",F:"",I:"",N:"朱庙八层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "朱庙九层", I: "4,2",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "朱庙七层", I: "4,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,4":{L:"",F:"",I:"",N:"朱庙七层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "朱庙八层", I: "4,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "朱庙六层", I: "4,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,5":{L:"",F:"",I:"",N:"朱庙六层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "朱庙七层", I: "4,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "朱庙五层", I: "4,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,6":{L:"",F:"",I:"",N:"朱庙五层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "朱庙六层", I: "4,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "朱庙四层", I: "4,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,7":{L:"",F:"",I:"",N:"朱庙四层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "朱庙五层", I: "4,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "朱庙三层", I: "4,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,8":{L:"",F:"",I:"",N:"朱庙三层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "朱庙四层", I: "4,7",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "朱庙二层", I: "4,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,9":{L:"",F:"",I:"",N:"朱庙二层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "朱庙三层", I: "4,8",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "朱庙一层", I: "4,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,10":{L:"",F:"",I:"",N:"朱庙一层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "朱庙二层", I: "4,9",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "红庙后院", I: "4,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,11":{L:"",F:"",I:"",N:"双庙左",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "红庙后院", I: "4,11",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,11":{L:"",F:"",I:"",N:"红庙后院",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "朱庙一层", I: "4,10",X:0,Y:0},Z:Mapfx{F:"",N: "双庙左", I: "3,11",X:0,Y:0},Y:Mapfx{F:"",N: "双庙右", I: "5,11",X:0,Y:0},X:Mapfx{F:"",N: "红庙大堂", I: "4,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,11":{L:"",F:"",I:"",N:"双庙右",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "红庙后院", I: "4,11",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,12":{L:"",F:"",I:"",N:"红庙大堂",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "红庙后院", I: "4,11",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//通天塔
"tongtianta": {
"4,0":{L:"",F:"",I:"",N:"通天塔25层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔24层", I: "4,1",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,1":{L:"",F:"",I:"",N:"通天塔24层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔25层", I: "4,0",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔23层", I: "4,2",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,2":{L:"",F:"",I:"",N:"通天塔23层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔24层", I: "4,1",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔22层", I: "4,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,3":{L:"",F:"",I:"",N:"通天塔22层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔23层", I: "4,2",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔21层", I: "4,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,4":{L:"",F:"",I:"",N:"通天塔21层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔22层", I: "4,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔20层", I: "4,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,5":{L:"",F:"",I:"",N:"通天塔20层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔21层", I: "4,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔19层", I: "4,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,6":{L:"",F:"",I:"",N:"通天塔19层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔20层", I: "4,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔18层", I: "4,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,7":{L:"",F:"",I:"",N:"通天塔18层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔19层", I: "4,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔17层", I: "4,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,8":{L:"",F:"",I:"",N:"通天塔17层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔18层", I: "4,7",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔16层", I: "4,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,9":{L:"",F:"",I:"",N:"通天塔16层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔17层", I: "4,8",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔15层", I: "4,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,10":{L:"",F:"",I:"",N:"通天塔15层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔16层", I: "4,9",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔14层", I: "4,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,11":{L:"",F:"",I:"",N:"通天塔14层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔15层", I: "4,10",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔13层", I: "4,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,12":{L:"",F:"",I:"",N:"通天塔13层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔14层", I: "4,11",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔12层", I: "4,13",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,13":{L:"",F:"",I:"",N:"通天塔12层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔13层", I: "4,12",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔11层", I: "4,14",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,14":{L:"",F:"",I:"",N:"通天塔11层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔12层", I: "4,13",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔10层", I: "4,15",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,15":{L:"",F:"",I:"",N:"通天塔10层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔11层", I: "4,14",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔9层", I: "4,16",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,16":{L:"",F:"",I:"",N:"通天塔9层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔10层", I: "4,15",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔8层", I: "4,17",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,17":{L:"",F:"",I:"",N:"通天塔8层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔9层", I: "4,16",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔7层", I: "4,18",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,18":{L:"",F:"",I:"",N:"通天塔7层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔8层", I: "4,17",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔6层", I: "4,19",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,19":{L:"",F:"",I:"",N:"通天塔6层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔7层", I: "4,18",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔5层", I: "4,20",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,20":{L:"",F:"",I:"",N:"通天塔5层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔6层", I: "4,19",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔4层", I: "4,21",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,21":{L:"",F:"",I:"",N:"通天塔4层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔5层", I: "4,20",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔3层", I: "4,22",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,22":{L:"",F:"",I:"",N:"通天塔3层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔4层", I: "4,21",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔2层", I: "4,23",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,23":{L:"",F:"",I:"",N:"通天塔2层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔3层", I: "4,22",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "通天塔1层", I: "4,24",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,24":{L:"",F:"",I:"",N:"通天塔1层",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "通天塔2层", I: "4,23",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//贤才馆
"xiancaiguan": {
"6,1":{L:"",F:"",I:"",N:"西北馆",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "左天馆", I: "7,1",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,1":{L:"",F:"",I:"",N:"左天馆",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "西北馆", I: "6,1",X:0,Y:0},Y:Mapfx{F:"",N: "天馆", I: "8,1",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,1":{L:"",F:"",I:"",N:"天馆",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "左天馆", I: "7,1",X:0,Y:0},Y:Mapfx{F:"",N: "右天馆", I: "9,1",X:0,Y:0},X:Mapfx{F:"",N: "北馆", I: "8,2",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,1":{L:"",F:"",I:"",N:"右天馆",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "天馆", I: "8,1",X:0,Y:0},Y:Mapfx{F:"",N: "东北馆", I: "10,1",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,1":{L:"",F:"",I:"",N:"东北馆",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "右天馆", I: "9,1",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,2":{L:"",F:"",I:"",N:"北馆",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "天馆", I: "8,1",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "中心贤才馆", I: "8,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,3":{L:"",F:"",I:"",N:"西馆",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "中心贤才馆", I: "8,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,3":{L:"",F:"",I:"",N:"中心贤才馆",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "北馆", I: "8,2",X:0,Y:0},Z:Mapfx{F:"",N: "西馆", I: "7,3",X:0,Y:0},Y:Mapfx{F:"",N: "东馆", I: "9,3",X:0,Y:0},X:Mapfx{F:"",N: "南馆", I: "8,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,3":{L:"",F:"",I:"",N:"东馆",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "中心贤才馆", I: "8,3",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,4":{L:"",F:"",I:"",N:"南馆",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "中心贤才馆", I: "8,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "地馆", I: "8,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,5":{L:"",F:"",I:"",N:"西南馆",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "左地馆", I: "7,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,5":{L:"",F:"",I:"",N:"左地馆",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "西南馆", I: "6,5",X:0,Y:0},Y:Mapfx{F:"",N: "地馆", I: "8,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,5":{L:"",F:"",I:"",N:"地馆",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "南馆", I: "8,4",X:0,Y:0},Z:Mapfx{F:"",N: "左地馆", I: "7,5",X:0,Y:0},Y:Mapfx{F:"",N: "右地馆", I: "9,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,5":{L:"",F:"",I:"",N:"右地馆",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "地馆", I: "8,5",X:0,Y:0},Y:Mapfx{F:"",N: "东南馆", I: "10,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"10,5":{L:"",F:"",I:"",N:"东南馆",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "右地馆", I: "9,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//六艺塔
"liuyita": {
"4,0":{L:"",F:"",I:"",N:"六艺房",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "阁楼", I: "4,1",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,1":{L:"",F:"",I:"",N:"阁楼",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "六艺房", I: "4,0",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "三层走廊", I: "4,2",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,2":{L:"",F:"",I:"",N:"射艺房",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "三层走廊", I: "4,2",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,2":{L:"",F:"",I:"",N:"三层走廊",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "阁楼", I: "4,1",X:0,Y:0},Z:Mapfx{F:"",N: "射艺房", I: "3,2",X:0,Y:0},Y:Mapfx{F:"",N: "御艺房", I: "5,2",X:0,Y:0},X:Mapfx{F:"",N: "三层走廊", I: "4,3",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,2":{L:"",F:"",I:"",N:"御艺房",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "三层走廊", I: "4,2",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,3":{L:"",F:"",I:"",N:"三层走廊",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "三层走廊", I: "4,2",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "二层走廊", I: "4,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,4":{L:"",F:"",I:"",N:"书艺房",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "二层走廊", I: "4,4",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,4":{L:"",F:"",I:"",N:"二层走廊",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "三层走廊", I: "4,3",X:0,Y:0},Z:Mapfx{F:"",N: "书艺房", I: "3,4",X:0,Y:0},Y:Mapfx{F:"",N: "数艺房", I: "5,4",X:0,Y:0},X:Mapfx{F:"",N: "二层走廊", I: "4,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,4":{L:"",F:"",I:"",N:"数艺房",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "二层走廊", I: "4,4",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,5":{L:"",F:"",I:"",N:"二层走廊",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "二层走廊", I: "4,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "一层走廊", I: "4,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,6":{L:"",F:"",I:"",N:"礼艺房",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "一层走廊", I: "4,6",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,6":{L:"",F:"",I:"",N:"一层走廊",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "二层走廊", I: "4,5",X:0,Y:0},Z:Mapfx{F:"",N: "礼艺房", I: "3,6",X:0,Y:0},Y:Mapfx{F:"",N: "乐艺房", I: "5,6",X:0,Y:0},X:Mapfx{F:"",N: "一层走廊", I: "4,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,6":{L:"",F:"",I:"",N:"乐艺房",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "一层走廊", I: "4,6",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,7":{L:"",F:"",I:"",N:"一层走廊",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "一层走廊", I: "4,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//幽暗池
"youanchi": {
"7,5":{L:"",F:"",I:"",N:"白光入口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "湿碌小路", I: "7,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,6":{L:"",F:"",I:"",N:"幽暗草丛",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "湿碌小路", I: "7,6",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,6":{L:"",F:"",I:"",N:"湿碌小路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "白光入口", I: "7,5",X:0,Y:0},Z:Mapfx{F:"",N: "幽暗草丛", I: "6,6",X:0,Y:0},Y:Mapfx{F:"",N: "幽暗草丛", I: "8,6",X:0,Y:0},X:Mapfx{F:"",N: "幽暗冰路", I: "7,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,6":{L:"",F:"",I:"",N:"幽暗草丛",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "湿碌小路", I: "7,6",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,7":{L:"",F:"",I:"",N:"幽暗冰路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "湿碌小路", I: "7,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "幽暗冰路", I: "7,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,8":{L:"",F:"",I:"",N:"幽暗冰路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "幽暗冰路", I: "7,7",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "幽暗白雪路", I: "7,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,9":{L:"",F:"",I:"",N:"幽暗白雪路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "幽暗冰路", I: "7,8",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "幽暗白雪路", I: "7,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,10":{L:"",F:"",I:"",N:"幽暗白雪路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "幽暗白雪路", I: "7,9",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "巨型幽黑大门", I: "7,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,11":{L:"",F:"",I:"",N:"巨型幽黑大门",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "幽暗白雪路", I: "7,10",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "幽暗大殿", I: "7,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,12":{L:"",F:"",I:"",N:"幽暗西殿",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "幽暗大殿", I: "7,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,12":{L:"",F:"",I:"",N:"幽暗大殿",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "巨型幽黑大门", I: "7,11",X:0,Y:0},Z:Mapfx{F:"",N: "幽暗西殿", I: "6,12",X:0,Y:0},Y:Mapfx{F:"",N: "幽暗东殿", I: "8,12",X:0,Y:0},X:Mapfx{F:"",N: "幽暗密道", I: "7,13",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,12":{L:"",F:"",I:"",N:"幽暗东殿",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "幽暗大殿", I: "7,12",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,13":{L:"",F:"",I:"",N:"幽暗密道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "幽暗大殿", I: "7,12",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "幽暗密道", I: "7,14",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,14":{L:"",F:"",I:"",N:"幽暗密道",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "幽暗密道", I: "7,13",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "灵兽堂口", I: "7,15",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,15":{L:"",F:"",I:"",N:"灵兽堂口",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "幽暗密道", I: "7,14",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "灵兽堂", I: "7,16",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,16":{L:"",F:"",I:"",N:"灵兽堂",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "灵兽堂口", I: "7,15",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//火岩洞
"huoyandong": {
"3,3":{L:"",F:"",I:"",N:"灰土路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "灰土路", I: "4,3",X:0,Y:0},X:Mapfx{F:"",N: "岔路", I: "3,4",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,3":{L:"",F:"",I:"",N:"灰土路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "灰土路", I: "3,3",X:0,Y:0},Y:Mapfx{F:"",N: "灰土路", I: "5,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,3":{L:"",F:"",I:"",N:"灰土路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "灰土路", I: "4,3",X:0,Y:0},Y:Mapfx{F:"",N: "灰土洞", I: "6,3",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,3":{L:"",F:"",I:"",N:"灰土洞",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "灰土路", I: "5,3",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,4":{L:"",F:"",I:"",N:"岔路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "灰土路", I: "3,3",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "灰土路", I: "3,5",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"3,5":{L:"",F:"",I:"",N:"灰土路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "岔路", I: "3,4",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "灰土路", I: "4,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"4,5":{L:"",F:"",I:"",N:"灰土路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "灰土路", I: "3,5",X:0,Y:0},Y:Mapfx{F:"",N: "灰土路", I: "5,5",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,5":{L:"",F:"",I:"",N:"灰土路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "灰土路", I: "4,5",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "灰土路", I: "5,6",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,6":{L:"",F:"",I:"",N:"灰土路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "灰土路", I: "5,5",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "红岩路", I: "6,6",X:0,Y:0},X:Mapfx{F:"",N: "红岩池", I: "5,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,6":{L:"",F:"",I:"",N:"红岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "灰土路", I: "5,6",X:0,Y:0},Y:Mapfx{F:"",N: "红岩路", I: "7,6",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,6":{L:"",F:"",I:"",N:"红岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "红岩路", I: "6,6",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "红岩路", I: "7,7",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"5,7":{L:"",F:"",I:"",N:"红岩池",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "灰土路", I: "5,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,7":{L:"",F:"",I:"",N:"红岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "红岩路", I: "7,6",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "红石小屋", I: "8,7",X:0,Y:0},X:Mapfx{F:"",N: "红岩路", I: "7,8",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,7":{L:"",F:"",I:"",N:"红石小屋",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "红岩路", I: "7,7",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,8":{L:"",F:"",I:"",N:"红岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "红岩路", I: "7,7",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "红岩路", I: "7,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,8":{L:"",F:"",I:"",N:"岩浆河",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "岩浆河", I: "9,8",X:0,Y:0},X:Mapfx{F:"",N: "岩石桥", I: "8,9",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,8":{L:"",F:"",I:"",N:"岩浆河",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "岩浆河", I: "8,8",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,9":{L:"",F:"",I:"",N:"红岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "红岩路", I: "7,8",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "岩石桥", I: "8,9",X:0,Y:0},X:Mapfx{F:"",N: "蓝岩池", I: "7,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,9":{L:"",F:"",I:"",N:"岩石桥",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "岩浆河", I: "8,8",X:0,Y:0},Z:Mapfx{F:"",N: "红岩路", I: "7,9",X:0,Y:0},Y:Mapfx{F:"",N: "蓝岩路", I: "9,9",X:0,Y:0},X:Mapfx{F:"",N: "岩浆河", I: "8,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,9":{L:"",F:"",I:"",N:"蓝岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "岩石桥", I: "8,9",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "蓝岩路", I: "9,10",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,10":{L:"",F:"",I:"",N:"蓝岩池",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "红岩路", I: "7,9",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,10":{L:"",F:"",I:"",N:"岩浆河",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "岩石桥", I: "8,9",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,10":{L:"",F:"",I:"",N:"蓝岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "蓝岩路", I: "9,9",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "蓝岩路", I: "9,11",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,11":{L:"",F:"",I:"",N:"蓝岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "蓝岩路", I: "9,10",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "熔岩路", I: "9,12",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,12":{L:"",F:"",I:"",N:"熔岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "熔岩路", I: "7,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,12":{L:"",F:"",I:"",N:"熔岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "熔岩路", I: "6,12",X:0,Y:0},Y:Mapfx{F:"",N: "熔岩路", I: "8,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,12":{L:"",F:"",I:"",N:"熔岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "熔岩路", I: "7,12",X:0,Y:0},Y:Mapfx{F:"",N: "熔岩路", I: "9,12",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,12":{L:"",F:"",I:"",N:"熔岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "蓝岩路", I: "9,11",X:0,Y:0},Z:Mapfx{F:"",N: "熔岩路", I: "8,12",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "熔岩路", I: "9,13",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,13":{L:"",F:"",I:"",N:"熔岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "熔岩路", I: "7,13",X:0,Y:0},X:Mapfx{F:"",N: "熔岩路", I: "6,14",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,13":{L:"",F:"",I:"",N:"熔岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "熔岩路", I: "6,13",X:0,Y:0},Y:Mapfx{F:"",N: "熔岩路", I: "8,13",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,13":{L:"",F:"",I:"",N:"熔岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "熔岩路", I: "7,13",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "熔岩路", I: "8,14",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,13":{L:"",F:"",I:"",N:"熔岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "熔岩路", I: "9,12",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "熔岩路", I: "9,14",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,14":{L:"",F:"",I:"",N:"熔岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "熔岩路", I: "6,13",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "熔岩路", I: "6,15",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,14":{L:"",F:"",I:"",N:"熔岩之心",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "熔岩路", I: "8,14",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,14":{L:"",F:"",I:"",N:"熔岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "熔岩路", I: "8,13",X:0,Y:0},Z:Mapfx{F:"",N: "熔岩之心", I: "7,14",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,14":{L:"",F:"",I:"",N:"熔岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "熔岩路", I: "9,13",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "熔岩路", I: "9,15",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"6,15":{L:"",F:"",I:"",N:"熔岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "熔岩路", I: "6,14",X:0,Y:0},Z:Mapfx{F:"",N: "", I: "",X:0,Y:0},Y:Mapfx{F:"",N: "熔岩路", I: "7,15",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"7,15":{L:"",F:"",I:"",N:"熔岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "熔岩路", I: "6,15",X:0,Y:0},Y:Mapfx{F:"",N: "熔岩路", I: "8,15",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"8,15":{L:"",F:"",I:"",N:"熔岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "", I: "",X:0,Y:0},Z:Mapfx{F:"",N: "熔岩路", I: "7,15",X:0,Y:0},Y:Mapfx{F:"",N: "熔岩路", I: "9,15",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
"9,15":{L:"",F:"",I:"",N:"熔岩路",M:"",Ds:0,Dz:0,Dy:0,Dx:0,S:Mapfx{F:"",N: "熔岩路", I: "9,14",X:0,Y:0},Z:Mapfx{F:"",N: "熔岩路", I: "8,15",X:0,Y:0},Y:Mapfx{F:"",N: "", I: "",X:0,Y:0},X:Mapfx{F:"",N: "", I: "",X:0,Y:0},Npc:[]Npc{},Fx:[]Mapfx{},Jq:map[int]bool{},Mp:[]Idsl{}},
},
//野猪林
"yezhulin": {
},
}