<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <script src="https://unpkg.com/pinyin-pro@3.18.2/dist/index.js"></script>
    <script>
        let dataSource={
"普通攻击": {N: "普通攻击",M: "",L:"物理攻击", Xs: 2, Jl: 100, Fw: 50, Sl: 1, Lq: 1, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"大刀砍术": {N: "大刀砍术",M: "",L:"物理攻击", Xs: 66, Jl: 200, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"雁翎刀法": {N: "雁翎刀法",M: "",L:"物理攻击", Xs: 66, Jl: 300, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"乱棍": {N: "乱棍",M: "",L:"物理攻击", Xs: 40, Jl: 200, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "棍", Fk: "", Xg: 0.00, Sc: 0},
"无声刀法": {N: "无声刀法",M: "",L:"物理攻击", Xs: 30, Jl: 300, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"玉心剑法": {N: "玉心剑法",M: "",L:"物理攻击", Xs: 71, Jl: 200, Fw: 50, Sl: 3, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"打虎棍法": {N: "打虎棍法",M: "",L:"物理攻击", Xs: 70, Jl: 300, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "棍", Fk: "", Xg: 0.00, Sc: 0},
"霸王剑法": {N: "霸王剑法",M: "",L:"物理攻击", Xs: 72, Jl: 400, Fw: 50, Sl: 5, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"萧萧落木刀法": {N: "萧萧落木刀法",M: "",L:"物理攻击", Xs: 96, Jl: 400, Fw: 50, Sl: 5, Lq: 2, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"大力金刚棍法": {N: "大力金刚棍法",M: "",L:"物理攻击", Xs: 84, Jl: 300, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "棍", Fk: "", Xg: 0.00, Sc: 0},
"柴家枪法": {N: "柴家枪法",M: "",L:"物理攻击", Xs: 76, Jl: 400, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "枪", Fk: "", Xg: 0.00, Sc: 0},
"吹云破风枪法": {N: "吹云破风枪法",M: "",L:"物理攻击", Xs: 76, Jl: 300, Fw: 50, Sl: 3, Lq: 2, T: 0, Bq: "枪", Fk: "", Xg: 0.00, Sc: 0},
"赤目刀法": {N: "赤目刀法",M: "",L:"物理攻击", Xs: 84, Jl: 300, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"弱水刀法": {N: "弱水刀法",M: "",L:"物理攻击", Xs: 45, Jl: 300, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"杨家五合枪": {N: "杨家五合枪",M: "",L:"物理攻击", Xs: 45, Jl: 400, Fw: 50, Sl: 3, Lq: 2, T: 0, Bq: "枪", Fk: "", Xg: 0.00, Sc: 0},
"茅山道剑": {N: "茅山道剑",M: "",L:"物理攻击", Xs: 42, Jl: 200, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"天机剑法": {N: "天机剑法",M: "",L:"物理攻击", Xs: 45, Jl: 300, Fw: 50, Sl: 4, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"醉刀": {N: "醉刀",M: "",L:"物理攻击", Xs: 75, Jl: 400, Fw: 50, Sl: 1, Lq: 2, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"大力板斧": {N: "大力板斧",M: "",L:"物理攻击", Xs: 75, Jl: 200, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "斧", Fk: "", Xg: 0.00, Sc: 0},
"祝福剑法": {N: "祝福剑法",M: "",L:"物理攻击", Xs: 80, Jl: 200, Fw: 50, Sl: 5, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"祝福刀法": {N: "祝福刀法",M: "",L:"物理攻击", Xs: 80, Jl: 200, Fw: 50, Sl: 5, Lq: 2, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"快攻": {N: "快攻",M: "",L:"物理攻击", Xs: 30, Jl: 100, Fw: 50, Sl: 1, Lq: 2, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"大斧砍术": {N: "大斧砍术",M: "",L:"物理攻击", Xs: 10, Jl: 100, Fw: 50, Sl: 1, Lq: 2, T: 0, Bq: "斧", Fk: "", Xg: 0.00, Sc: 0},
"刺枪法": {N: "刺枪法",M: "",L:"物理攻击", Xs: 68, Jl: 300, Fw: 50, Sl: 3, Lq: 2, T: 0, Bq: "枪", Fk: "", Xg: 0.00, Sc: 0},
"精砍刀法": {N: "精砍刀法",M: "",L:"物理攻击", Xs: 30, Jl: 300, Fw: 50, Sl: 1, Lq: 2, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"精刺剑法": {N: "精刺剑法",M: "",L:"物理攻击", Xs: 72, Jl: 300, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"素女剑法": {N: "素女剑法",M: "",L:"物理攻击", Xs: 71, Jl: 200, Fw: 50, Sl: 1, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"大力棍法": {N: "大力棍法",M: "",L:"物理攻击", Xs: 84, Jl: 200, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "棍", Fk: "", Xg: 0.00, Sc: 0},
"大力刀法": {N: "大力刀法",M: "",L:"物理攻击", Xs: 84, Jl: 100, Fw: 50, Sl: 1, Lq: 2, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"冲锋枪法": {N: "冲锋枪法",M: "",L:"物理攻击", Xs: 76, Jl: 400, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "枪", Fk: "", Xg: 0.00, Sc: 0},
"大力剑法": {N: "大力剑法",M: "",L:"物理攻击", Xs: 73, Jl: 300, Fw: 50, Sl: 3, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"尽力一击": {N: "尽力一击",M: "",L:"物理攻击", Xs: 84, Jl: 400, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"铁甲枪": {N: "铁甲枪",M: "",L:"物理攻击", Xs: 45, Jl: 400, Fw: 50, Sl: 3, Lq: 2, T: 0, Bq: "枪", Fk: "", Xg: 0.00, Sc: 0},
"木偶剑法": {N: "木偶剑法",M: "",L:"物理攻击", Xs: 42, Jl: 300, Fw: 50, Sl: 5, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"水中剑法": {N: "水中剑法",M: "",L:"物理攻击", Xs: 45, Jl: 300, Fw: 50, Sl: 4, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"神行棍法": {N: "神行棍法",M: "",L:"物理攻击", Xs: 45, Jl: 200, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "棍", Fk: "", Xg: 0.00, Sc: 0},
"长臂刀": {N: "长臂刀",M: "",L:"物理攻击", Xs: 75, Jl: 500, Fw: 50, Sl: 5, Lq: 2, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"大力斧": {N: "大力斧",M: "",L:"物理攻击", Xs: 56, Jl: 100, Fw: 50, Sl: 1, Lq: 2, T: 0, Bq: "斧", Fk: "", Xg: 0.00, Sc: 0},
"虎奔锤法": {N: "虎奔锤法",M: "",L:"物理攻击", Xs: 75, Jl: 300, Fw: 50, Sl: 4, Lq: 2, T: 0, Bq: "锤", Fk: "", Xg: 0.00, Sc: 0},
"虎怒剑法": {N: "虎怒剑法",M: "",L:"物理攻击", Xs: 75, Jl: 300, Fw: 50, Sl: 4, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"虎扑刀法": {N: "虎扑刀法",M: "",L:"物理攻击", Xs: 75, Jl: 300, Fw: 50, Sl: 4, Lq: 2, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"虎冲斧法": {N: "虎冲斧法",M: "",L:"物理攻击", Xs: 75, Jl: 300, Fw: 50, Sl: 4, Lq: 2, T: 0, Bq: "斧", Fk: "", Xg: 0.00, Sc: 0},
"虎吼枪法": {N: "虎吼枪法",M: "",L:"物理攻击", Xs: 75, Jl: 300, Fw: 50, Sl: 4, Lq: 2, T: 0, Bq: "枪", Fk: "", Xg: 0.00, Sc: 0},
"乱剑": {N: "乱剑",M: "",L:"物理攻击", Xs: 50, Jl: 500, Fw: 50, Sl: 5, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"狂暴棍": {N: "狂暴棍",M: "",L:"物理攻击", Xs: 60, Jl: 200, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "棍", Fk: "", Xg: 0.00, Sc: 0},
"军斧": {N: "军斧",M: "",L:"物理攻击", Xs: 60, Jl: 200, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "斧", Fk: "", Xg: 0.00, Sc: 0},
"重甲枪法": {N: "重甲枪法",M: "",L:"物理攻击", Xs: 87, Jl: 300, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "枪", Fk: "", Xg: 0.00, Sc: 0},
"飞影剑法": {N: "飞影剑法",M: "",L:"物理攻击", Xs: 30, Jl: 300, Fw: 50, Sl: 3, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"重锤": {N: "重锤",M: "",L:"物理攻击", Xs: 30, Jl: 300, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "锤", Fk: "", Xg: 0.00, Sc: 0},
"神刀法": {N: "神刀法",M: "",L:"物理攻击", Xs: 70, Jl: 200, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"拼命一击": {N: "拼命一击",M: "",L:"物理攻击", Xs: 80, Jl: 100, Fw: 50, Sl: 3, Lq: 2, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"飞刀法": {N: "飞刀法",M: "",L:"物理攻击", Xs: 30, Jl: 100, Fw: 50, Sl: 1, Lq: 2, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"御林剑法": {N: "御林剑法",M: "",L:"物理攻击", Xs: 51, Jl: 300, Fw: 50, Sl: 5, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"羽林刀法": {N: "羽林刀法",M: "",L:"物理攻击", Xs: 54, Jl: 200, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"黄金枪法": {N: "黄金枪法",M: "",L:"物理攻击", Xs: 90, Jl: 300, Fw: 50, Sl: 3, Lq: 2, T: 0, Bq: "枪", Fk: "", Xg: 0.00, Sc: 0},
"飞天神剑": {N: "飞天神剑",M: "",L:"物理攻击", Xs: 180, Jl: 100, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"娘子剑法": {N: "娘子剑法",M: "",L:"物理攻击", Xs: 56, Jl: 200, Fw: 50, Sl: 2, Lq: 2, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"飞仙剑法": {N: "飞仙剑法",M: "",L:"物理攻击", Xs: 80, Jl: 300, Fw: 50, Sl: 5, Lq: 5, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"龙吟剑法": {N: "龙吟剑法",M: "",L:"物理攻击", Xs: 84, Jl: 300, Fw: 50, Sl: 6, Lq: 5, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"金光刺眼锤": {N: "金光刺眼锤",M: "",L:"物理攻击", Xs: 100, Jl: 200, Fw: 50, Sl: 3, Lq: 5, T: 0, Bq: "锤", Fk: "", Xg: 0.00, Sc: 0},
"乱眼棍法": {N: "乱眼棍法",M: "",L:"物理攻击", Xs: 90, Jl: 100, Fw: 50, Sl: 4, Lq: 5, T: 0, Bq: "棍", Fk: "", Xg: 0.00, Sc: 0},
"赤云枪法": {N: "赤云枪法",M: "",L:"物理攻击", Xs: 95, Jl: 400, Fw: 50, Sl: 2, Lq: 52, T: 0, Bq: "枪", Fk: "", Xg: 0.00, Sc: 0},
"横扫千军": {N: "横扫千军",M: "",L:"物理攻击", Xs: 130, Jl: 300, Fw: 50, Sl: 4, Lq: 5, T: 0, Bq: "斧", Fk: "", Xg: 0.00, Sc: 0},
"随浪刀法": {N: "随浪刀法",M: "",L:"物理攻击", Xs: 110, Jl: 400, Fw: 50, Sl: 2, Lq: 5, T: 0, Bq: "刀", Fk: "", Xg: 0.00, Sc: 0},
"独臂剑法": {N: "独臂剑法",M: "",L:"物理攻击", Xs: 90, Jl: 400, Fw: 50, Sl: 1, Lq: 5, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"烈火追击": {N: "烈火追击",M: "",L:"物理攻击", Xs: 80, Jl: 100, Fw: 50, Sl: 1, Lq: 5, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"青龙偃月斩": {N: "青龙偃月斩",M: "",L:"物理攻击", Xs: 208, Jl: 400, Fw: 50, Sl: 4, Lq: 5, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"百步穿杨": {N: "百步穿杨",M: "",L:"物理攻击", Xs: 228, Jl: 300, Fw: 50, Sl: 4, Lq: 5, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"炫光水雷": {N: "炫光水雷",M: "",L:"物理攻击", Xs: 300, Jl: 400, Fw: 50, Sl: 4, Lq: 5, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"战车": {N: "战车",M: "",L:"物理攻击", Xs: 208, Jl: 400, Fw: 50, Sl: 4, Lq: 5, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"阿弥陀佛": {N: "阿弥陀佛",M: "",L:"物理攻击", Xs: 208, Jl: 400, Fw: 50, Sl: 4, Lq: 5, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"替天算命": {N: "替天算命",M: "",L:"物理攻击", Xs: 200, Jl: 400, Fw: 50, Sl: 4, Lq: 5, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"素心剑法": {N: "素心剑法",M: "",L:"物理攻击", Xs: 208, Jl: 400, Fw: 50, Sl: 4, Lq: 5, T: 0, Bq: "剑", Fk: "", Xg: 0.00, Sc: 0},
"战炮": {N: "战炮",M: "",L:"物理攻击", Xs: 200, Jl: 400, Fw: 50, Sl: 4, Lq: 5, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"神鞭": {N: "神鞭",M: "",L:"物理攻击", Xs: 200, Jl: 400, Fw: 50, Sl: 4, Lq: 5, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"双枪": {N: "双枪",M: "",L:"物理攻击", Xs: 200, Jl: 400, Fw: 50, Sl: 4, Lq: 5, T: 0, Bq: "枪", Fk: "", Xg: 0.00, Sc: 0},
"血印手": {N: "血印手",M: "",L:"物理攻击", Xs: 180, Jl: 400, Fw: 50, Sl: 5, Lq: 10, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"降龙十八掌": {N: "降龙十八掌",M: "",L:"物理攻击", Xs: 200, Jl: 500, Fw: 50, Sl: 10, Lq: 20, T: 0, Bq: "任意", Fk: "", Xg: 0.00, Sc: 0},
"打狗棒法": {N: "打狗棒法",M: "",L:"物理攻击", Xs: 180, Jl: 300, Fw: 60, Sl: 5, Lq: 8, T: 0, Bq: "棍", Fk: "", Xg: 0.00, Sc: 0},
"干将剑谱": {N: "干将剑谱",M: "宝宝或养子使用后学会干将剑谱技能，使用后武将其周围形成一片红色剑气，武将最多在5回合攻击力增加4.5倍伤害",L:"辅助技能", Xs: 1, Jl: 100, Fw: 50, Sl: 1, Lq: 300, T: 0, Bq: "任意", Fk: "攻击", Xg: 4.50, Sc: 5},
"莫邪剑谱": {N: "莫邪剑谱",M: "宝宝或养子使用后学会莫邪剑谱技能，使用后武将其周围形成一片黑色雾气，武将在使用该技能后接下来的最多3秒内防御提升6倍",L:"辅助技能", Xs: 1, Jl: 100, Fw: 50, Sl: 1, Lq: 300, T: 0, Bq: "任意", Fk: "防御", Xg: 6.00, Sc: 3},
"战歌礼赞": {N: "战歌礼赞",M: "宝宝使用后学会战歌礼赞技能，使用时能使士兵暴怒",L:"辅助技能", Xs: 1, Jl: 100, Fw: 50, Sl: 3, Lq: 300, T: 0, Bq: "任意", Fk: "攻击", Xg: 2.00, Sc: 5},
"呐喊": {N: "呐喊",M: "",L:"辅助技能", Xs: 1, Jl: 100, Fw: 50, Sl: 2, Lq: 300, T: 0, Bq: "任意", Fk: "攻击", Xg: 2.00, Sc: 8}
}
let dataobj={}
var { pinyin } = pinyinPro;
let subType={"刀": 21,"剑": 22,"棍": 23,"斧": 24,"锤": 25,"枪": 26,"任意":0}
function toCamelCase(input) {
    // 去掉空格并分割成数组
    const words = input.split(' ');
    // 遍历数组，将每个单词的首字母大写（除了第一个单词）
    const formattedWords = words.map((word, index) => {
        if (index === 0) {
            // 第一个单词保持原样
            return word.toLowerCase();
        } else {
            // 其他单词首字母大写
            return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
        }
    });
    // 将数组拼接成字符串并返回
    return formattedWords.join('');
}
for (let i in dataSource) {
    let obj=dataSource[i]
    let arr=[i,obj.Xs,obj.Jl/100,obj.Sl,subType[obj.Bq]]
    if(obj.Lq>30){
        arr.push(obj.Lq)
    }
    dataobj[toCamelCase(pinyin(obj.N, { toneType: 'none' }))]=arr
}
console.log('dataobj',dataobj);
    </script>
</body>
</html>