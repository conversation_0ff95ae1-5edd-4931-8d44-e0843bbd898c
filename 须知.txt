一个加密的例子
```
var CryptoJS = require("crypto-js");
// Encrypt
var ciphertext = CryptoJS.AES.encrypt('my message', 'secret key 123').toString();
// Decrypt
var bytes  = CryptoJS.AES.decrypt(ciphertext, 'secret key 123');
var originalText = bytes.toString(CryptoJS.enc.Utf8);
console.log(originalText); // 输出'my message'
```

缓存有两种 一种30分钟 一种永久

缓存：
哈希值
user+id routerList  存储已点击的路由 30分钟
user+id gpdId  存储用户gpsId
user+id msg  存储消息 1公聊 2私聊 3系统 4帮派
user+id routers     cmd对应的路由(刷新页面自动生成的路由) 30分钟
sids    sid userId 存储用户sid和id的对应关系
mapInfo 地图id   地图信息  npcInfo是怪物信息 users是附近玩家
npcInfo 怪物id   怪物信息 


<a href="gCmd.do\?cmd=[^"]*">  替换 <a href="">


数据库 mysql5.7
redis 3.0

玩家打玩家Pk场是全部，国战和强制pk是打人的上设置几个就上几个，被打的只上被打的那个
如果有一方下线，下线的一方默认败方。
Pk场除外！pk场下线不下线都会存在，也不允许中途退出

攻击/10* 15-19

 tempId+'npc'+npcInfo.id+'user'+userId+'gId'+generalId

 {
    "id": "1-24",
    "desc": "任务描述",
    "condition":{  条件
        "release": "npc或者地图发布：npc-1|map-1",
        "condition":完成条件1:庄园升1级;2:需要物品/怪物;3:升级,
        "monsters": [{
            "name": "怪物名称1",
            "num": 怪物数量
        }], 
        "goods": [{
            "id": 物品id,
            "num": 物品数量
        }]
    },
    "taskReward":{  奖励
        "gold":银两,
        "potential":潜能,
        "food":粮草,
        "wood":木材,
        "stone":石料,
        "iron":生铁,
        "goods": [{
            "id": 物品id,
            "num": 物品数量
        }]
    }
},


装备镶嵌宝石
    物品表
    孔表 关联物品表


镶嵌宝石要消耗20块石头
石头 分 绑定和不绑定的
查询石头数量并判断(比如当前背包拥有石头绑定*12，不绑定*15)
优先使用绑定的
绑定的石头-12 数据库删除这条记录
不绑定的石头-8
流程结束